name: 'release docker debug'

on:
  workflow_run:
    workflows: [ "release base debug" ]
    types:
      - completed
  workflow_dispatch:
    paths-ignore:
      - 'build*'
      - 'release*'
      - 'config/**'
      - 'doc/**'
      - 'README.md'
      - 'pom.xml'
      - 'Dockerfile-v7'
      - 'Dockerfile'
      - 'Dockerfile-base'
      - 'Dockerfile-base-new'
      - 'Dockerfile-native-base'
      - 'Dockerfile-jre'
      - 'scripts/install.sh'
      - '.github/workflows/build-base.yaml'
      - '.github/workflows/build-base-new.yaml'
      - '.github/workflows/build-native-dev.yaml'
      - '.github/workflows/build-native-base.yaml'
      - '.github/workflows/build-dev.yaml'
      - '.github/workflows/build-java.yaml'
      - '.github/workflows/build-v7.yaml'
      - '.github/ISSUE_TEMPLATE/**'

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: debug
      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18.16.x
          cache: npm
          cache-dependency-path: ./web-ui
      - name: Install npm
        working-directory: ./web-ui
        run: |
          npm ci
          ls node_modules/mpegts.js || echo "mpegts.js not found"
          npm ls mpegts.js || npm install mpegts.js
      - name: Build web UI
        working-directory: ./web-ui
        run: npm run build
      - name: setup graalvm
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '17.0.7'
          distribution: 'graalvm' # See 'Options' for all available distributions
          github-token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'maven'
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Extract Spring Boot layers
        working-directory: ./target
        run: java -Djarmode=layertools -jar alist-tvbox-1.0.jar extract
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_NAME_AILG666 }}
          password: ${{ secrets.DOCKERHUB_AILG666_TOKEN }}
      - name: Set APP version
        run: |
          [ -d data ] || mkdir data
          export TZ=Asia/Shanghai
          echo $((($(date +%Y) - 2023) * 366 + $(date +%j | sed 's/^0*//'))).$(date +%H%M) > data/version
          echo "GB.$(date +%y%m%d.%H%M)" > data/GB_version
          cp data/version data/app_version
          cat data/version
      - name: Build host mode docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-host-debug
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_NAME_AILG666 }}/xiaoya-tvbox:debug
          cache-from: type=gha
          cache-to: type=gha,mode=max
