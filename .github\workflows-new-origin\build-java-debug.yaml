name: 'release java debug'

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'pom.xml'

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'maven'
      - name: Resolve dependencies
        run: mvn dependency:resolve
      - name: Clean and install
        run: mvn clean install
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-jre
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/java:debug
          cache-from: type=gha
          cache-to: type=gha,mode=max