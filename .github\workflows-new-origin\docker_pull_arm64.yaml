name: ARM64 Pull and Save Docker Image

on:
  workflow_dispatch:
    inputs:
      docker_images:
        description: '请填写docker镜像名称 多个用英文逗号分开'
        required: true
        default: 'alpine:latest,ubuntu:latest'  # 设置默认的 Docker 镜像列表

jobs:
  pull_and_package:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Pull Docker Images and Package
      run: |
        images="${{ github.event.inputs.docker_images }}"
        IFS=',' read -r -a image_array <<< "$images"
        for image in "${image_array[@]}"; do
          docker pull "${image}" --platform "linux/arm64"
          docker save "${image}" -o "${image//\//_}-arm64.tar"
        done

    - name: Compress the TAR files
      run: tar -czf arm64-images.tar.gz *-arm64.tar

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: docker-images-tar
        path: arm64-images.tar.gz
        retention-days: 1  # 将保留天数设置为 1 天 最多可设置90天

    - name: Clean up intermediate files
      run: |
        rm *-arm64.tar
