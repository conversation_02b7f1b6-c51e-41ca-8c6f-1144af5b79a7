name: 'test'

on:
  workflow_dispatch:
  
jobs:
  test-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Update SHA and sync to repositories
        env:
          REPO_TOKEN: ${{ secrets.REPO_TOKEN }}
          VPS_SSH_KEY: ${{ secrets.VPS_170_SSH_KEY }}
          VPS_HOST: ${{ secrets.VPS_HOST }}
        run: |
          # 获取最新的 Docker 镜像 SHA 并更新本地文件
          sleep 10
          latest_sha=$(curl -s "https://hub.docker.com/v2/repositories/ailg/g-box/tags/hostmode" | grep -oE '[0-9a-f]{64}' | tail -1)
          sed -i '/ailg\/g-box:hostmode/d' ailg_sha_remote.txt
          echo "ailg/g-box:hostmode $latest_sha" >> ailg_sha_remote.txt
          
          # 配置 Git 并提交更改到当前仓库
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"

          
          # 设置正确的远程 URL（使用内置的 GITHUB_TOKEN)
          git remote set-url origin https://x-access-token:${{ github.token }}@github.com/${GITHUB_REPOSITORY}.git
          git add ailg_sha_remote.txt

          git diff --staged --quiet || git commit -m "Update version files and SHA for ailg/g-box:hostmode"


          git pull --rebase origin main

          git push origin


          