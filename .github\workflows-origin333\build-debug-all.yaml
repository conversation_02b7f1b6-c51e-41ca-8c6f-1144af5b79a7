name: 'release docker debug all'

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'pom.xml'

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'maven'
      - name: Build with Maven
        run: mvn -B package --file pom.xml

      - name: Extract Spring Boot layers
        working-directory: ./target
        run: java -Djarmode=layertools -jar alist-tvbox-1.0.jar extract

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18.16.x
          cache: npm
          cache-dependency-path: ./web-ui

      - name: Install npm
        working-directory: ./web-ui
        run: npm ci

      - name: Build web UI
        working-directory: ./web-ui
        run: npm run build

      - name: setup graalvm
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '17.0.7'
          distribution: 'graalvm'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'maven'

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set APP version
        run: |
          [ -d data ] || mkdir data
          export TZ=Asia/Shanghai
          echo $((($(date +%Y) - 2023) * 366 + $(date +%j | sed 's/^0*//'))).$(date +%H%M) > data/version
          echo "GB.$(date +%y%m%d.%H%M)" > data/GB_version
          cp data/version data/app_version
          cat data/version

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-debug-all
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:debug-all

          cache-from: type=gha
          cache-to: type=gha,mode=max
