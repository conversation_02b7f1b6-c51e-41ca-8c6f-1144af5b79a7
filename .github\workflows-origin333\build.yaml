name: 'release docker'

on:
  workflow_run:
    workflows: [ "release base" ]
    types:
      - completed
  workflow_dispatch:
  push:
    branches:
      - master
    paths-ignore:
      - 'build*'
      - 'release*'
      - 'config/**'
      - 'doc/**'
      - 'README.md'
      - 'pom.xml'
      - 'Dockerfile-v7'
      - 'Dockerfile'
      - 'Dockerfile-base'
      - 'Dockerfile-base-new'
      - 'Dockerfile-native-base'
      - 'Dockerfile-jre'
      - 'scripts/install.sh'
      - '.github/workflows/build-base.yaml'
      - '.github/workflows/build-base-new.yaml'
      - '.github/workflows/build-native-dev.yaml'
      - '.github/workflows/build-native-base.yaml'
      - '.github/workflows/build-dev.yaml'
      - '.github/workflows/build-java.yaml'
      - '.github/workflows/build-v7.yaml'
      - '.github/ISSUE_TEMPLATE/**'

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18.16.x
          cache: npm
          cache-dependency-path: ./web-ui
      - name: Install npm
        working-directory: ./web-ui
        run: npm ci
      - name: Build web UI
        working-directory: ./web-ui
        run: npm run build
      - name: setup graalvm
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '17.0.7'
          distribution: 'graalvm' # See 'Options' for all available distributions
          github-token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'maven'
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Extract Spring Boot layers
        working-directory: ./target
        run: java -Djarmode=layertools -jar alist-tvbox-1.0.jar extract
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Set APP version
        run: |
          [ -d data ] || mkdir data
          export TZ=Asia/Shanghai
          echo $((($(date +%Y) - 2023) * 366 + $(date +%j | sed 's/^0*//'))).$(date +%H%M) > data/version
          echo "${{ github.event.head_commit.message }}" >> data/version
          cp data/version data/app_version
          cat data/version
      - name: Build docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-xiaoya
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Build host mode docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-host
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:hostmode
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Build standalone docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/alist-tvbox:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Build native with Maven
        run: |
          export TOOLCHAIN_DIR=/opt/x86_64-linux-musl-native/
          export CC=$TOOLCHAIN_DIR/bin/gcc
          export PATH="$TOOLCHAIN_DIR/bin:$PATH"
          sudo apt update
          sudo apt install build-essential libz-dev zlib1g-dev zlib1g -y
          wget https://more.musl.cc/10.2.1/x86_64-linux-musl/x86_64-linux-musl-native.tgz
          tar xf x86_64-linux-musl-native.tgz -C /opt
          wget https://zlib.net/zlib-1.3.1.tar.gz
          tar xf zlib-1.3.1.tar.gz
          cd zlib-1.3.1
          ./configure --prefix=$TOOLCHAIN_DIR --static
          make
          sudo make install
          cd ..
          mvn -B -Pnative package --file pom.xml
      - name: Build native docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-native
          platforms: linux/amd64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:native
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Build host native docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-native-host
          platforms: linux/amd64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:native-host
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Send version file
        env:
          ssh_host: ${{ secrets.SSH_HOST }}
        if: ${{ env.ssh_host != '' }}
        uses: appleboy/scp-action@v0.1.4
        with:
          host: ${{ secrets.SSH_HOST }}
          port: 22
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          source: "data/app_version,scripts/update_new.sh,scripts/update_xiaoya.sh,scripts/update_hostmode.sh,scripts/update_native.sh,scripts/update_native_host.sh"
          strip_components: 1
          target: /var/www/alist/
      - name: send telegram message
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.CHANNEL_ID }}
          token: ${{ secrets.BOT_TOKEN }}
          message: |
            ${{ github.actor }} created commit:
            Commit message: ${{ github.event.commits[0].message }}

            Repository: ${{ github.repository }}

            See changes: https://github.com/${{ github.repository }}/commit/${{github.sha}}
