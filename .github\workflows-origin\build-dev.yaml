name: 'release dev docker'

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'The git branch to build.'
        required: true
        default: 'dev'
      tag:
        description: 'The docker tag to build.'
        required: true
        default: 'dev'
      publish:
        description: 'Publish the notification.'
        required: true
        type: boolean
        default: true
      release:
        description: 'Release the app.'
        required: true
        type: boolean
        default: false

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ inputs.branch }}
      - name: show info
        run: |
          echo "git branch: ${{ inputs.branch }}"
          echo "docker tag: ${{ inputs.tag }}"
          echo "publish: ${{ inputs.publish }}"
          echo "release: ${{ inputs.release }}"
          echo "HEAD_MESSAGE=$(git log -1 --pretty=%B)" >> $GITHUB_ENV
      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18.16.x
          cache: npm
          cache-dependency-path: ./web-ui
      - name: Install npm
        working-directory: ./web-ui
        run: npm ci
      - name: Build web UI
        working-directory: ./web-ui
        run: npm run build
      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'maven'
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Extract Spring Boot layers
        working-directory: ./target
        run: java -Djarmode=layertools -jar alist-tvbox-1.0.jar extract
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Set APP version
        run: |
          [ -d data ] || mkdir data
          export TZ=Asia/Shanghai
          echo $((($(date +%Y) - 2023) * 366 + $(date +%j | sed 's/^0*//'))).$(date +%H%M)-dev > data/version
          echo ${{ github.event.head_commit.message }} >> data/version
          cp data/version data/app_version
          cat data/version
      - name: Build docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-xiaoya
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:${{ inputs.tag }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Build host mode docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-host
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:hostmode-${{ inputs.tag }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Build standalone docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/alist-tvbox:${{ inputs.tag }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - if: ${{ inputs.publish }}
        name: send telegram message
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.CHANNEL_ID }}
          token: ${{ secrets.BOT_TOKEN }}
          message: |
            一键部署(开发版)： wget https://d.har01d.cn/update_xiaoya.sh -O update_xiaoya.sh && sh ./update_xiaoya.sh -t ${{ inputs.tag }}

            ${{ github.actor }} created commit on ${{ inputs.branch }} branch:
            Commit message: ${{ env.HEAD_MESSAGE }}

            Repository: ${{ github.repository }}:${{ inputs.branch }}
            Branch: ${{ inputs.branch }}
            Docker Image: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:${{ inputs.tag }}

            See changes: https://github.com/${{ github.repository }}/commit/${{github.sha}}
