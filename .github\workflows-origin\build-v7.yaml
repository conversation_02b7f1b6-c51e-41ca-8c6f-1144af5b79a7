name: 'release arm v7'

on:
  workflow_dispatch:

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18.16.x
          cache: npm
          cache-dependency-path: ./web-ui
      - name: Install npm
        working-directory: ./web-ui
        run: npm ci
      - name: Build web UI
        working-directory: ./web-ui
        run: npm run build
      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'maven'
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Extract Spring Boot layers
        working-directory: ./target
        run: java -Djarmode=layertools -jar alist-tvbox-1.0.jar extract
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Set APP version
        run: |
          mkdir data
          export TZ=Asia/Shanghai
          num1=$(date +%Y)
          num2=$(date +%j)
          sum=$((($num1 - 2023) * 366 + $num2))
          echo $sum.$(date +%H%M) > data/version
          cp data/version data/app_version
          cat data/version
      - name: Build docker and push - arm v7
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-v7
          platforms: linux/arm/v7
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:arm-v7
          cache-from: type=gha
          cache-to: type=gha,mode=max
