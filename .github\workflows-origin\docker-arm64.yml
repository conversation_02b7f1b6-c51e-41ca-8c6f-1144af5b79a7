name: Build and Push ARM64 Docker Image

on:
  workflow_dispatch:

jobs:
  docker:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/debug'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ailg
          password: ${{ secrets.DOCKERHUB_AILG_TOKEN }}
      
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
<<<<<<< HEAD
          file: ./Dockerfile_240913
=======
          file: ./Dockerfile_debug
>>>>>>> debug
          platforms: linux/arm64
          push: true
          tags: ailg/g-box:t6 