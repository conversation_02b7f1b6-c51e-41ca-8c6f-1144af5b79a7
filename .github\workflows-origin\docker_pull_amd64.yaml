name: x86-64 Pull and Save Docker Image
on:
  workflow_dispatch:
    inputs:
      docker_images:
        description: '请填写docker镜像名称 多个用英文逗号分开'
        required: true
        default: 'alpine:latest'  # 设置默认的 Docker 镜像列表

jobs:
  pull_and_package:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    # 添加缓存支持
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Cache Docker layers
      uses: actions/cache@v3
      with:
        path: /tmp/.buildx-cache
        key: ${{ runner.os }}-buildx-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-buildx-

    - name: Clean up Docker to free space
      run: |
        docker system prune -a -f
        docker volume prune -f

    - name: Pull Docker Images and Package
      run: |
        images="${{ github.event.inputs.docker_images }}"
        IFS=',' read -r -a image_array <<< "$images"
        
        # 创建临时目录存储镜像
        mkdir -p /tmp/docker-images
        
        for image in "${image_array[@]}"; do
          # 尝试拉取镜像，如果失败则退出
          if ! docker pull "${image}" --platform "linux/amd64"; then
            echo "Error: Failed to pull image ${image}"
            exit 1
          fi
          
          # 保存镜像，使用安全的文件名转换
          safe_name=$(echo "${image}" | sed 's/[^a-zA-Z0-9._-]/_/g')
          if ! docker save "${image}" -o "/tmp/docker-images/${safe_name}-amd64.tar"; then
            echo "Error: Failed to save image ${image}"
            exit 1
          fi
        done

    - name: Compress the TAR files
      run: |
        cd /tmp/docker-images
        if ! tar -czf x86-64-images.tar.gz *-amd64.tar; then
          echo "Error: Failed to compress image files"
          exit 1
        fi
        mv x86-64-images.tar.gz $GITHUB_WORKSPACE/

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: docker-images-tar
        path: x86-64-images.tar.gz
        retention-days: 1  # 将保留天数设置为 1 天 最多可设置90天

    - name: Clean up intermediate files
      if: always()  # 确保清理步骤总是运行
      run: |
        rm -rf /tmp/docker-images
        rm -f x86-64-images.tar.gz
