name: 'release base debug'

on:
  repository_dispatch:
    types: [glist_debug_image_updated]
  workflow_run:
    workflows: [ "release java" ]
    types:
      - completed
  workflow_dispatch:
    branches:
      - main
      - debug

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Display trigger info
        run: |
          if [ "${{ github.event_name }}" = "repository_dispatch" ]; then
            echo "Triggered by glist with image tag: ${{ github.event.client_payload.image_tag }}"
          fi
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: debug
      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'maven'
      - name: Print classpath
        run: mvn dependency:build-classpath -Dmdep.outputFile=classpath.txt
      - name: Display classpath
        run: cat classpath.txt
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Extract Spring Boot layers
        working-directory: ./target
        run: java -Djarmode=layertools -jar alist-tvbox-1.0.jar extract
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_NAME_AILG666 }}
          password: ${{ secrets.DOCKERHUB_AILG666_TOKEN }}
      - name: Build hostmode docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-base-debug
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_NAME_AILG666 }}/alist-base:debug
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: TAG=debug
