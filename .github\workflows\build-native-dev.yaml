name: 'release native dev docker'

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'The git branch to build.'
        required: true
        default: 'dev'

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ inputs.branch }}
      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 18.16.x
          cache: npm
          cache-dependency-path: ./web-ui
      - name: Install npm
        working-directory: ./web-ui
        run: npm ci
      - name: Build web UI
        working-directory: ./web-ui
        run: npm run build
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Set APP version
        run: |
          mkdir data
          export TZ=Asia/Shanghai
          num1=$(date +%Y)
          num2=$(date +%j)
          sum=$((($num1 - 2023) * 366 + $num2))
          echo $sum.$(date +%H%M)-dev > data/version
          echo ${{ github.event.head_commit.message }} >> data/version
          cp data/version data/app_version
          cat data/version
      - name: Build docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-native
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:native-dev
          cache-from: type=gha
          cache-to: type=gha,mode=max
