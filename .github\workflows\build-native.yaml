name: 'release native docker'

on:
  workflow_dispatch:

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: npm
          cache-dependency-path: ./web-ui
      - name: Install npm
        working-directory: ./web-ui
        run: npm ci
      - name: Build web UI
        working-directory: ./web-ui
        run: npm run build
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: setup graalvm
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '17'
          distribution: 'graalvm' # See 'Options' for all available distributions
          native-image-musl: 'true'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'maven'
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Set APP version
        run: |
          export TZ=Asia/Shanghai
          echo $((($(date +%Y) - 2023) * 366 + $(date +%j | sed 's/^0*//'))).$(date +%H%M) > data/version
          echo ${{ github.event.head_commit.message }} >> data/version
          cp data/version data/app_version
          cat data/version
      - name: Build native with Maven
        run: |
          mvn -B -Pnative package --file pom.xml
      - name: Build docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-native
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaoya-tvbox:native
          cache-from: type=gha
          cache-to: type=gha,mode=max
