name: 'release docker test'

on:
  workflow_run:
    workflows: [ "release base test" ]
    types:
      - completed
  workflow_dispatch:
    inputs:
      commit_message:
        description: 'Custom commit message'
        required: false
        default: 'pushed by ailg'
  push:
    branches:
      - main
    paths-ignore:
      - 'build*'
      - 'release*'
      - 'config/**'
      - 'doc/**'
      - 'README.md'
      - 'pom.xml'
      - 'Dockerfile-v7'
      - 'Dockerfile'
      - 'Dockerfile-base'
      - 'Dockerfile-base-new'
      - 'Dockerfile-native-base'
      - 'Dockerfile-jre'
      - 'scripts/install.sh'
      - '.github/workflows/build-base.yaml'
      - '.github/workflows/build-base-new.yaml'
      - '.github/workflows/build-native-dev.yaml'
      - '.github/workflows/build-native-base.yaml'
      - '.github/workflows/build-dev.yaml'
      - '.github/workflows/build-java.yaml'
      - '.github/workflows/build-v7.yaml'
      - '.github/ISSUE_TEMPLATE/**'

jobs:
  release-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: npm
          cache-dependency-path: ./web-ui
      - name: Install npm
        working-directory: ./web-ui
        run: npm ci
      - name: Build web UI
        working-directory: ./web-ui
        run: npm run build
      - name: setup graalvm
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '17'
          distribution: 'graalvm'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'maven'
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Extract Spring Boot layers
        working-directory: ./target
        run: java -Djarmode=layertools -jar alist-tvbox-1.0.jar extract
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub (ailg666)
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_NAME_AILG666 }}
          password: ${{ secrets.DOCKERHUB_AILG666_TOKEN }}
      - name: Set APP version
        run: |
          [ -d data ] || mkdir data
          export TZ=Asia/Shanghai
          DATE_TAG=$(date +%y%m%d)  # 获取日期标签
          echo $((($(date +%Y) - 2023) * 366 + $(date +%j | sed 's/^0*//'))).$(date +%H%M) > data/version
          echo "${{ github.event.head_commit.message }}" >> data/version
          echo "GB.$(date +%y%m%d.%H%M)" > data/GB_version
          cp data/version data/app_version
          echo "DATE_TAG=${DATE_TAG}" >> $GITHUB_ENV  # 导出变量供后续步骤使用
          cat data/version
      - name: Build host mode docker and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile-host-test
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ secrets.DOCKERHUB_NAME_AILG666 }}/xiaoya-tvbox:test
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Wait for Docker Hub
        run: sleep 10
      - name: Login to Docker Hub (ailg)
        uses: docker/login-action@v2
        with:
          username: ailg
          password: ${{ secrets.DOCKERHUB_AILG_TOKEN }}
      - name: Build ailg/g-box hostmode and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile_240913
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ailg/g-box:hostmode
            ailg/g-box:${{ env.DATE_TAG }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Wait for Docker Hub
        run: sleep 20
      - name: Update SHA and sync to repositories
        if: success()
        env:
          REPO_TOKEN: ${{ secrets.REPO_AILG666 }}
          VPS_SSH_KEY: ${{ secrets.VPS_170_SSH_KEY }}
          VPS_HOST: ${{ secrets.VPS_HOST }}
        run: |
          sleep 10
          # 获取并更新多个镜像的 SHA 值
          # 1. ailg/g-box:hostmode
          latest_sha=$(curl -s "https://hub.docker.com/v2/repositories/ailg/g-box/tags/hostmode" | grep -oE '[0-9a-f]{64}' | tail -1)
          sed -i '/ailg\/g-box:hostmode/d' ailg_sha_remote.txt
          echo "ailg/g-box:hostmode $latest_sha" >> ailg_sha_remote.txt
          
          # 2. ailg/alist:latest
          latest_sha=$(curl -s "https://hub.docker.com/v2/repositories/ailg/alist/tags/latest" | grep -oE '[0-9a-f]{64}' | tail -1)
          sed -i '/ailg\/alist:latest/d' ailg_sha_remote.txt
          echo "ailg/alist:latest $latest_sha" >> ailg_sha_remote.txt
          
          # 3. ailg/alist:hostmode
          latest_sha=$(curl -s "https://hub.docker.com/v2/repositories/ailg/alist/tags/hostmode" | grep -oE '[0-9a-f]{64}' | tail -1)
          sed -i '/ailg\/alist:hostmode/d' ailg_sha_remote.txt
          echo "ailg/alist:hostmode $latest_sha" >> ailg_sha_remote.txt
          
          # 4. ddsderek/xiaoya-emd:latest
          latest_sha=$(curl -s "https://hub.docker.com/v2/repositories/ddsderek/xiaoya-emd/tags/latest" | grep -oE '[0-9a-f]{64}' | tail -1)
          sed -i '/ddsderek\/xiaoya-emd:latest/d' ailg_sha_remote.txt
          echo "ddsderek/xiaoya-emd:latest $latest_sha" >> ailg_sha_remote.txt
          
          # 5. ailg/ggbond:latest
          latest_sha=$(curl -s "https://hub.docker.com/v2/repositories/ailg/ggbond/tags/latest" | grep -oE '[0-9a-f]{64}' | tail -1)
          sed -i '/ailg\/ggbond:latest/d' ailg_sha_remote.txt
          echo "ailg/ggbond:latest $latest_sha" >> ailg_sha_remote.txt
          
          # 配置 Git 并提交更改到当前仓库
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"

          # 设置正确的远程 URL（使用内置的 GITHUB_TOKEN)
          git remote set-url origin https://x-access-token:${{ github.token }}@github.com/${GITHUB_REPOSITORY}.git
          
          # 直接添加并强制推送新生成的文件
          git add data/GB_version data/app_version data/version ailg_sha_remote.txt
          git commit -m "Update version files and SHA for ailg/g-box:hostmode"
          git push -f origin main  # 使用 -f 强制推送

          # 推送文件到同账号的 xy 仓库
          # 首先获取文件的当前 SHA
          CURRENT_SHA=$(curl -s -H "Authorization: token $REPO_TOKEN" \
            "https://api.github.com/repos/ailg666/xy/contents/ailg_sha_remote.txt" | \
            grep '"sha":' | cut -d'"' -f4)

          # 然后使用获取到的 SHA 更新文件
          curl -X PUT \
            -H "Authorization: token $REPO_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
              \"message\": \"Update SHA for ailg/g-box:hostmode\",
              \"content\": \"$(base64 -w 0 ailg_sha_remote.txt)\",
              \"sha\": \"$CURRENT_SHA\"
            }" \
            "https://api.github.com/repos/ailg666/xy/contents/ailg_sha_remote.txt"
          echo "File updated in xy repository"

          # 推送 data/version 文件
          VERSION_SHA=$(curl -s -H "Authorization: token $REPO_TOKEN" \
            "https://api.github.com/repos/ailg666/xy/contents/version" | \
            grep '"sha":' | cut -d'"' -f4)
          echo "Version SHA: $VERSION_SHA"

          curl -X PUT \
            -H "Authorization: token $REPO_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                  \"message\": \"Update version file\",
                  \"content\": \"$(base64 -w 0 data/version)\",
                  \"sha\": \"$VERSION_SHA\"
                }" \
            "https://api.github.com/repos/ailg666/xy/contents/version"
          echo "Version file updated in xy repository"

          # 推送 data/app_version 文件
          APP_VERSION_SHA=$(curl -s -H "Authorization: token $REPO_TOKEN" \
            "https://api.github.com/repos/ailg666/xy/contents/app_version" | \
            grep '"sha":' | cut -d'"' -f4)
          echo "App Version SHA: $APP_VERSION_SHA"

          curl -X PUT \
            -H "Authorization: token $REPO_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                  \"message\": \"Update app version file\",
                  \"content\": \"$(base64 -w 0 data/app_version)\",
                  \"sha\": \"$APP_VERSION_SHA\"
                }" \
            "https://api.github.com/repos/ailg666/xy/contents/app_version"
          echo "App version file updated in xy repository"

          # 推送 data/GB_version 文件
          GB_VERSION_SHA=$(curl -s -H "Authorization: token $REPO_TOKEN" \
            "https://api.github.com/repos/ailg666/xy/contents/GB_version" | \
            grep '"sha":' | cut -d'"' -f4)
          echo "App Version SHA: $GB_VERSION_SHA"

          curl -X PUT \
            -H "Authorization: token $REPO_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                  \"message\": \"Update GB version file\",
                  \"content\": \"$(base64 -w 0 data/GB_version)\",
                  \"sha\": \"$GB_VERSION_SHA\"
                }" \
            "https://api.github.com/repos/ailg666/xy/contents/GB_version"
          echo "GB version file updated in xy repository！"

          # 设置 SSH 并推送到远程 VPS
          mkdir -p ~/.ssh
          echo "$VPS_SSH_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H $VPS_HOST >> ~/.ssh/known_hosts
          echo "SSH setup completed"

          # 推送文件到 VPS
          scp ailg_sha_remote.txt root@$VPS_HOST:/var/www/ailg/
          scp data/version root@$VPS_HOST:/var/www/ailg/
          scp data/app_version root@$VPS_HOST:/var/www/ailg/
          scp data/GB_version root@$VPS_HOST:/var/www/ailg/
          echo "Files pushed to VPS"

          # 设置 SSH 并推送到远程 VPS
          mkdir -p ~/.ssh
          echo "$VPS_SSH_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H $VPS_HOST >> ~/.ssh/known_hosts
          echo "SSH setup completed"

          # 推送文件到 VPS
          scp ailg_sha_remote.txt root@$VPS_HOST:/var/www/ailg/
          echo "File pushed to VPS"
