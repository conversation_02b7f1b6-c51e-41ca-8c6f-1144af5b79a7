name: Docker Image CI

on:
  push:
    branches: [ "main" ]
  workflow_dispatch:
    
jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write
  
    steps:
    - name: Check out the repo
      uses: actions/checkout@v4
      
    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_PUBHUB_USER }}
        password: ${{ secrets.DOCKER_PUBHUB_PASS }}
        
    - name: Log in to the Container registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
          
    - name: Build and push Docker Image
      uses: docker/build-push-action@v3
      with:
        context: .
        file: ./Dockerfile
        push: false
        tags: openlistteam/openlist_api_server:latest

    - name: Build and push Docker Image
      uses: docker/build-push-action@v3
      with:
        context: .
        file: ./Dockerfile-Lite
        push: false
        tags: openlistteam/openlist_api_server:alpine

    - name: Push Docker Image to ghcrio
      run:     docker push openlistteam/openlist_api_server:latest
            && docker tag  openlistteam/openlist_api_server:latest ghcr.io/openlistteam/openlist_api_server:latest
            && docker push ghcr.io/openlistteam/openlist_api_server:latest

    - name: Push Docker Image to ghcrio
      run:     docker push openlistteam/openlist_api_server:alpine
            && docker tag  openlistteam/openlist_api_server:alpine ghcr.io/openlistteam/openlist_api_server:alpine
            && docker push ghcr.io/openlistteam/openlist_api_server:alpine