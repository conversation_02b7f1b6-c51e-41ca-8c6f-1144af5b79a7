name: Multi-Arch Docker Images Pull and Update
on:
  workflow_dispatch:
    inputs:
      docker_images:
        description: '请填写docker镜像名称 多个用英文逗号分开'
        required: true
        default: 'ailg/ggbond:latest,ailg/g-box:hostmode,emby/embyserver:********'
      vps_host:
        description: 'VPS主机地址'
        required: true
      vps_user:
        description: 'VPS用户名'
        required: true
      vps_port:
        description: 'SSH端口号'
        required: true
        default: '22'
      remote_dir:
        description: '远程服务器存放目录'
        required: true
        default: '/opt/ailg/images'

jobs:
  pull_and_update:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3
      with:
        platforms: 'arm64,arm'

    - name: Install SSH key
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        known_hosts: ${{ secrets.KNOWN_HOSTS }}

    - name: Clean up Docker to free space
      run: |
        docker system prune -a -f
        docker volume prune -f

    - name: Get remote image hashes
      run: |
        mkdir -p /tmp/docker-images
        ssh -p ${{ github.event.inputs.vps_port }} ${{ github.event.inputs.vps_user }}@${{ github.event.inputs.vps_host }} "cat ${{ github.event.inputs.remote_dir }}/ailg_sha_remote.txt" > /tmp/remote_hashes.txt || echo "" > /tmp/remote_hashes.txt

    - name: Check and pull images
      run: |
        images="${{ github.event.inputs.docker_images }}"
        IFS=',' read -r -a image_array <<< "$images"
        changed_images=()
        declare -A image_hashes
        
        for image in "${image_array[@]}"; do
          echo "Processing image: ${image}"
          
          # 获取镜像的当前哈希值
          current_hash=$(docker manifest inspect ${image} | sha256sum | cut -d' ' -f1)
          image_hashes["${image}"]="${current_hash}"
          
          # 检查远程哈希值
          remote_hash=$(grep "^${image} " /tmp/remote_hashes.txt | cut -d' ' -f2 || echo "")
          
          if [ "${current_hash}" != "${remote_hash}" ]; then
            echo "Image ${image} has changed or is new"
            changed_images+=("${image}")
          fi
        done
        
        if [ ${#changed_images[@]} -eq 0 ]; then
          echo "No images need to be updated"
          exit 0
        fi
        
        # 处理发生变化的镜像
        for image in "${changed_images[@]}"; do
          echo "Pulling image: ${image}"
          safe_name=$(echo "${image}" | sed 's/[^a-zA-Z0-9._-]/_/g')
          
          # AMD64 版本
          docker pull --platform linux/amd64 ${image}
          docker save ${image} -o "/tmp/docker-images/${safe_name}-amd64.tar"
          
          # ARM64 版本
          docker pull --platform linux/arm64 ${image}
          docker save ${image} -o "/tmp/docker-images/${safe_name}-arm64.tar"
          
          # ARMv7 版本
          docker pull --platform linux/arm/v7 ${image} || echo "ARMv7 not available for ${image}"
          if docker inspect ${image} >/dev/null 2>&1; then
            docker save ${image} -o "/tmp/docker-images/${safe_name}-armv7.tar"
          fi
        done
        
        # 创建新的哈希值文件
        > /tmp/new_hashes.txt
        while IFS= read -r line || [ -n "$line" ]; do
          image=$(echo $line | cut -d' ' -f1)
          if [ -n "${image_hashes[$image]}" ]; then
            echo "${image} ${image_hashes[$image]}" >> /tmp/new_hashes.txt
          else
            echo "$line" >> /tmp/new_hashes.txt
          fi
        done < /tmp/remote_hashes.txt
        
        # 添加新镜像的哈希值
        for image in "${!image_hashes[@]}"; do
          if ! grep -q "^${image} " /tmp/new_hashes.txt; then
            echo "${image} ${image_hashes[$image]}" >> /tmp/new_hashes.txt
          fi
        done

    - name: Compress files
      run: |
        cd /tmp/docker-images
        tar -czf docker-images.tar.gz *.tar

    - name: Upload to VPS
      run: |
        # 创建远程目录（如果不存在）
        ssh -p ${{ github.event.inputs.vps_port }} ${{ github.event.inputs.vps_user }}@${{ github.event.inputs.vps_host }} "mkdir -p ${{ github.event.inputs.remote_dir }}"
        
        # 上传文件
        scp -P ${{ github.event.inputs.vps_port }} /tmp/docker-images/docker-images.tar.gz ${{ github.event.inputs.vps_user }}@${{ github.event.inputs.vps_host }}:${{ github.event.inputs.remote_dir }}/
        scp -P ${{ github.event.inputs.vps_port }} /tmp/new_hashes.txt ${{ github.event.inputs.vps_user }}@${{ github.event.inputs.vps_host }}:${{ github.event.inputs.remote_dir }}/ailg_sha_remote.txt
        
        # 解压文件
        ssh -p ${{ github.event.inputs.vps_port }} ${{ github.event.inputs.vps_user }}@${{ github.event.inputs.vps_host }} "cd ${{ github.event.inputs.remote_dir }} && tar -xzf docker-images.tar.gz && rm docker-images.tar.gz"

    - name: Clean up
      if: always()
      run: |
        rm -rf /tmp/docker-images
        rm -f /tmp/remote_hashes.txt /tmp/new_hashes.txt
