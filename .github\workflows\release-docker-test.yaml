name: Release ailg/g-box:hostmode

on:
  push:
    branches:
      - master

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1

    - name: Log in to Docker Hub
      uses: docker/login-action@v1
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_PASSWORD }}

    - name: Build and push Docker images
      env:
        DATE_TAG: ${{ steps.date.outputs.date_tag }}
      uses: docker/build-push-action@v4
      with:
        context: .
        file: Dockerfile_240913
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          ${{ secrets.DOCKERHUB_USERNAME }}/g-box:${{ env.DATE_TAG }}
          ${{ secrets.DOCKERHUB_USERNAME }}/g-box:hostmode
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Update ailg_sha_remote.txt with new image hash
      run: |
        IMAGE_HASH=$(curl -s "https://hub.docker.com/v2/repositories/${{ secrets.DOCKERHUB_USERNAME }}/g-box/tags/hostmode" | grep -oE '[0-9a-f]{64}' | tail -1)
        echo "New image hash: $IMAGE_HASH"
        git config --global user.name "github-actions[bot]"
        git config --global user.email "github-actions[bot]@users.noreply.github.com"
        git checkout master
        sed -i '\#${{ secrets.DOCKERHUB_USERNAME }}/g-box#d' ailg_sha_remote.txt
        echo -e "${{ secrets.DOCKERHUB_USERNAME }}/g-box:hostmode $IMAGE_HASH" >> ailg_sha_remote.txt
        git add ailg_sha_remote.txt
        git commit -m "Update ailg_sha_remote.txt with new image hash"
        git push origin master

    - name: Get current date
      id: date
      run: echo "::set-output name=date_tag::$(date +'%y%m%d')"
