ARG TAG=test

FROM ailg666/java:17 as corretto-jdk
RUN apk add --no-cache p7zip && 7z a -t7z -mx=9 /jre.7z /jre

FROM ailg666/alist:${TAG}

LABEL MAINTAINER="ailg"

ENV JAVA_HOME=/jre
ENV PATH="${JAVA_HOME}/bin:${PATH}"

COPY data/update.sql /
COPY data/countries.json /

COPY --from=corretto-jdk /jre.7z /var/lib

VOLUME /opt/atv/data/

WORKDIR /opt/atv/

COPY index.sh /

COPY base1.7z /var/lib
COPY base2.7z /var/lib
COPY base3.7z /var/lib
