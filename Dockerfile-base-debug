ARG TAG=debug

FROM ailg666/java:debug as corretto-jdk

FROM ailg666/alist:${TAG}

LABEL MAINTAINER="ailg"

ENV JAVA_HOME=/jre
ENV PATH="${J<PERSON><PERSON>_HOME}/bin:${PATH}"

COPY data/update.sql /
COPY data/countries.json /

COPY --from=corretto-jdk /jre $JAVA_HOME

VOLUME /opt/atv/data/

WORKDIR /opt/atv/

COPY scripts/index.sh /

COPY target/dependencies/ ./
COPY target/snapshot-dependencies/ ./
COPY target/spring-boot-loader/ ./
