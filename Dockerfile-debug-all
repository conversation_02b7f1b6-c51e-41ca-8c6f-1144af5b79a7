FROM golang:1.20 as <PERSON><PERSON><PERSON><PERSON>

WORKDIR /app/

COPY atv-cli ./

RUN go build

FROM ailg666/alist-base:debug

LABEL MAINTAINER="ailg"

ENV MEM_OPT="-Xmx512M" ALIST_PORT=5678 INSTALL=hostmode

COPY config/config-host.json /opt/alist/data/config.json

COPY --from=BUILDER /app/atv-cli /

COPY init.sh /
COPY movie.sh /
COPY entrypoint.sh /

COPY target/application/ ./

COPY data/tvbox.zip /
COPY data/base_version /
COPY data/index.share.zip /
COPY data/cat.zip /
COPY data/pg.zip /
COPY data/data.zip /

COPY data/version data/app_version
COPY data/GB_version data/GB_version

EXPOSE 4567 5678

ENTRYPOINT ["/entrypoint.sh"]

CMD ["5233", "--spring.profiles.active=production,xia<PERSON>,host"]
