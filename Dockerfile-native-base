FROM golang:1.20 as <PERSON><PERSON><PERSON><PERSON>

WORKDIR /app/

COPY atv-cli ./

RUN go build

FROM ubuntu:22.04

WORKDIR /app/

COPY --from=BUILDER /app/atv-cli /opt/atv/

COPY scripts/install.sh ./

RUN bash install.sh

ENV LC_ALL=en_US.UTF-8
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US.UTF-8
ENV TOOLCHAIN_DIR=/opt/musl
ENV CC="$TOOLCHAIN_DIR/bin/gcc"
ENV MAVEN_HOME=/opt/maven
ENV JAVA_HOME=/opt/graalvm
ENV GRAALVM_HOME=/opt/graalvm
ENV PATH="${JAVA_HOME}/bin:${MAVEN_HOME}/bin:$TOOLCHAIN_DIR/bin:${PATH}"
