ARG TAG=latest

FROM eclipse-temurin:17-jre-jammy as adoptopenjdk

FROM xiaoyaliu/alist:${TAG}

LABEL MAINTAINER="Har01d"

ENV JAVA_HOME=/jre
ENV PATH="${JAVA_HOME}/bin:${PATH}"

COPY --from=adoptopenjdk /opt/java/openjdk $JAVA_HOME

VOLUME /opt/atv/data/

WORKDIR /opt/atv/

RUN apk add --update-cache gcompat

COPY scripts/index.sh /
COPY init.sh /
COPY entrypoint.sh /

COPY target/dependencies/ ./
COPY target/snapshot-dependencies/ ./
COPY target/spring-boot-loader/ ./
COPY target/application/ ./

COPY data/version data/app_version

EXPOSE 4567 80

ENTRYPOINT ["/entrypoint.sh"]

CMD ["81", "--spring.profiles.active=production,xiaoya"]
