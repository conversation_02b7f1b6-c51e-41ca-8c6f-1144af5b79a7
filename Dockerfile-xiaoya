FROM golang:1.20 as <PERSON><PERSON><PERSON><PERSON>

WORKDIR /app/

COPY atv-cli ./

RUN go build

FROM haroldli/alist-base:latest

LABEL MAINTAINER="Har01d"

ENV MEM_OPT="-Xmx512M" ALIST_PORT=5344 INSTALL=xiaoya

COPY config/config.json /opt/alist/data/config.json

COPY --from=BUILDER /app/atv-cli /

COPY init.sh /
COPY movie.sh /
COPY entrypoint.sh /

COPY target/application/ ./

COPY data/tvbox.zip /
COPY data/index.share.zip /
COPY data/cat.zip /
COPY data/pg.zip /
COPY data/data.zip /
COPY data/base_version /

COPY data/version data/app_version

EXPOSE 4567 80

ENTRYPOINT ["/entrypoint.sh"]

CMD ["81", "--spring.profiles.active=production,xiaoya"]
