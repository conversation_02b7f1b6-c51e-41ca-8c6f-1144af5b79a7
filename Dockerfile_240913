FROM hslr/sun-panel:latest AS builder

FROM ailg666/xiaoya-tvbox:test
RUN mkdir -p /app
COPY --from=builder /app/web /app/web
COPY --from=builder /app/sun-panel /app/sun-panel

EXPOSE 3002
RUN apk add --update \
    logrotate \
    libxml2-dev \
    ca-certificates \
    su-exec \
    tzdata \
	file \
	enca \
 && rm -rf /var/cache/apk/* \
 && chmod +x /app/sun-panel \
 && /app/sun-panel -config-reset

COPY lib_v1.zip /var/lib/
COPY tmp.sh /tmp.sh
RUN chmod +x /tmp.sh && /tmp.sh && rm -f /tmp.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["5233", "--spring.profiles.active=production,xiaoya,host"]
