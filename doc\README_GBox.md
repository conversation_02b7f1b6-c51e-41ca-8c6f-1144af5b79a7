# G-Box 安装使用说明
G-Box是一个基于alist-tvbox项目魔改的应用，为了提升用户体验，做了不少新的功能和优化。它内置了小雅alist同时也内置了pg/真心等主流tvbox源，安装即可使用，支持各类tvbox壳应用（如影视/OK影视/猫影视等等）。
安装G-Box后，你可以利用它快速安装小雅emby/jellyfin，如有阿里三方权益或115会员，千兆满速状态15分钟内装完！并自动安装小雅爬虫同步工具！
### 相较于原版小雅alist和原版alist-tvbox，G-Box主要做了以下优化：
   - 通过G-Box可快速安装小雅emby全家桶，下完即装完，无需解压，完整版最低空间要求只要130g而非原版的220g
   - G-Box提供纯115通道快速安装小雅Emby，115会员不用从阿里转存即可下载安装，绕过阿里网盘空间不足的限制
   - 更换网盘token或cookie不用重启容器，直接生效（需稍等1-2分钟完成资源重载）
   - 应用内可直接扫码获取并自动填写cookie和token，更方便无需来回切换
   - 可挂载自己的115/夸克/uc网盘，并进行索引，载入tvbox美观方便
   - infuse可以使用阿里快传115播放
   - emby的app中可加载和调用第三方播放器，实现快速起播和更好的解码
   - 所有支持的网盘内置了自动删除，并可设置重启自动更新，不用安装那么多额外容器


## 一、安装部署
### Linux环境安装(推荐)
1. 环境准备
   - Docker环境检查与安装
   - SSH连接配置
   - 安装目录规划

2. 安装步骤
   - 一键安装脚本说明
   - Docker管理功能配置
   - 常见问题处理

### Windows环境安装
1. 虚拟化环境准备
   - CPU虚拟化检查与开启
   - Windows功能开启
   - WSL安装配置

2. Docker Desktop安装
   - 下载与安装
   - 基础配置

3. G-Box部署
   - 配置文件准备
   - 安装命令执行
   - 验证安装结果

## 二、功能使用
### 基础配置
1. 账号管理
   - 网盘账号配置(115/阿里云/夸克等)
   - Cookie获取方法
   - 主账号设置说明

2. 资源管理
   - 分享资源添加
   - 资源ID获取方法
   - 资源整理与维护

### 特色功能
1. 自动更新
   - 夸克网盘自动更新
   - 更新时间设置
   - 更新日志查看

2. 海报墙
   - 海报墙模式说明
   - 自定义分类
   - 筛选配置

3. 订阅管理
   - 订阅源配置
   - PG订阅说明
   - 真心订阅说明

### 进阶设置
1. 高级功能
   - WebDAV配置
   - 代理设置
   - 安全设置

2. 数据管理
   - 数据备份
   - 数据恢复
   - 日志管理

## 三、G-Box速装小雅Emby全家桶
1. 套件说明
   - 包含组件
   - 功能特点
   - 系统要求

2. 安装配置
   - 安装步骤
   - 初始设置
   - 测试验证

3. 使用维护
   - 日常使用
   - 更新升级
   - 故障处理

## 附录
- 常见问题(FAQ)
- 更新日志
- 交流社区
- 相关资源 