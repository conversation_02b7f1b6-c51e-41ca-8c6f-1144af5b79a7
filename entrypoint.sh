#!/bin/sh

if [ -f /data/proxy.txt ]; then
  proxy_url=$(head -n1 /data/proxy.txt)
  export HTTP_PROXY=$proxy_url
  export HTTPS_PROXY=$proxy_url
  export no_proxy="*.aliyundrive.com"
fi

chmod a+x /init.sh /index.sh /downloadPg.sh /downloadZx.sh

if [ ! -d /data/log ]; then
  mkdir -p /data/log
fi
ln -sf /data/log /opt/atv/log

mkfifo /tmp/init_pipe
tee -a /opt/atv/log/init.log < /tmp/init_pipe &
init 2>&1 > /tmp/init_pipe

/bin/busybox-extras httpd -p "$1" -h /www
chmod 777 /usr/sbin/nginx && /usr/sbin/nginx
shift

java "$MEM_OPT" -cp BOOT-INF/classes:BOOT-INF/lib/* cn.har01d.alist_tvbox.AListApplication "$@"
