<?xml version="1.0"?>
<Container version="2">
  <Name>g-box</Name>
  <Repository>ailg/g-box:hostmode</Repository>
  <Registry/>
  <Network>host</Network>
  <MyIP/>
  <Shell>bash</Shell>
  <Privileged>false</Privileged>
  <Support>https://ailg.ggbond.org</Support>
  <Project/>
  <Overview>G-BOX&#x662F;&#x5728;alist-tvbox&#x9879;&#x76EE;&#x57FA;&#x7840;&#x4E0A;&#x9B54;&#x6539;&#x7684;&#x5E94;&#x7528;&#xFF0C;&#x5185;&#x7F6E;&#x4E86;&#x8001;G&#x4F18;&#x5316;&#x7684;&#x5C0F;&#x96C5;alist&#xFF0C;&#x53EF;&#x5B9E;&#x73B0;&#x5FEB;&#x901F;&#x5B89;&#x88C5;&#x5C0F;&#x96C5;emby/Jellyfin&#x5168;&#x5BB6;&#x6876;&#xFF0C;&#x540C;&#x65F6;&#x5185;&#x7F6E;&#x5F53;&#x524D;&#x4E3B;&#x6D41;tvbox&#x8BA2;&#x9605;&#x6E90;&#xFF08;&#x5982;pg&#x3001;&#x996D;&#x592A;&#x786C;&#x7B49;&#xFF09;&#xFF0C;&#x5B9E;&#x73B0;&#x4E86;&#x4E00;&#x4E9B;&#x539F;&#x7248;alist-tvbox&#x6682;&#x65F6;&#x6CA1;&#x505A;&#x7684;&#x529F;&#x80FD;&#xFF0C;&#x6BD4;&#x5982;&#x66F4;&#x65B9;&#x4FBF;&#x7684;&#x83B7;&#x53D6;&#x5404;&#x79CD;&#x7F51;&#x76D8;cookie&#xFF0C;&#x5728;Emby&#x5E94;&#x7528;&#x4E2D;&#x5185;&#x7F6E;&#x7B2C;&#x4E09;&#x65B9;&#x64AD;&#x653E;&#x5668;&#x7B49;&#xFF0C;&#x4F60;&#x53EF;&#x4EE5;&#x5F53;&#x4F5C;&#x5C0F;&#x96C5;alist+emby+tvbox&#x7684;&#x878D;&#x5408;&#x589E;&#x5F3A;&#x7248;&#xFF0C;&#x7279;&#x522B;&#x611F;&#x8C22;alist-tvbox&#x9879;&#x76EE;&#x539F;&#x4F5C;&#x8005;haroldli&#xFF0C;&#x9879;&#x76EE;&#x4E3B;&#x9875;&#xFF1A;https://github.com/power721/alist-tvbox</Overview>
  <Category>MediaServer:Video MediaServer:Music MediaServer:Photos</Category>
  <WebUI>http://[IP]:[PORT:4567]/</WebUI>
  <TemplateURL>https://gbox.ggbond.org/gbox.xml</TemplateURL>
  <Icon>https://gbox.ggbond.org/gbox_logo.png</Icon>
  <ExtraParams/>
  <PostArgs/>
  <CPUset/>
  <DateInstalled>1728927587</DateInstalled>
  <DonateText/>
  <DonateLink/>
  <Requires/>
  <Config Name="G-box配置目录" Target="/data" Default="" Mode="rw" Description="Config directory" Type="Path" Display="always" Required="true" Mask="false">/mnt/user/appdata/g-box/</Config>
  <Config Name="启动文件" Target="/www/data" Default="" Mode="rw" Description="小雅alist启动文件目录" Type="Path" Display="always" Required="true" Mask="false">/mnt/user/appdata/g-box/data</Config>
  <Config Name="容器管理" Target="/var/run/docker.sock" Default="" Mode="rw" Description="不开容器管理功能请删除此项" Type="Path" Display="always" Required="false" Mask="false">/var/run/docker.sock</Config>
  <Config Name="Restart Policy" Target="--restart" Default="always" Mode="" Description="" Type="Variable" Display="always" Required="false" Mask="false">always</Config>
</Container> 