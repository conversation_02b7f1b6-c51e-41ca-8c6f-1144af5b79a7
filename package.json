{"name": "cf-worker-api", "scripts": {"dev-js": "tsx watch src/basic.ts", "build-js": "webpack --mode production", "deploy-js": "node dist/bundle.js", "dev-cf": "wrangler dev --config wrangler.encrypt.jsonc", "deploy-cf": "wrangler deploy --minify --config wrangler.encrypt.jsonc", "dev-eo": "edgeone pages dev", "build-eo": "edgeone pages build", "deploy-eo": "edgeone pages deploy ./ -n edgeone-hono-app -t ", "cf-typegen": "wrangler types --env-interface CloudflareBindings"}, "dependencies": {"@cloudflare/workers-types": "^4.20250610.0", "@edgeone/ef-types": "^1.0.5", "@google-cloud/local-auth": "^2.1.0", "@hono/node-server": "^1.15.0", "dotenv": "^17.0.1", "edgeone": "^1.0.27", "googleapis": "^105.0.0", "hono": "^4.8.2", "hono-alibaba-cloud-fc3-adapter": "^1.0.2", "http-server": "^14.1.1", "sweetalert2": "^11.6.13"}, "devDependencies": {"@serverless-devs/s": "^3.1.10", "esbuild": "^0.25.5", "wrangler": "^4.4.0", "tsx": "^4.7.1", "@types/node": "^20.11.17", "typescript": "^5.8.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "ts-loader": "^9.5.1"}}