<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.2</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>cn.har01d</groupId>
    <artifactId>alist-tvbox</artifactId>
    <version>1.0</version>
    <name>alist-tvbox</name>
    <description>AList proxy server for TvBox, support playlist and search.</description>
    <url>https://har01d.cn/</url>

    <scm>
        <url>https://github.com/power721/alist-tvbox</url>
        <connection>scm:git:https://github.com/power721/alist-tvbox.git</connection>
        <developerConnection>scm:git:https://github.com/power721/alist-tvbox</developerConnection>
    </scm>

    <developers>
        <developer>
            <name>Harold</name>
            <email><EMAIL></email>
            <url>https://github.com/power721</url>
        </developer>
    </developers>

    <properties>
        <java.version>17</java.version>
        <jjwt.version>0.11.5</jjwt.version>
        <libc>--libc=musl</libc>
        <lib.static>--static</lib.static>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.4</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.sealedtx</groupId>
            <artifactId>java-youtube-downloader</artifactId>
            <version>3.2.8</version>
    	</dependency>
	<dependency>
            <groupId>com.telegram4j</groupId>
            <artifactId>telegram4j-core</artifactId>
            <version>0.1.0-SNAPSHOT</version>
        </dependency>		
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.4.1</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>jitpack</id>
            <url>https://jitpack.io</url>
        </repository>
        <repository>
            <id>s01.oss.sonatype.org-snapshot</id>
            <url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <properties>
                <libc></libc>
                <lib.static></lib.static>
            </properties>
            <id>platform-arm</id>
            <activation>
                <os>
                    <arch>aarch64</arch>
                </os>
            </activation>
        </profile>
        <profile>
            <id>native</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.hibernate.orm.tooling</groupId>
                        <artifactId>hibernate-enhance-maven-plugin</artifactId>
                        <version>${hibernate.version}</version>
                        <executions>
                            <execution>
                                <id>enhance</id>
                                <goals>
                                    <goal>enhance</goal>
                                </goals>
                                <configuration>
                                    <failOnError>true</failOnError>
                                    <enableLazyInitialization>true</enableLazyInitialization>
                                    <enableDirtyTracking>true</enableDirtyTracking>
                                    <enableAssociationManagement>true</enableAssociationManagement>
                                    <enableExtendedEnhancement>false</enableExtendedEnhancement>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>build-native</id>
                                <goals>
                                    <goal>compile-no-fork</goal>
                                </goals>
                                <phase>package</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <imageName>atv</imageName>
                            <mainClass>cn.har01d.alist_tvbox.AListApplication</mainClass>
                            <buildArgs>
                                <buildArg>
                                    ${lib.static}
                                    ${libc}
                                    --enable-http
                                    --enable-https
                                    -march=compatibility
                                </buildArg>
                            </buildArgs>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
