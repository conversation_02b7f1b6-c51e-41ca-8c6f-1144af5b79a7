:root {
    --bg-color-light: #ffffff;
    --text-color-light: #000000;
    --bg-color-dark: #1e1e2f;
    --text-color-dark: #f0f0f0;
    --accent-color: #00d1b2;
}

[data-theme="light"] body {
    background-color: var(--bg-color-light);
    color: var(--text-color-light);
}

[data-theme="dark"] body {
    background-color: var(--bg-color-dark);
    color: var(--text-color-dark);
}

.form-container {
    max-width: 800px;
    margin: 3rem auto;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
    background: inherit;
    backdrop-filter: blur(10px);
}

label {
    font-weight: 600;
}

.form-control:read-only {
    background-color: #f5f5f5;
    cursor: pointer;
}

.toggle-theme {
    position: fixed;
    top: 1rem;
    right: 1rem;
}

a {
    text-decoration: none;
}

[data-theme="dark"] body {
    background-color: var(--bg-color-dark);
    color: var(--text-color-dark);
}

[data-theme="dark"] .form-control {
    background-color: #2a2a3b;
    color: #f0f0f0;
    border-color: #444;
}

[data-theme="dark"] .form-control:read-only {
    background-color: #2a2a3b;
    color: #ccc;
    border-color: #444;
}

[data-theme="dark"] .form-select {
    background-color: #2a2a3b;
    color: #f0f0f0;
    border-color: #444;
}

[data-theme="dark"] .btn {
    background-color: #00bfa5;
    color: #ffffff;
    border: none;
}

[data-theme="dark"] .btn:hover {
    background-color: #00a58e;
    color: #ffffff;
}

[data-theme="dark"] p {
    color: #ffffff;
}

/* 阿里云盘扫码v2样式 */
.qr-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.qr-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    text-align: center;
}

[data-theme="dark"] .qr-modal-content {
    background-color: #2a2a3b;
    color: #f0f0f0;
}

.qr-code-container {
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

[data-theme="dark"] .qr-code-container {
    background: #1a1a2e;
}

.qr-code-img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
}

.qr-status {
    margin: 15px 0;
    padding: 10px;
    border-radius: 5px;
    font-weight: 500;
}

.qr-status.waiting {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.qr-status.scaned {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.qr-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.qr-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

[data-theme="dark"] .qr-status.waiting {
    background: #3d3d00;
    color: #ffeb3b;
    border: 1px solid #ffeb3b;
}

[data-theme="dark"] .qr-status.scaned {
    background: #003d4d;
    color: #00bcd4;
    border: 1px solid #00bcd4;
}

[data-theme="dark"] .qr-status.success {
    background: #1b5e20;
    color: #4caf50;
    border: 1px solid #4caf50;
}

[data-theme="dark"] .qr-status.error {
    background: #5d1a1a;
    color: #f44336;
    border: 1px solid #f44336;
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-btn:hover {
    color: black;
}

[data-theme="dark"] .close-btn:hover {
    color: white;
}

/* Google 同意框 */
.google-consent-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.google-consent-modal-content {
    background-color: #ffffff;
    margin: 8% auto;
    padding: 30px;
    border-radius: 14px;
    width: 90%;
    max-width: 500px;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border: none;
    font-family: 'Arial', 'Segoe UI', sans-serif;
}

.google-consent-modal-content h2 {
    color: #2a2a2a;
    margin-bottom: 15px;
    font-size: 22px;
    font-weight: 500;
}

.google-consent-modal-content p {
    color: #555555;
    margin-bottom: 25px;
    line-height: 1.5;
    font-size: 15px;
}

.google-consent-btn-container {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 20px;
}

.google-consent-accept-btn {
    background-color: #4285f4;
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.google-consent-accept-btn:hover {
    background-color: #3367d6;
}

.google-consent-reject-btn {
    background-color: #f1f1f1;
    color: #333333;
    border: none;
    padding: 10px 18px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.google-consent-reject-btn:hover {
    background-color: #e1e1e1;
}

[data-theme="dark"] .google-consent-modal-content {
    background-color: #2a2a3b;
    color: #f0f0f0;
}


@font-face {
    font-family: 'MapleMono';         /* 自定义字体名称 */
    src: url('fonts.woff2') format('woff2'); /* 本地文件路径 */
    font-weight: normal;                /* 字重 */
    font-style: normal;                 /* 样式 */
    font-display: swap;                 /* 优化加载（可选） */
}


* {
    font-family: "MapleMono", monospace;
    font-optical-sizing: auto;
    font-style: normal;
}