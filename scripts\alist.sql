CREATE TABLE IF NOT EXISTS "x_storages" (`id` integer,`mount_path` text UNIQUE,`order` integer,`driver` text,`cache_expiration` integer,`status` text,`addition` text,`remark` text,`modified` datetime,`disabled` numeric,`order_by` text,`order_direction` text,`extract_folder` text,`web_proxy` numeric,`webdav_policy` text,`down_proxy_url` text,PRIMARY KEY (`id`));
CREATE TABLE IF NOT EXISTS "x_meta" (`id` integer,`path` text UNIQUE,`password` text,`p_sub` numeric,`write` numeric,`w_sub` numeric,`hide` text,`h_sub` numeric,`readme` text,`r_sub` numeric,PRIMARY KEY (`id`));
CREATE TABLE IF NOT EXISTS "x_setting_items" (`key` text,`value` text,`help` text,`type` text,`options` text,`group` integer,`flag` integer,PRIMARY KEY (`key`));
CREATE TABLE IF NOT EXISTS `x_users` (`id` integer,`username` text UNIQUE,`pwd_hash` text,`salt` text,`password` text,`base_path` text,`role` integer,`disabled` numeric,`permission` integer,`otp_secret` text,`sso_id` text,PRIMARY KEY (`id`));
