name: beta

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    name: Build Docker Image
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKER_USERNAME }}/xiaoya-emd
          tags: |
            type=raw,value=beta

      -
        name: Set Up QEMU
        uses: docker/setup-qemu-action@v3

      -
        name: Set Up Buildx
        uses: docker/setup-buildx-action@v3

      -
        name: Login DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      -
        name: Build Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          platforms: |
            linux/amd64
            linux/arm64/v8
            linux/arm/v7
          build-args: |
            BRANCH=beta
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
