name: release

on:
  push:
    branches:
    - master
    tags:
        - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    name: Build Docker Image
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4

      - 
        name: Get version
        id: get_version
        run: |
          echo "VERSION=v${GITHUB_REF#refs/tags/}"
          echo "VERSION=v${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

      - 
        name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKER_USERNAME }}/xiaoya-emd
          tags: |
            type=raw,value=${{ env.VERSION }}
            type=raw,value=latest

      -
        name: Set Up QEMU
        uses: docker/setup-qemu-action@v3

      -
        name: Set Up Buildx
        uses: docker/setup-buildx-action@v3

      -
        name: Login DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      -
        name: Build Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          platforms: |
            linux/amd64
            linux/arm64/v8
            linux/arm/v7
          build-args: |
            BRANCH=main
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
        env:
          DOCKER_BUILD_RECORD_UPLOAD: false

      - 
        name: Docker Hub Description
        uses: peter-evans/dockerhub-description@v4
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
          repository: ${{ secrets.DOCKER_USERNAME }}/xiaoya-emd
          short-description: 高速异步爬虫从 https://emby.xiaoya.pro/ 同步小雅元数据
          readme-filepath: README.md

  Windows-amd64:
    runs-on: windows-latest
    name: Build Windows Binary
    steps:
      - 
        name: Checkout
        uses: actions/checkout@v4

      - 
        name: Init Python 3.13
        uses: actions/setup-python@v5
        with:
            python-version: '3.13'
            cache: 'pip'

      - 
        name: Install Dependent Packages
        run: |
            python -m pip install --upgrade pip
            pip install wheel pyinstaller
            pip install -r requirements.txt
        shell: pwsh

      - 
        name: Pyinstaller
        run: |
            pyinstaller solid.spec
        shell: pwsh

      - 
        name: Upload Windows File
        uses: actions/upload-artifact@v4
        with:
            name: solid-windows-amd64
            path: dist/solid.exe

  release:
    runs-on: ubuntu-latest
    needs: [ Windows-amd64, build ]
    steps:
      - 
        name: Checkout
        uses: actions/checkout@v4

      - 
        name: Get version
        id: get_version
        run: |
          echo "VERSION=${GITHUB_REF#refs/tags/}"
          echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

      - 
        name: Download Artifact
        uses: actions/download-artifact@v4

      - 
        name: Rename
        shell: bash
        run: |
            mv ./solid-windows-amd64/solid.exe ./solid-windows-amd64.exe

      -
        name: Release
        uses: softprops/action-gh-release@v2
        with:
            name: v${{ env.VERSION }}
            tag_name: ${{ env.VERSION }}
            files: |
                ./solid-windows-amd64.exe
