name: style

on:
  workflow_dispatch:
  push:
  pull_request:

jobs:
  sh-checker:
    runs-on: ubuntu-latest
    name: sh-checker
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4

      - 
        name: Run sh-checker
        uses: luizm/action-sh-checker@v0.9.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SHFMT_OPTS: -d -i 4 -sr
        with:
          sh_checker_comment: true