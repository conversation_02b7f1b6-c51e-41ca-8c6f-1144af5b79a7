# 爬虫FAQ

#### Q: 什么是爬虫？
爬虫在自然界代表到处乱爬的虫子。。
在互联网上代表批量浏览，自动下载网站内容的小程序。。

#### Q: 小雅爬虫(xiaoya_emd)是干什么用的？
 安装小雅emby全家桶的用户需要及时同步媒体库的更新，以便及时获取最新的视频资源。在爬虫诞生之前，这个工作是由累死鸟(resilio)来完成的。

#### Q: 爬虫和累死鸟的区别是什么？
 - 累死鸟的工作原理与BT下载，理论上当一个用户的文件夹内的文件更新时，所有的用户都会通过P2P的方式获取同样的更新
 - 爬虫是通过访问小雅的Web服务器，通过HTTP下载的方式获取更新的数据文件

#### Q: 爬虫解决了累死鸟的哪些问题？
 - 累死鸟需要跟不同的在线用户建立若干条网络连接，并且需要长时间保持在线。 爬虫每次只需要运行3-5分钟即可。
 - 累死鸟高度依赖P2P的网络连接情况，当在线用户很少的时候，同步会变得很困难。 爬虫依赖Cloudflare的可靠性，目前有三个镜像网站提供下载服务。
 - 累死鸟会覆盖emby本地的刮削文件，因此emby和累死鸟互相删除对方的数据，导致CPU使用率一直居高不下。爬虫只在第一次运行时删除同步网站上不存在的文件，后续默认不会更新emby的刮削文件，因此同步更新几乎不消耗CPU资源

#### Q: 爬虫和累死鸟需要同时使用吗？
 不需要，极限二选一

#### Q: 那种爬虫部署方式最好？
 - 对于大部分用户，**首推高中生的一键脚本(9-2)**, 一键部署不需要任何人工干预
 - 对于有特殊功能需求的用户，可以手动启动docker，并传入指定参数
 - 对于熟悉Linux和Python的朋友，可以直接clone源代码进行魔改

#### Q: Latest还是beta?
 - 目前latest只提供增量更新功能，不会删除过期的文件
 - Beta版本日趋完善，不久便会并入latest
 - 之后latest会专注bug修复，beta版本会进行新功能的测试

#### Q: 爬虫是否需要翻墙？
默认**不需要！不需要！不需要！**
如果用户所在的地区的运营商屏蔽了Cloudflare(几乎不太可能)，那就需要翻墙

#### Q: 如何查看同步是否成功？
 - 首先看docker的log，默认下载成功和下载失败都会有log
 - 然后到媒体文件夹按日期排序看是否有更新
 - Emby需要开启媒体库实时监控，不然需要手动点击"扫描媒体库文件"

#### Q: 常见的报错信息

|HTTP code|原因|如何修复|
|-|-|-|     
|401/403|文件服务器权限不对|把出错的文件名反馈给小雅|
|503/510|Cloudfare限速 CF默认限制100线程的HTTP请求|尝试加参数 --count <1-100> 从小到大直至错误消失|
|TBD|


#### Q: 如何反馈问题？
在[TG群](https://t.me/xiaoyaliu)@我(Rik)
在[Github](https://github.com/Rik-F5/xiaoya_db)开Issue
请附上问题描述，部署方式和log的截图