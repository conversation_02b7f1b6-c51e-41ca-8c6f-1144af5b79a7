#!/bin/bash
docker_pull() {
    # 尝试直接从 Docker Hub 拉取镜像两次
    for i in {1..2}; do
        if ! curl -s --unix-socket /var/run/docker.sock "http://localhost/images/${1}/json" | jq -e .Id > /dev/null; then
            echo "${1} 镜像不存在，正在进行第 ${i} 次下载..."
            name_img=$(echo "${1}" | awk -F'[:]' '{print $1}')
            tag_img=$(echo "${1}" | awk -F'[:]' '{print $2}')
            curl -s --unix-socket /var/run/docker.sock -X POST "http://localhost/images/create?fromImage=${name_img}&tag=${tag_img}"
        else
            break
        fi
    done

    # 如果直接拉取失败，尝试从代理镜像点拉取
    if ! curl -s --unix-socket /var/run/docker.sock "http://localhost/images/${1}/json" | jq -e .Id > /dev/null; then
        tempfile=$(mktemp)
        mirrors=()
        
        # 从文件或默认列表获取代理镜像点
        if [ -s "/data/docker_mirrors.txt" ]; then
            INFO "正在从 /data/docker_mirrors.txt 文件获取代理点配置……"
            while IFS= read -r line; do
                if [ "$line" != "docker.io" ]; then
                    mirrors+=("$line")
                fi
            done < "/data/docker_mirrors.txt"
        else
            mirrors=("hub.rat.dev" "docker.1ms.run" "dk.nastool.de" "dockerhub.anzu.vip" "docker.adysec.com" "docker.1panel.live")
        fi

        # 从代理镜像点拉取镜像
        for mirror in "${mirrors[@]}"; do
            INFO "正在从 ${mirror} 代理点为您下载镜像……"

            timeout 300 curl -s --unix-socket /var/run/docker.sock -X POST \
                "http://localhost/images/create?fromImage=${mirror}/${name_img}&tag=${tag_img}" | tee "$tempfile"

            # local_sha=$(grep 'Digest: sha256' "$tempfile" | awk -F':' '{print $3}')
            local_sha=$(grep 'Digest: sha256' "$tempfile" | awk -F':' '{gsub(/["}]/, "", $NF); print $NF}')
            echo -e "local_sha:${local_sha}"
            
            if [ -n "${local_sha}" ]; then
                sed -i "\#${1}#d" "/data/ailg_sha.txt"
                echo "${1} ${local_sha}" >> "/data/ailg_sha.txt"
                curl --unix-socket /var/run/docker.sock -X POST "http://localhost/images/${mirror}/${1}/tag?repo=${name_img}&tag=${tag_img}"
                curl -s -X DELETE --unix-socket /var/run/docker.sock "http://localhost/images/${mirror}/${1}?force=true"
                rm "$tempfile"
                return 0
            else
                WARN "${1} 镜像拉取失败，正在进行重试..."
            fi
        done

        # 清理临时文件并输出错误信息
        rm "$tempfile"
        ERROR "已尝试所有镜像代理点拉取失败，程序将退出，请检查网络后再试！"
        WARN "如需重测速选择代理，请手动删除 /data/docker_mirrors.txt 文件后重新运行脚本！"
        return 1
    fi
}

check_diy_mount() {
    # 获取当前容器的挂载
    list=$(curl -s --unix-socket /var/run/docker.sock http://localhost/containers/json?all=true)
    docker_name=$(echo $list | jq -r '.[] | select(.Image == "ailg/g-box:hostmode") | .Names[0] | ltrimstr("/")')
    echo -e "docker_name_1:${docker_name}"
    if [ -z "$docker_name" ]; then
        docker_name=$(echo $list | jq -r '.[] | select(.Names[] | contains("g-box")) | .Names[0] | ltrimstr("/")')
        echo -e "docker_name_2:${docker_name}"
        if [ -z "$docker_name" ]; then
            WARN "未找到g-box容器ID或名字，中止更新！"
            return 1
        fi
    fi
    container_binds_str=$(curl -s --unix-socket /var/run/docker.sock "http://localhost/containers/${docker_name}/json" | jq -r '.HostConfig.Binds[]')
    echo -e "container_binds_str:${container_binds_str}"
    container_binds=()
    container_binds_other=()
    while IFS= read -r bind; do
        if [[ "$bind" == *":/data" ]] || [[ "$bind" == *":/var/run/docker.sock" ]] || [[ "$bind" == *":/www/data" ]]; then
            container_binds+=("$bind")
        else
            container_binds_other+=("$bind")
        fi
    done < <(echo "$container_binds_str" | tr ' ' '\n')

    # 检查自定义挂载文件是否存在且不为空
    MOUNTS_FILE="/data/diy_mount.txt"
    mounts_changed=false
    if [ -s "$MOUNTS_FILE" ]; then
        [ "$(tail -c1 "$MOUNTS_FILE" | od -An -t x1)" != " 0a" ] && echo >> "$MOUNTS_FILE"
        declare -a MOUNTS
        while IFS= read -r line; do
            if [ -z "$line" ]; then
                continue
            fi
            line=$(echo "$line" | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')
            host_dir=$(echo "$line" | awk '{print $1}')
            container_dir=$(echo "$line" | awk '{print $2}')
            mount_str="$host_dir:$container_dir"
            MOUNTS+=("$mount_str")
        done < "$MOUNTS_FILE"

        # 检查自定义挂载目录是否正确
        MOUNTS_STR=$(IFS=,; printf '"%s",' "${MOUNTS[@]}")
        MOUNTS_STR=${MOUNTS_STR%,}
        docker_pull "alpine:latest"
        [ $? -ne 0 ] && exit 1

        response=$(curl -s --unix-socket /var/run/docker.sock \
            -X POST "http://localhost/containers/create" \
            -H "Content-Type: application/json" \
            -d '{
                "Image": "alpine",
                "Cmd": ["echo", "Directories are valid."],
                "HostConfig": {
                "Binds": ['$MOUNTS_STR']
                }
            }')
        container_id=$(echo "$response" | jq -r '.Id')
        response=$(curl -s -o /dev/null -w "%{http_code}" --unix-socket /var/run/docker.sock -X POST "http://localhost/containers/${container_id}/start")
        if [ "$response" -eq 204 ]; then
            # 比较 container_binds_other 和 MOUNTS 数组的内容
            for mount in "${MOUNTS[@]}"; do
                if [[ ! " ${container_binds_other[@]} " =~ " $mount " ]]; then
                    mounts_changed=true
                    break
                fi
            done

            for bind in "${container_binds_other[@]}"; do
                if [[ ! " ${MOUNTS[@]} " =~ " $bind " ]]; then
                    mounts_changed=true
                    break
                fi
            done

            if [ "$mounts_changed" = true ]; then
                # 更新 container_binds 数组
                for mount in "${MOUNTS[@]}"; do
                    container_binds+=("$mount")
                done
            else
            	container_binds=()
	            while IFS= read -r bind; do
	                container_binds+=("$bind")
	            done < <(echo "$container_binds_str" | tr ' ' '\n')
            fi
        else
            ERROR "diy_mount.txt文件存在错误内容，请检查书写格式或宿主机目录是否存在！本次更新将不挂载此文件内容，保持原挂载！"
            # 将原挂载信息添加到 container_binds 中
            container_binds=()
            while IFS= read -r bind; do
                container_binds+=("$bind")
            done < <(echo "$container_binds_str" | tr ' ' '\n')
        fi
        curl -s --unix-socket /var/run/docker.sock -X DELETE "http://localhost/containers/${container_id}?force=true"
    else
        # 如果文件不存在或为空，将 container_binds 的内容设置为原来的挂载点
        container_binds=()
        while IFS= read -r bind; do
            if [[ "$bind" == *":/data" ]] || [[ "$bind" == *":/var/run/docker.sock" ]] || [[ "$bind" == *":/www/data" ]]; then
	            container_binds+=("$bind")
	        fi
        done < <(echo "$container_binds_str" | tr ' ' '\n')
        # 判断 mounts_changed 的值
        if [ ${#container_binds_other[@]} -eq 0 ]; then
            mounts_changed=false
        else
            mounts_changed=true
        fi
    fi

    # 将需要挂载的目录输出到临时文件中
    MOUNTS_OUT=$(IFS=,; printf '"%s",' "${container_binds[@]}")
    MOUNTS_OUT=${MOUNTS_OUT%,}
    
    # 一次性写入所有内容，确保有换行符
    cat > /data/mounts.bind << EOF
${MOUNTS_OUT}
mounts_changed:${mounts_changed}
EOF
    
    echo -e "MOUNTS_OUT:${MOUNTS_OUT}"
    echo -e "mounts_changed:${mounts_changed}"
}

check_diy_mount