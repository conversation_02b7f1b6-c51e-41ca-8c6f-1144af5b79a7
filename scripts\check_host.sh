#!/bin/bash
if ! ping -c 4 api.aliyundrive.com &> /dev/null;then
	sed '/aliyundrive.com/d' /etc/hosts > /tmp/hosts && cat /tmp/hosts > /etc/hosts
	echo -e "***********\tapi.aliyundrive.com\n***********\tuser.aliyundrive.com\n***********\tauth.aliyundrive.com\n***********\tmember.aliyundrive.com\n***********\twww.aliyundrive.com" >> /etc/hosts
	echo -e "***********\tapi.alipan.com\n***********\tuser.alipan.com\n***********\tauth.alipan.com\n***********\topenapi.alipan.com" >> /etc/hosts
fi
if ! ping -c 4 api.open.uc.cn &> /dev/null;then
	sed '/api.open.uc.cn/d' /etc/hosts > /tmp/hosts && cat /tmp/hosts > /etc/hosts
	echo -e "***********\tapi.open.uc.cn" >> /etc/hosts
fi
if ! ping -c 4 uop.quark.cn &> /dev/null;then
	sed '/uop.quark.cn/d' /etc/hosts > /tmp/hosts && cat /tmp/hosts > /etc/hosts
	echo -e "************\tuop.quark.cn" >> /etc/hosts
fi
if ! ping -c 4 api.aliyundrive.com &> /dev/null;then
    echo -e "\033[1;31m$(date +%Y/%m/%d' '%H:%M:%S)\t连接阿里云网络失败，请检查网络后重试！\033[0m"
	sed '/49\.7\.63\.220/d' /etc/hosts > /tmp/hosts && cat /tmp/hosts > /etc/hosts
fi
if ! ping -c 4 api.open.uc.cn &> /dev/null;then
	echo -e "\033[1;31m$(date +%Y/%m/%d' '%H:%M:%S)\t连接UC网络失败，请手动配置hosts，否则可能影响UC cookie的自动获取！\033[0m"
	sed '/59\.82\.23\.63/d' /etc/hosts > /tmp/hosts && cat /tmp/hosts > /etc/hosts
fi
if ! ping -c 4 uop.quark.cn &> /dev/null;then
	echo -e "\033[1;31m$(date +%Y/%m/%d' '%H:%M:%S)\t连接夸克网络失败，请手动配置hosts，否则可能影响夸克cookie的自动获取！\033[0m"
	sed '/59\.82\.31\.215/d' /etc/hosts > /tmp/hosts && cat /tmp/hosts > /etc/hosts
fi