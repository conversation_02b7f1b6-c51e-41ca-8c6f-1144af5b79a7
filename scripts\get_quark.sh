#!/bin/bash

# 设置输入文件路径
input_file="share_info.txt"

# 检查输入文件是否存在
if [ ! -f "$input_file" ]; then
    echo "错误：找不到输入文件 $input_file"
    exit 1
fi

# 从文件读取信息
share_code=$(grep "分享码" "$input_file" | cut -d':' -f2 | tr -d ' ')
share_pwd=$(grep "密码" "$input_file" | cut -d':' -f2 | tr -d ' ')
cookie=$(grep "Cookie" "$input_file" | cut -d':' -f2- | tr -d ' ')

# 设置必要的变量
ua='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/18.3.5.4-b478491100 Safari/537.36 Channel/pckk_other_ch'
referer='https://pan.quark.cn'

echo "步骤1: 验证分享码和密码..."
# 验证分享码和密码
share_token=$(curl -s -X POST "https://drive.quark.cn/1/clouddrive/share/token?pr=ucpro&fr=pc" \
    -H "Accept: application/json, text/plain, */*" \
    -H "User-Agent: $ua" \
    -H "Referer: $referer" \
    -H "Content-Type: application/json" \
    -d "{\"share_id\":\"$share_code\",\"pwd\":\"$share_pwd\"}" | jq -r '.data.token')

if [ "$share_token" == "null" ] || [ -z "$share_token" ]; then
    echo "错误：获取分享token失败"
    exit 1
fi
echo "获取分享token成功: $share_token"

echo "步骤2: 获取文件信息..."
# 获取文件信息
file_info=$(curl -s -X POST "https://drive.quark.cn/1/clouddrive/share/download/file?pr=ucpro&fr=pc" \
    -H "Accept: application/json, text/plain, */*" \
    -H "User-Agent: $ua" \
    -H "Referer: $referer" \
    -H "Content-Type: application/json" \
    -H "x-share-token: $share_token" \
    -d "{\"share_id\":\"$share_code\"}")

fid=$(echo "$file_info" | jq -r '.data.file_info.fid')
if [ "$fid" == "null" ] || [ -z "$fid" ]; then
    echo "错误：获取文件ID失败"
    exit 1
fi
echo "获取文件ID成功: $fid"

echo "步骤3: 保存文件..."
# 保存文件到自己的网盘
save_result=$(curl -s -X POST "https://drive.quark.cn/1/clouddrive/share/save?pr=ucpro&fr=pc" \
    -H "Cookie: $cookie" \
    -H "Accept: application/json, text/plain, */*" \
    -H "User-Agent: $ua" \
    -H "Referer: $referer" \
    -H "Content-Type: application/json" \
    -H "x-share-token: $share_token" \
    -d "{\"fid_list\":[\"$fid\"],\"share_id\":\"$share_code\"}")

saved_fid=$(echo "$save_result" | jq -r '.data.file_list[0].fid')
if [ "$saved_fid" == "null" ] || [ -z "$saved_fid" ]; then
    echo "错误：保存文件失败"
    exit 1
fi
echo "文件保存成功，新的文件ID: $saved_fid"

echo "步骤4: 获取下载链接..."
# 获取下载链接
download_info=$(curl -s -X POST "https://drive.quark.cn/1/clouddrive/file/download?pr=ucpro&fr=pc" \
    -H "Cookie: $cookie" \
    -H "Accept: application/json, text/plain, */*" \
    -H "User-Agent: $ua" \
    -H "Referer: $referer" \
    -H "Content-Type: application/json" \
    -d "{\"fids\":[\"$saved_fid\"]}")

download_url=$(echo "$download_info" | jq -r '.data[0].download_url')
if [ "$download_url" == "null" ] || [ -z "$download_url" ]; then
    echo "错误：获取下载链接失败"
    exit 1
fi

echo "成功获取下载链接:"
echo "$download_url"

# 保存下载链接到文件
echo "$download_url" > download_url.txt
echo "下载链接已保存到 download_url.txt"