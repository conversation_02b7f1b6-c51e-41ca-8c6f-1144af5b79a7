#!/bin/bash

# 颜色定义
Blue="\033[1;34m"
Green="\033[1;32m"
Red="\033[1;31m"
Yellow="\033[1;33m"
NC="\033[0m"

# 提示函数
INFO() { echo -e "${Green}[INFO]${NC} $1"; }
ERROR() { echo -e "${Red}[ERROR]${NC} $1"; }
WARN() { echo -e "${Yellow}[WARN]${NC} $1"; }

# 镜像列表
declare -A images=(
    [1]="ailg/g-box:hostmode"
    [2]="ailg/ggbond:latest"
    [3]="emby/embyserver:*******"
    [4]="emby/embyserver:********"
    [5]="jellyfin/jellyfin:10.9.6"
    [6]="nyanmisaka/jellyfin:latest"
    [7]="ddsderek/xiaoya-emd:latest"
    [8]="cloudnas/clouddrive2:latest"
)

# 选择架构
while true; do
    echo -e "\n${Yellow}请选择导出镜像的架构:${NC}"
    echo "1. amd64 (x86_64)"
    echo "2. arm64 (aarch64)"
    read -erp "请输入选择 (1-2): " arch_choice
    
    case $arch_choice in
        1) arch="amd64"; break ;;
        2) arch="arm64"; break ;;
        *) ERROR "无效的选择，请重新输入" ;;
    esac
done

# 选择保存目录
while true; do
    read -erp $'\n请输入保存目录的完整路径: ' save_dir
    if [ ! -d "$save_dir" ]; then
        read -erp "目录不存在，是否创建? (y/n): " create_dir
        if [[ $create_dir =~ ^[Yy]$ ]]; then
            mkdir -p "$save_dir" || { ERROR "创建目录失败"; exit 1; }
        else
            continue
        fi
    fi
    if [ -w "$save_dir" ]; then
        break
    else
        ERROR "目录无写入权限，请重新选择"
    fi
done

# 检查并更新镜像
check_image() {
    local image=$1
    local tag=$2
    local org_name=$(echo $image | cut -d'/' -f1)
    local img_name=$(echo $image | cut -d'/' -f2 | cut -d':' -f1)
    
    INFO "检查 $image 是否为最新版本..."
    
    # 获取远程镜像SHA
    local remote_sha
    remote_sha=$(curl -s -m 20 "https://hub.docker.com/v2/repositories/${org_name}/${img_name}/tags/${tag}" | grep -oE '[0-9a-f]{64}' | tail -1)
    if [ -z "$remote_sha" ]; then
        ERROR "无法获取远程镜像SHA"
        return 1
    fi
    
    # 如果本地存在镜像，检查SHA
    if docker image inspect "$image" >/dev/null 2>&1; then
        local local_sha
        local_sha=$(docker inspect -f'{{index .RepoDigests 0}}' "$image" | cut -f2 -d:)
        
        if [ "$local_sha" != "$remote_sha" ]; then
            WARN "发现新版本，正在更新..."
            # 备份旧镜像
            docker tag "$image" "${image}-old"
            docker rmi "$image" >/dev/null 2>&1
            
            # 重试下载
            local retry_count=0
            while [ $retry_count -lt 3 ]; do
                if docker pull "$image"; then
                    INFO "更新成功"
                    # 删除旧镜像
                    docker rmi "${image}-old" >/dev/null 2>&1
                    return 0
                fi
                ((retry_count++))
                WARN "下载失败，第 $retry_count 次重试..."
                sleep 3
            done
            
            # 所有重试都失败，回滚到旧版本
            ERROR "更新失败，回滚到旧版本"
            docker tag "${image}-old" "$image"
            docker rmi "${image}-old" >/dev/null 2>&1
            return 1
        else
            INFO "已是最新版本"
        fi
    else
        WARN "本地未找到镜像，正在下载..."
        # 重试下载
        local retry_count=0
        while [ $retry_count -lt 3 ]; do
            if docker pull "$image"; then
                INFO "下载成功"
                return 0
            fi
            ((retry_count++))
            WARN "下载失败，第 $retry_count 次重试..."
            [ $retry_count -lt 3 ] && sleep 3
        done
        [ $retry_count -eq 3 ] && ERROR "下载失败" && return 1
    fi
    return 0
}

# 下载镜像的函数
download_image() {
    local image=$1
    local retry_count=0
    while [ $retry_count -lt 3 ]; do
        if docker pull "$image"; then
            INFO "下载成功"
            return 0
        fi
        ((retry_count++))
        WARN "下载失败，第 $retry_count 次重试..."
        [ $retry_count -lt 3 ] && sleep 3
    done
    [ $retry_count -eq 3 ] && ERROR "下载失败"
    return 1
}

# 导出镜像
echo -e "\n${Yellow}开始导出镜像...${NC}"
for i in "${!images[@]}"; do
    image="${images[$i]}"
    # 生成文件名
    filename=$(echo "$image" | tr '/:' '.' | sed 's/\.$//')".$arch.tar.gz"
    output_path="$save_dir/$filename"
    
    # 检查并清理已存在的旧文件
    if [ -f "$output_path" ]; then
        INFO "清理已存在的旧文件: $filename"
        rm -f "$output_path" || { ERROR "清理旧文件失败"; continue; }
    fi
    
    echo -e "\n${Yellow}正在处理 $image${NC}"
    
    # 根据标签类型检查更新
    if [[ $image == *":latest" ]]; then
        check_image "$image" "latest" || continue
    elif [[ $image == *":hostmode" ]]; then
        check_image "$image" "hostmode" || continue
    elif ! docker image inspect "$image" >/dev/null 2>&1; then
        WARN "镜像不存在，正在下载..."
        download_image "$image" || continue
    fi
    
    echo -e "${Yellow}正在导出 $image${NC}"
    if docker save "$image" | gzip > "$output_path"; then
        INFO "成功导出到: $output_path"
    else
        ERROR "导出 $image 失败"
        # 如果导出失败，清理可能部分写入的文件
        [ -f "$output_path" ] && rm -f "$output_path"
    fi
done

echo -e "\n${Green}所有操作完成!${NC}"
echo "镜像已保存到: $save_dir"