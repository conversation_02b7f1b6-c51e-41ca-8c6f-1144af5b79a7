import argparse
import logging
import sys, os
import urllib.error
import urllib.parse
import urllib.request
from urllib.parse import urljoin, urlparse, unquote, quote
import aiohttp.client_exceptions
from bs4 import BeautifulSoup
from datetime import datetime
import random
import re
import gzip
import hashlib
import uuid
import time
import asyncio
import aiofiles
import aiohttp
from aiohttp import ClientSession, TCPConnector
import aiosqlite
import aiofiles.os as aio_os
import threading
from flask import Flask, jsonify, request
import psutil


logging.basicConfig(
    format="%(asctime)s %(levelname)s %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
    stream=sys.stdout,
)
logger = logging.getLogger("emd")
logging.getLogger("chardet.charsetprober").disabled = True


s_paths_all = [
    quote("测试/"),  # 8192
    quote("json/"),  # 4096
    quote("ISO/"),  # 2048
    quote("115/"),  # 1024
    quote("PikPak/"),  # 512
    quote("动漫/"),  # 256
    quote("每日更新/"),  # 128
    quote("电影/"),  # 64
    quote("电视剧/"),  # 32
    quote("纪录片/"),  # 16
    quote("纪录片（已刮削）/"),  # 8
    quote("综艺/"),  # 4
    quote("音乐/"),  # 2
    quote("📺画质演示测试（4K，8K，HDR，Dolby）/"),  # 1
]

t_paths = [
    quote("115/"),
    quote("每日更新/"),
    quote("纪录片（已刮削）/"),
    quote("音乐/"),
    quote("综艺/"),
]

s_paths = [
    quote("115/"),
    quote("每日更新/"),
    quote("纪录片（已刮削）/"),
    quote("音乐/"),
    quote("综艺/"),
]

s_pool = [
    "https://emby.xiaoya.pro/",
    "https://icyou.eu.org/",
    "https://emby.8.net.co/",
    "https://emby.raydoom.tk/",
    "https://embyxiaoya.laogl.top/",
    "https://emby-data.poxi1221.eu.org/",
    "https://emby-data.bdbd.fun/",
    "https://emby-data.ymschh.top/",
    "https://emby-data.r2s.site/",
    "https://emby-data.neversay.eu.org/",
    "https://emby-data.800686.xyz/",
    "https://emby-data.xn--yetq23gxma.org/",
    "https://emby-data.younv.at/",
    "https://emby.kaiserver.uk/",
    "https://emby-data.ermaokj.cn/",
    "https://emby-data.wwwh.eu.org/",
    "https://emby-data.f1rst.top/",
    "https://emby-data.wx1.us.kg/",
    "https://emby-data.xnn.ee/",
]

s_folder = [".sync"]

s_ext = [".ass", ".srt", ".ssa"]

# CF blocks urllib...

CUSTOM_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
opener = urllib.request.build_opener()
opener.addheaders = [("User-Agent", CUSTOM_USER_AGENT)]
urllib.request.install_opener(opener)


#########################################
# 改进1: 同步状态跟踪
#########################################
class SyncState:
    def __init__(self):
        self.running = False
        self.last_start = None
        self.syncing_path = None
        self.total_files = 0
        self.processed_files = 0
        self.error_files = 0
        self.download_size = 0
        self.total_size = 0
        self.current_speed = 0
        self.eta = None
        self.stage = "准备中"
        self.lock = threading.RLock()
        
    def start(self, path=None):
        with self.lock:
            self.running = True
            self.last_start = time.time()
            self.syncing_path = path
            self.total_files = 0
            self.processed_files = 0
            self.error_files = 0
            self.download_size = 0
            self.total_size = 0
            self.current_speed = 0
            self.eta = None
            self.stage = "准备中"
        
    def stop(self):
        with self.lock:
            self.running = False
            self.stage = "已停止"
        
    def set_stage(self, stage):
        with self.lock:
            self.stage = stage
        
    def update(self, total=None, processed=0, errors=0, size=0, total_size=None):
        with self.lock:
            if total is not None:
                self.total_files = total
            if total_size is not None:
                self.total_size = total_size
            self.processed_files += processed
            self.error_files += errors
            self.download_size += size
            
            # 计算下载速度和预计完成时间
            if self.total_size > 0 and self.download_size > 0:
                elapsed = time.time() - self.last_start
                if elapsed > 0:
                    self.current_speed = self.download_size / elapsed
                    remaining = (self.total_size - self.download_size) / max(1, self.current_speed)
                    self.eta = remaining
        
    def get_progress(self):
        with self.lock:
            if self.total_files == 0:
                return 0
            return min(100, round(self.processed_files / self.total_files * 100, 2))
    
    def get_status(self):
        with self.lock:
            return {
                'running': self.running,
                'progress': self.get_progress(),
                'stage': self.stage,
                'current_path': self.syncing_path,
                'start_time': self.last_start,
                'elapsed': time.time() - self.last_start if self.last_start else 0,
                'processed_files': self.processed_files,
                'total_files': self.total_files,
                'error_files': self.error_files,
                'download_size': self.download_size,
                'total_size': self.total_size,
                'current_speed': self.current_speed,
                'eta': self.eta
            }

    def increment_processed(self, count=1):
        with self.lock:
            self.processed_files += count

    def increment_purged(self, count=1):
        with self.lock:
            self.purged_files = getattr(self, 'purged_files', 0) + count

    def complete(self):
        with self.lock:
            self.running = False
            self.stage = "已完成"

    def error(self, error_msg):
        with self.lock:
            self.running = False
            self.stage = f"错误: {error_msg}"

# 全局同步状态对象
sync_state = SyncState()


#########################################
# 改进2: 带宽限制功能
#########################################
class BandwidthLimiter:
    def __init__(self, limit_mbps=None):
        """
        创建带宽限制器
        limit_mbps: 带宽限制，单位为Mbps，None表示不限制
        """
        self.limit_mbps = limit_mbps
        self.last_time = time.time()
        self.bytes_sent = 0
        self.lock = asyncio.Lock()
    
    async def limit(self, size):
        """限制带宽"""
        if not self.limit_mbps:
            return
        
        async with self.lock:
            self.bytes_sent += size
            current_time = time.time()
            elapsed = current_time - self.last_time
            
            # 计算期望的传输时间
            expected_time = self.bytes_sent / (self.limit_mbps * 125000)  # 转换为字节/秒
            
            # 如果实际用时小于期望时间，则等待
            if elapsed < expected_time:
                await asyncio.sleep(expected_time - elapsed)
            
            # 重置计数
            self.bytes_sent = 0
            self.last_time = time.time()


def generate_group_id(parent_dir, filename):
    """生成唯一组ID"""
    # 使用文件路径和随机UUID生成唯一哈希值
    unique_str = f"{parent_dir}_{filename}_{uuid.uuid4()}"
    return hashlib.md5(unique_str.encode()).hexdigest()[:16]


#########################################
# 改进3: 智能服务器选择
#########################################
async def pick_best_servers(url_list, max_servers=3, top_n=None):
    """选择响应时间最快的几个服务器
    
    Args:
        url_list: 服务器URL列表
        max_servers: 最大服务器数量，默认3
        top_n: 可选，指定返回前n个服务器
    """
    # 使用 top_n 如果提供，否则使用 max_servers
    n = top_n if top_n is not None else max_servers
    
    logger.info("开始测试服务器响应时间...")
    sync_state.set_stage("测试服务器")
    
    server_details = []
    test_tasks = []
    
    async def test_server(url):
        try:
            start_time = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.head(url, timeout=5) as response:
                    if response.status == 200:
                        response_time = time.time() - start_time
                        # 检查是否有Last-Modified头
                        last_modified = None
                        if 'Last-Modified' in response.headers:
                            last_modified = response.headers['Last-Modified']
                        
                        server_details.append({
                            'url': url,
                            'response_time': response_time,
                            'last_modified': last_modified
                        })
                        logger.info(f"服务器 {url} 响应时间: {response_time:.3f}秒")
        except Exception as e:
            logger.info(f"访问服务器 {url} 出错: {e}")
    
    # 并行测试所有服务器
    for url in url_list:
        test_tasks.append(asyncio.create_task(test_server(url)))
    
    await asyncio.gather(*test_tasks)
    
    # 按响应时间排序
    server_details.sort(key=lambda x: x['response_time'])
    
    # 返回最快的几个服务器
    best_servers = [server['url'] for server in server_details[:n]]
    
    if best_servers:
        logger.info(f"选择了 {len(best_servers)} 个最佳服务器")
    else:
        logger.warning("没有找到可用的服务器")
    
    return best_servers


def pick_a_pool_member(url_list):
    # 保留原函数以兼容旧代码，但不再使用
    random.shuffle(url_list)
    for member in url_list:
        try:
            logger.debug("Testing: %s", member)
            response = urllib.request.urlopen(member)
            if response.getcode() == 200:
                content = response.read()
                try:
                    content_decoded = content.decode("utf-8")
                    if "每日更新" in content_decoded:
                        logger.info("Picked: %s", member)
                        return member
                    else:
                        logger.info("Content at %s does not contain '每日更新'", member)
                except UnicodeDecodeError:
                    logger.info("Non-UTF-8 content at %s", member)
        except Exception as e:
            logger.info("Error accessing %s: %s", member, e)
    return None


def current_amount(url, media, paths):
    listfile = os.path.join(media, ".scan.list.gz")
    try:
        res = urllib.request.urlretrieve(url, listfile)
        with gzip.open(listfile) as response:
            pattern = r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2} \/(.*)$"
            hidden_pattern = r"^.*?\/\..*$"
            matching_lines = 0
            for line in response:
                try:
                    line = line.decode(encoding="utf-8").strip()
                    match = re.match(pattern, line)
                    if match:
                        file = match.group(1)
                        if any(file.startswith(unquote(path)) for path in paths):
                            if not re.match(
                                hidden_pattern, file
                            ) and not file.lower().endswith(".txt"):
                                matching_lines += 1
                except:
                    logger.error("Error decoding line: %s", line)
        return matching_lines
    except urllib.error.URLError as e:
        print("Error:", e)
        return -1


async def fetch_html(url, session, **kwargs) -> str:
    semaphore = kwargs["semaphore"]
    async with semaphore:
        async with session.request(method="GET", url=url) as resp:
            logger.debug(
                "Request Headers for [%s]: [%s]",
                unquote(url),
                resp.request_info.headers,
            )
            resp.raise_for_status()
            logger.debug("Response Headers for [%s]: [%s]", unquote(url), resp.headers)
            logger.debug("Got response [%s] for URL: %s", resp.status, unquote(url))
            try:
                text = await resp.text()
                return text
            except UnicodeDecodeError:
                logger.error("Non-UTF-8 content at %s", unquote(url))
                return None


async def parse(url, session, max_retries=3, **kwargs) -> tuple:
    global html
    retries = 0
    files = []
    directories = []
    
    while True:
        if retries < max_retries:
            try:
                html = await fetch_html(url=url, session=session, **kwargs)
                if html is None:
                    logger.debug("Failed to fetch HTML content for URL: %s", unquote(url))
                    return files, directories
                break
            except aiohttp.ClientResponseError as e:
                logger.error(
                    "aiohttp ClientResponseError for %s [%s]: %s. Retrying (%d/%d)...",
                    unquote(url),
                    getattr(e, "status", None),
                    getattr(e, "message", None),
                    retries + 1,
                    max_retries,
                )
                retries += 1
            except (
                aiohttp.ClientError,
                aiohttp.http_exceptions.HttpProcessingError,
                aiohttp.ClientPayloadError,
            ) as e:
                logger.error(
                    "aiohttp exception for %s [%s]: %s",
                    unquote(url),
                    getattr(e, "status", None),
                    getattr(e, "message", None),
                )
                return files, directories
            except Exception as e:
                logger.exception(
                    "Non-aiohttp exception occurred: %s", getattr(e, "__dict__", {})
                )
                return files, directories
        else:
            logger.error("Max retries reached for %s. Request failed.", unquote(url))
            return files, directories

    soup = BeautifulSoup(html, "html.parser")
    for link in soup.find_all("a"):
        href = link.get("href")
        if href.__contains__("/cdn-cgi/l/email-protection"):
            continue
        if (
            href != "../"
            and not href.endswith("/")
            and not href.endswith("txt")
            and href != "scan.list"
        ):
            try:
                abslink = urljoin(url, href)
                filename = unquote(urlparse(abslink).path)
                timestamp_str = link.next_sibling.strip().split()[0:2]
                timestamp = datetime.strptime(" ".join(timestamp_str), "%d-%b-%Y %H:%M")
                timestamp_unix = int(timestamp.timestamp())
                filesize = link.next_sibling.strip().split()[2]
                files.append((abslink, filename, timestamp_unix, filesize))
            except Exception as e:
                logger.exception("Error processing link: %s", e)
                continue
        elif href != "../" and not href.lower().endswith(".txt"):
            directories.append(urljoin(url, href))
    
    return files, directories


async def need_download(file, **kwargs):
    url, filename, timestamp, filesize = file
    file_path = os.path.join(kwargs["media"], filename.lstrip("/"))
    if not os.path.exists(file_path):
        logger.debug("%s doesn't exists", file_path)
        return True
    elif file_path.endswith(".nfo"):
        if not kwargs["nfo"]:
            return False
    current_filesize = os.path.getsize(file_path)
    current_timestamp = os.path.getmtime(file_path)
    logger.debug("%s has timestamp: %s and size: %s", filename, timestamp, filesize)
    if int(filesize) == int(current_filesize) and int(timestamp) <= int(
        current_timestamp
    ):
        return False
    logger.debug(
        "%s has current_timestamp: %s and current_size: %s",
        filename,
        current_timestamp,
        current_filesize,
    )
    return True


#########################################
# 改进4: 改进的下载函数，支持带宽限制和状态更新
#########################################
async def download(file, session, **kwargs):
    url, filename, timestamp, filesize = file
    semaphore = kwargs["semaphore"]
    limiter = kwargs.get("limiter")
    
    async with semaphore:
        try:
            async with session.get(url) as response:
                if response.status == 200:
                    file_path = os.path.join(kwargs["media"], filename.lstrip("/"))
                    os.umask(0)
                    os.makedirs(os.path.dirname(file_path), mode=0o777, exist_ok=True)
                    
                    # 添加下载进度跟踪
                    total_size = int(response.headers.get("content-length", 0))
                    downloaded = 0
                    start_time = time.time()
                    
                    async with aiofiles.open(file_path, "wb") as f:
                        logger.debug("Starting to write file: %s", filename)
                        # 分块读取并应用带宽限制
                        chunk_size = 64 * 1024  # 64KB chunks
                        async for chunk in response.content.iter_chunked(chunk_size):
                            await f.write(chunk)
                            downloaded += len(chunk)
                            
                            # 更新同步状态
                            sync_state.update(processed=0, size=len(chunk))
                            
                            # 应用带宽限制
                            if limiter:
                                await limiter.limit(len(chunk))
                                
                        logger.debug("Finish to write file: %s", filename)
                    
                    # 设置文件时间戳和权限
                    os.chmod(file_path, 0o777)
                    os.utime(file_path, (timestamp, timestamp))
                    
                    # 更新处理文件数
                    sync_state.update(processed=1)
                    
                    logger.info("Downloaded: %s", filename)
                else:
                    # 更新错误计数
                    sync_state.update(errors=1)
                    logger.error(
                        "Failed to download: %s [Response code: %s]",
                        filename,
                        response.status,
                    )
        except Exception as e:
            # 更新错误计数
            sync_state.update(errors=1)
            logger.exception("Download exception: %s", e)


async def download_files(files, session, **kwargs):
    # 更新同步状态
    sync_state.update(total=len(files))
    sync_state.set_stage("下载文件")
    
    # 计算文件总大小
    total_size = sum(int(file[3]) for file in files)
    sync_state.update(total_size=total_size)
    
    # 使用动态并发控制
    max_tasks = determine_optimal_concurrency(kwargs.get("max_concurrency", 100))
    logger.info(f"使用最佳并发数: {max_tasks}")
    
    download_tasks = set()
    for file in files:
        if await need_download(file, **kwargs) is True:
            while len(download_tasks) >= max_tasks:
                # 等待一些任务完成以保持并发数
                done, pending = await asyncio.wait(
                    download_tasks, return_when=asyncio.FIRST_COMPLETED
                )
                download_tasks = pending
            
            task = asyncio.create_task(download(file, session, **kwargs))
            task.add_done_callback(lambda t: download_tasks.discard(t) if t in download_tasks else None)
            download_tasks.add(task)
    
    # 等待所有下载任务完成
    if download_tasks:
        await asyncio.gather(*download_tasks)


#########################################
# 改进5: 动态并发控制
#########################################
def determine_optimal_concurrency(max_user_specified):
    """根据系统资源动态确定最佳并发数"""
    # 获取系统信息
    cpu_count = psutil.cpu_count(logical=True)
    memory = psutil.virtual_memory()
    
    # 基于CPU和内存计算建议值
    cpu_based = cpu_count * 4  # 每个CPU核心4个并发任务
    memory_based = int(memory.available / (128 * 1024 * 1024))  # 每个任务预留128MB内存
    
    # 取较小值，但至少保证4个并发，最多不超过用户指定的最大值
    optimal = max(4, min(cpu_based, memory_based, max_user_specified))
    
    return optimal


async def download_files_by_list(files_to_download, session, url_base, **kwargs):
    """根据文件列表下载文件"""
    sync_state.update(total=len(files_to_download))
    sync_state.set_stage("下载文件列表")
    
    # 计算文件总大小
    total_size = sum(int(file[2]) for file in files_to_download)
    sync_state.update(total_size=total_size)
    
    # 动态调整并发数
    max_tasks = determine_optimal_concurrency(kwargs.get("max_concurrency", 100))
    logger.info(f"使用最佳并发数: {max_tasks}")
    
    download_tasks = set()
    semaphore = kwargs["semaphore"]
    media = kwargs["media"]
    limiter = kwargs.get("limiter")
    
    for file_info in files_to_download:
        filename, timestamp, filesize = file_info
        
        # 构造URL
        url_path = filename
        if not url_path.startswith('/'):
            url_path = '/' + url_path
        url_path = url_path.replace(' ', '%20')
        url = urljoin(url_base, url_path)
        
        file_tuple = (url, filename, timestamp, filesize)
        
        # 控制并发数
        while len(download_tasks) >= max_tasks:
            done, pending = await asyncio.wait(
                download_tasks, return_when=asyncio.FIRST_COMPLETED
            )
            download_tasks = pending
        
        # 创建下载任务
        task = asyncio.create_task(download(
            file_tuple, session, semaphore=semaphore, limiter=limiter, media=media, **kwargs
        ))
        task.add_done_callback(lambda t: download_tasks.discard(t) if t in download_tasks else None)
        download_tasks.add(task)
    
    # 等待所有任务完成
    if download_tasks:
        await asyncio.gather(*download_tasks)


async def create_table(conn):
    try:
        # 创建主文件表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS files (
                filename TEXT PRIMARY KEY,
                timestamp INTEGER NULL,
                filesize INTEGER NULL,
                group_id TEXT,
                parent_dir TEXT)
        """)
        
        # 创建strm文件表用于快速查询
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS strm_files (
                filename TEXT PRIMARY KEY,
                timestamp INTEGER NULL,
                filesize INTEGER NULL,
                group_id TEXT)
        """)
        
        # 创建tvshow文件表用于快速查询
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS tvshow_files (
                filename TEXT PRIMARY KEY,
                timestamp INTEGER NULL,
                filesize INTEGER NULL,
                group_id TEXT)
        """)
    except Exception as e:
        logger.error("Unable to create DB due to %s", e)
        sys.exit(1)


async def insert_files(conn, all_items, strm_items, tvshow_items):
    """一次性插入所有文件记录"""
    if all_items:
        await conn.executemany(
            "INSERT OR REPLACE INTO files VALUES (?, ?, ?, ?, ?)", 
            all_items
        )
    
    if strm_items:
        await conn.executemany(
            "INSERT OR REPLACE INTO strm_files VALUES (?, ?, ?, ?)", 
            strm_items
        )
    
    if tvshow_items:
        await conn.executemany(
            "INSERT OR REPLACE INTO tvshow_files VALUES (?, ?, ?, ?)", 
            tvshow_items
        )
    
    await conn.commit()


def process_folder_with_grouping(folder, media):
    """扫描目录并同时完成分组处理"""
    # 按目录收集文件
    directories = {}
    
    for root, dirs, files in os.walk(folder, topdown=False):
        dirs[:] = [d for d in dirs if d not in s_folder]
        rel_path = os.path.relpath(root, media)
        parent_dir = rel_path if rel_path != '.' else ''
        
        if parent_dir not in directories:
            directories[parent_dir] = []
            
        for file in files:
            if not file.startswith(".") and not file.lower().endswith(tuple(s_ext)):
                file_path = os.path.join(root, file)
                try:
                    file_path.encode("utf-8")  # 检查编码
                    rel_file_path = os.path.join(parent_dir, file) if parent_dir else file
                    
                    stat = os.stat(file_path)
                    file_info = (rel_file_path, int(stat.st_mtime), stat.st_size)
                    directories[parent_dir].append(file_info)
                except UnicodeEncodeError:
                    logging.error("Filename is not UTF-8 encoded: %s", file_path)
    
    # 处理各个目录的分组
    all_items = []
    strm_items = []
    tvshow_items = []
    
    for parent_dir, dir_files in directories.items():
        # 筛选特殊文件
        strm_files = [f for f in dir_files if f[0].lower().endswith('.strm')]
        tvshow_files = [f for f in dir_files if os.path.basename(f[0]).lower() == 'tvshow.nfo']
        
        if strm_files:
            # 处理含有strm文件的目录
            if len(strm_files) == 1:
                # 单strm目录，所有文件一个组
                group_id = f"strm_{generate_group_id(parent_dir, strm_files[0][0])}"
                for file in dir_files:
                    all_items.append((*file, group_id, parent_dir))
                    if file[0].lower().endswith('.strm'):
                        strm_items.append((*file, group_id))
            else:
                # 多strm目录，按基本名分组
                grouped = set()
                for strm_file in strm_files:
                    base_name = os.path.splitext(strm_file[0])[0]
                    group_id = f"strm_{generate_group_id(parent_dir, strm_file[0])}"
                    
                    # 找出同名文件
                    for file in dir_files:
                        file_base = os.path.splitext(file[0])[0]
                        if file_base == base_name:
                            all_items.append((*file, group_id, parent_dir))
                            grouped.add(file[0])
                            if file[0].lower().endswith('.strm'):
                                strm_items.append((*file, group_id))
                
                # 处理未分组文件
                for file in dir_files:
                    if file[0] not in grouped:
                        all_items.append((*file, "none-group", parent_dir))
        elif tvshow_files:
            # 处理只有tvshow.nfo的目录
            if len(tvshow_files) == 1:
                group_id = f"tvshow_{generate_group_id(parent_dir, tvshow_files[0][0])}"
                for file in dir_files:
                    all_items.append((*file, group_id, parent_dir))
                    if os.path.basename(file[0]).lower() == 'tvshow.nfo':
                        tvshow_items.append((*file, group_id))
            else:
                # 多个 tvshow.nfo，所有文件进入 none-group
                for file in dir_files:
                    all_items.append((*file, "none-group", parent_dir))
                    if os.path.basename(file[0]).lower() == 'tvshow.nfo':
                        tvshow_items.append((*file, "none-group"))
        else:
            # 其他目录
            for file in dir_files:
                all_items.append((*file, "none-group", parent_dir))
    
    return all_items, strm_items, tvshow_items


def remove_empty_folders(paths, media):
    for path in paths:
        for root, dirs, files in os.walk(
            unquote(os.path.join(media, path)), topdown=False
        ):
            dirs[:] = [d for d in dirs if d not in s_folder]
            if not dirs and not files:
                try:
                    os.rmdir(root)
                    logger.info("Deleted empty folder: %s", root)
                except OSError as e:
                    logger.error("Failed to delete folder %s: %s", root, e)


async def generate_localdb(db, media, paths):
    logger.warning(
        "Generating local DB... It takes time depends on the DiskI/O performance... Do NOT quit..."
    )
    sync_state.set_stage("生成本地数据库")
    
    async with aiosqlite.connect(db) as conn:
        await create_table(conn)
        
        all_items = []
        all_strm_items = []
        all_tvshow_items = []
        
        for path in paths:
            logger.info("Processing %s", unquote(os.path.join(media, path)))
            items, strm_items, tvshow_items = process_folder_with_grouping(
                unquote(os.path.join(media, path)), 
                media
            )
            all_items.extend(items)
            all_strm_items.extend(strm_items)
            all_tvshow_items.extend(tvshow_items)
            
            # 更新进度状态
            sync_state.set_stage(f"扫描本地目录: {unquote(path)}")
        
        # 一次性写入所有数据
        sync_state.set_stage("写入数据库")
        await insert_files(conn, all_items, all_strm_items, all_tvshow_items)
        
        total_items_count = len(all_items)
        logger.info("There are %d files on the local disk", total_items_count)
        return total_items_count


async def get_total_items_count(conn):
    async with conn.execute("SELECT COUNT(*) FROM files") as cursor:
        result = await cursor.fetchone()
        total_count = result[0] if result else 0
    return total_count


async def process_remote_files(files, parent_dir, db_session):
    """处理远程爬取的文件，进行分组并存储到数据库"""
    # 筛选特殊文件
    strm_files = [f for f in files if f[1].lower().endswith('.strm')]
    tvshow_files = [f for f in files if os.path.basename(f[1]).lower() == 'tvshow.nfo']
    
    all_items = []
    strm_items = []
    tvshow_items = []
    
    if strm_files:
        # 有 strm 文件的目录
        if len(strm_files) == 1:
            # 单 strm 目录，所有文件一个组
            group_id = f"strm_{generate_group_id(parent_dir, strm_files[0][1])}"
            for file in files:
                _, filename, timestamp, filesize = file
                all_items.append((filename, timestamp, filesize, group_id, parent_dir))
                if filename.lower().endswith('.strm'):
                    strm_items.append((filename, timestamp, filesize, group_id))
        else:
            # 多 strm 目录，按基本名分组
            grouped = set()
            for strm_file in strm_files:
                _, filename, timestamp, filesize = strm_file
                base_name = os.path.splitext(filename)[0]
                group_id = f"strm_{generate_group_id(parent_dir, filename)}"
                
                # 找出同名文件
                for file in files:
                    _, f_name, f_timestamp, f_filesize = file
                    f_base = os.path.splitext(f_name)[0]
                    if f_base == base_name:
                        all_items.append((f_name, f_timestamp, f_filesize, group_id, parent_dir))
                        grouped.add(f_name)
                        if f_name.lower().endswith('.strm'):
                            strm_items.append((f_name, f_timestamp, f_filesize, group_id))
            
            # 处理未分组文件
            for file in files:
                _, f_name, f_timestamp, f_filesize = file
                if f_name not in grouped:
                    all_items.append((f_name, f_timestamp, f_filesize, "none-group", parent_dir))
    elif tvshow_files:
        # 只有 tvshow.nfo 的目录
        if len(tvshow_files) == 1:
            # 单 tvshow.nfo，所有文件一个组
            _, filename, timestamp, filesize = tvshow_files[0]
            group_id = f"tvshow_{generate_group_id(parent_dir, filename)}"
            for file in files:
                _, f_name, f_timestamp, f_filesize = file
                all_items.append((f_name, f_timestamp, f_filesize, group_id, parent_dir))
                if os.path.basename(f_name).lower() == 'tvshow.nfo':
                    tvshow_items.append((f_name, f_timestamp, f_filesize, group_id))
        else:
            # 多个 tvshow.nfo，所有文件进入 none-group
            for file in files:
                _, f_name, f_timestamp, f_filesize = file
                all_items.append((f_name, f_timestamp, f_filesize, "none-group", parent_dir))
                if os.path.basename(f_name).lower() == 'tvshow.nfo':
                    tvshow_items.append((f_name, f_timestamp, f_filesize, "none-group"))
    else:
        # 无 strm 和 tvshow.nfo 的目录，所有文件进入 none-group
        for file in files:
            _, f_name, f_timestamp, f_filesize = file
            all_items.append((f_name, f_timestamp, f_filesize, "none-group", parent_dir))
    
    # 写入数据库
    if all_items:
        await db_session.executemany(
            "INSERT OR REPLACE INTO files VALUES (?, ?, ?, ?, ?)",
            all_items
        )
    
    if strm_items:
        await db_session.executemany(
            "INSERT OR REPLACE INTO strm_files VALUES (?, ?, ?, ?)",
            strm_items
        )
    
    if tvshow_items:
        await db_session.executemany(
            "INSERT OR REPLACE INTO tvshow_files VALUES (?, ?, ?, ?)",
            tvshow_items
        )
    
    await db_session.commit()


async def write_one(url, session, db_session, **kwargs) -> list:
    # 处理根路径
    if urlparse(url).path == "/":
        directories = []
        for path in kwargs["paths"]:
            directories.append(urljoin(url, path))
        return directories
    
    # 获取当前目录的文件和子目录
    files, directories = await parse(url=url, session=session, **kwargs)
    if not files:
        return directories
    
    # 获取当前目录的相对路径
    parent_dir = os.path.dirname(urlparse(url).path.lstrip('/'))
    
    # 如果指定了媒体目录，下载文件
    if kwargs["media"]:
        await download_files(files=files, session=session, **kwargs)
    
    # 如果有数据库会话，处理文件分组并写入数据库
    if db_session:
        await process_remote_files(files, parent_dir, db_session)
    
    return directories


async def bulk_crawl_and_write(url, session, db_session, depth=0, **kwargs) -> None:
    tasks = set()
    sync_state.set_stage(f"爬取目录: {unquote(urlparse(url).path)}")
    
    directories = await write_one(
        url=url, session=session, db_session=db_session, **kwargs
    )
    
    # 动态控制并发数
    max_parallel = determine_optimal_concurrency(20)  # 子目录并发爬取数
    active_tasks = set()
    
    for url in directories:
        # 控制目录爬取的并发数
        while len(active_tasks) >= max_parallel:
            done, pending = await asyncio.wait(
                active_tasks, return_when=asyncio.FIRST_COMPLETED
            )
            active_tasks = pending
        
        task = asyncio.create_task(
            bulk_crawl_and_write(
                url=url,
                session=session,
                db_session=db_session,
                depth=depth + 1,
                **kwargs,
            )
        )
        task.add_done_callback(lambda t: active_tasks.discard(t) if t in active_tasks else None)
        active_tasks.add(task)
        tasks.add(task)
    
    # 第一层时等待所有目录爬取完成
    if depth == 0:
        await asyncio.gather(*tasks)
    
    # 等待当前批次的子目录爬取完成
    await asyncio.gather(*active_tasks)


async def determine_sync_files(localdb, tempdb, **kwargs):
    """确定需要同步的文件"""
    logger.info("Determining files to sync...")
    sync_state.set_stage("确定需要同步的文件")
    
    files_to_download = []
    
    async with aiosqlite.connect(localdb) as local_conn, aiosqlite.connect(tempdb) as remote_conn:
        # 获取本地和远程的 strm 文件
        async with local_conn.execute("SELECT * FROM strm_files") as cursor:
            local_strm = {row[0]: row for row in await cursor.fetchall()}
        
        async with remote_conn.execute("SELECT * FROM strm_files") as cursor:
            remote_strm = {row[0]: row for row in await cursor.fetchall()}
        
        # 1. 远程有本地没有的 strm 文件
        for filename, info in remote_strm.items():
            if filename not in local_strm:
                # 获取该 strm 文件的组内所有文件
                group_id = info[3]
                async with remote_conn.execute(
                    "SELECT filename, timestamp, filesize FROM files WHERE group_id = ?", 
                    (group_id,)
                ) as cursor:
                    group_files = await cursor.fetchall()
                
                # 添加到下载列表
                files_to_download.extend(group_files)
                logger.debug("Planning to download new file group: %s", group_id)
            else:
                # 本地和远程都有的 strm 文件，检查是否需要更新
                local_info = local_strm[filename]
                remote_info = info
                
                # 比较时间戳和大小
                if (int(remote_info[2]) != int(local_info[2]) or  # 大小不同
                    int(remote_info[1]) > int(local_info[1])):   # 远程更新
                    # 只更新 strm 文件本身
                    files_to_download.append((filename, remote_info[1], remote_info[2]))
                    logger.debug("Planning to update strm file: %s", filename)
        
        # 2. 处理 tvshow.nfo 文件
        async with local_conn.execute(
            "SELECT * FROM tvshow_files WHERE group_id LIKE 'tvshow_%'"
        ) as cursor:
            local_tvshows = {row[0]: row for row in await cursor.fetchall()}
        
        async with remote_conn.execute(
            "SELECT * FROM tvshow_files WHERE group_id LIKE 'tvshow_%'"
        ) as cursor:
            remote_tvshows = {row[0]: row for row in await cursor.fetchall()}
        
        for filename, info in remote_tvshows.items():
            if filename not in local_tvshows:
                # 获取组内所有文件
                group_id = info[3]
                async with remote_conn.execute(
                    "SELECT filename, timestamp, filesize FROM files WHERE group_id = ?", 
                    (group_id,)
                ) as cursor:
                    group_files = await cursor.fetchall()
                
                # 添加到下载列表
                files_to_download.extend(group_files)
                logger.debug("Planning to download tvshow group: %s", group_id)
            else:
                # tvshow.nfo 更新比较
                local_info = local_tvshows[filename]
                remote_info = info
                
                if (int(remote_info[2]) != int(local_info[2]) or  # 大小不同
                    int(remote_info[1]) > int(local_info[1])):   # 远程更新
                    # 只更新 nfo 文件本身
                    files_to_download.append((filename, remote_info[1], remote_info[2]))
                    logger.debug("Planning to update tvshow.nfo file: %s", filename)
        
        # 3. 处理未分组文件
        if kwargs.get("no_group_sync", False):
            async with remote_conn.execute(
                "SELECT filename, timestamp, filesize FROM files WHERE group_id = 'none-group'"
            ) as cursor:
                none_group_files = await cursor.fetchall()
            
            for file in none_group_files:
                # 检查本地是否已有此文件
                async with local_conn.execute(
                    "SELECT COUNT(*) FROM files WHERE filename = ?", (file[0],)
                ) as count_cursor:
                    exists = (await count_cursor.fetchone())[0] > 0
                
                if not exists:
                    files_to_download.append(file)
                    logger.debug("Planning to download ungrouped file: %s", file[0])
    
    logger.info("Found %d files to sync", len(files_to_download))
    sync_state.update(total=len(files_to_download))
    
    # 计算总大小
    total_size = sum(int(file[2]) for file in files_to_download)
    sync_state.update(total_size=total_size)
    
    return files_to_download


async def determine_purge_files(localdb, tempdb):
    """确定需要清理的文件"""
    logger.info("Determining files to purge...")
    sync_state.set_stage("确定需要清理的文件")
    
    files_to_purge = []
    
    async with aiosqlite.connect(localdb) as local_conn, aiosqlite.connect(tempdb) as remote_conn:
        # 获取本地和远程的 strm 文件
        async with local_conn.execute("SELECT * FROM strm_files") as cursor:
            local_strm = {row[0]: row for row in await cursor.fetchall()}
        
        async with remote_conn.execute("SELECT * FROM strm_files") as cursor:
            remote_strm = {row[0]: row for row in await cursor.fetchall()}
        
        # 1. 本地有远程没有的 strm 文件
        for filename, info in local_strm.items():
            if filename not in remote_strm:
                # 获取该 strm 文件的组内所有文件
                group_id = info[3]
                async with local_conn.execute(
                    "SELECT filename FROM files WHERE group_id = ?", 
                    (group_id,)
                ) as cursor:
                    group_files = [row[0] for row in await cursor.fetchall()]
                
                # 添加到删除列表
                files_to_purge.extend(group_files)
                logger.debug("Planning to purge strm group: %s (%d files)", group_id, len(group_files))
        
        # 2. 处理本地有远程没有的 tvshow.nfo 文件
        async with local_conn.execute(
            "SELECT * FROM tvshow_files WHERE group_id LIKE 'tvshow_%'"
        ) as cursor:
            local_tvshows = {row[0]: row for row in await cursor.fetchall()}
        
        async with remote_conn.execute(
            "SELECT * FROM tvshow_files WHERE group_id LIKE 'tvshow_%'"
        ) as cursor:
            remote_tvshows = {row[0]: row for row in await cursor.fetchall()}
        
        for filename, info in local_tvshows.items():
            if filename not in remote_tvshows:
                # 获取组内所有文件
                group_id = info[3]
                async with local_conn.execute(
                    "SELECT filename FROM files WHERE group_id = ?", 
                    (group_id,)
                ) as cursor:
                    group_files = [row[0] for row in await cursor.fetchall()]
                
                # 添加到删除列表，确保没有重复
                for file in group_files:
                    if file not in files_to_purge:
                        files_to_purge.append(file)
                        logger.debug("Planning to purge tvshow file: %s", file)
    
    logger.info("Found %d files to purge", len(files_to_purge))
    return files_to_purge


def test_media_folder(media, paths):
    t_paths = [os.path.join(media, unquote(path)) for path in paths]
    if all(os.path.exists(os.path.abspath(path)) for path in t_paths):
        return True
    else:
        return False


def test_db_folder(location):
    if not os.path.isdir(location):
        logging.error("The path %s is not a directory.", location)
        return False
    if not os.access(location, os.W_OK):
        logging.error("The directory %s doesn't have write permission.", location)
        return False
    return True


def load_paths_from_file(path_file):
    paths = []
    try:
        with open(path_file, "r", encoding="utf-8") as file:
            for line in file:
                stripped_line = line.strip()
                if stripped_line:
                    encoded_path = quote(stripped_line)
                    if is_subpath(encoded_path, s_paths_all):
                        paths.append(encoded_path)
                    else:
                        logging.error("Path is invalid: %s", unquote(encoded_path))
                        return []
    except Exception as e:
        logging.error("Error loading paths from file: %s", str(e))
    return paths


def is_subpath(path, base_paths):
    for base_path in base_paths:
        if path.startswith(base_path):
            return True
    return False


def get_paths_from_bitmap(bitmap, paths_all):
    max_bitmap_value = (1 << len(paths_all)) - 1
    if bitmap < 0 or bitmap > max_bitmap_value:
        raise ValueError(
            f"Bitmap value {bitmap} is out of range. Must be between 0 and {max_bitmap_value}."
        )
    selected_paths = []
    binary_representation = bin(bitmap)[2:].zfill(len(paths_all))
    for i, bit in enumerate(binary_representation):
        if bit == "1":
            selected_paths.append(paths_all[i])
    return selected_paths


#########################################
# 改进6: 添加Web服务器支持
#########################################
app = Flask(__name__)

@app.route('/api/status')
def get_status():
    return jsonify(sync_state.get_status())

@app.route('/api/start', methods=['POST'])
def start_sync():
    from threading import Thread
    # 这个函数需要外部定义或重构主函数为可调用形式
    if not sync_state.running:
        # 这里需要为web服务启动同步实现一个单独的函数
        # Thread(target=run_sync).start()
        return jsonify({'status': 'started'})
    return jsonify({'status': 'already_running'})

@app.route('/api/stop', methods=['POST'])
def stop_sync():
    if sync_state.running:
        sync_state.stop()
        return jsonify({'status': 'stopping'})
    return jsonify({'status': 'not_running'})

def run_web_server(port=8080):
    app.run(host='0.0.0.0', port=port, debug=False)


async def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--media",
        metavar="<folder>",
        type=str,
        default=None,
        required=True,
        help="Path to store downloaded media files [Default: %(default)s]",
    )
    parser.add_argument(
        "--count",
        metavar="[number]",
        type=int,
        default=100,
        help="Max concurrent HTTP Requests [Default: %(default)s]",
    )
    parser.add_argument(
        "--debug",
        action=argparse.BooleanOptionalAction,
        type=bool,
        default=False,
        help="Verbose debug [Default: %(default)s]",
    )
    parser.add_argument(
        "--db",
        action=argparse.BooleanOptionalAction,
        type=bool,
        default=False,
        help="<Python3.12+ required> Save into DB [Default: %(default)s]",
    )
    parser.add_argument(
        "--nfo",
        action=argparse.BooleanOptionalAction,
        type=bool,
        default=False,
        help="Download NFO [Default: %(default)s]",
    )
    parser.add_argument(
        "--url",
        metavar="[url]",
        type=str,
        default=None,
        help="Download path [Default: %(default)s]",
    )
    parser.add_argument(
        "--purge",
        action=argparse.BooleanOptionalAction,
        type=bool,
        default=True,
        help="Purge removed files [Default: %(default)s]",
    )
    parser.add_argument(
        "--all",
        action=argparse.BooleanOptionalAction,
        type=bool,
        default=False,
        help="Download all folders [Default: %(default)s]",
    )
    parser.add_argument(
        "--location",
        metavar="<folder>",
        type=str,
        default=None,
        required=None,
        help="Path to store database files [Default: %(default)s]",
    )
    parser.add_argument(
        "--paths",
        metavar="<file>",
        type=str,
        help="Bitmap of paths or a file containing paths to be selected (See paths.example)",
    )
    parser.add_argument(
        "--no-group-sync",
        action=argparse.BooleanOptionalAction,
        type=bool,
        default=False,
        help="是否同步未分组文件 [Default: %(default)s]",
    )
    parser.add_argument(
        "--bandwidth-limit",
        metavar="[mbps]",
        type=float,
        default=None,
        help="Bandwidth limit in Mbps [Default: no limit]",
    )
    parser.add_argument(
        "--web",
        action=argparse.BooleanOptionalAction,
        type=bool,
        default=False,
        help="启动Web服务器 [Default: %(default)s]",
    )
    parser.add_argument(
        "--port",
        metavar="[port]",
        type=int,
        default=8080,
        help="Web服务器端口 [Default: %(default)s]",
    )

    args = parser.parse_args()
    if args.debug:
        logging.getLogger("emd").setLevel(logging.DEBUG)
    logging.info("*** xiaoya_emd version 2.1.0 (strm-focused with enhancements) ***")
    
    # 启动Web服务器
    if args.web:
        from threading import Thread
        web_thread = Thread(target=run_web_server, args=(args.port,), daemon=True)
        web_thread.start()
        logger.info(f"Web服务器已启动在 http://localhost:{args.port}")
    
    # 创建带宽限制器
    limiter = None
    if args.bandwidth_limit:
        limiter = BandwidthLimiter(args.bandwidth_limit)
        logger.info(f"带宽限制设置为 {args.bandwidth_limit} Mbps")
    
    # 开始同步状态跟踪
    sync_state.start()
    
    paths = []
    if args.all:
        paths = s_paths_all
        s_pool.pop(0)
        if args.purge:
            args.db = True
    else:
        if args.paths:
            paths_from_file = []
            is_bitmap = False

            try:
                paths_bitmap = int(args.paths)
                paths_from_file = get_paths_from_bitmap(paths_bitmap, s_paths_all)
                is_bitmap = True
            except ValueError:
                logging.info(
                    "Paths parameter is not a bitmap, attempting to load from file."
                )

            if not is_bitmap:
                paths_from_file = load_paths_from_file(args.paths)

            if not paths_from_file:
                logging.error(
                    "Paths file doesn't contain any valid paths or bitmap value is incorrect: %s",
                    args.paths,
                )
                sys.exit(1)

            for path in paths_from_file:
                if not is_subpath(path, s_paths):
                    s_pool.pop(0)
                    break
            paths.extend(paths_from_file)
        if not paths:
            paths = s_paths

    if args.media:
        if not os.path.exists(os.path.join(args.media, "115")):
            logging.warning(
                "115 folder doesn't exist. Creating it anyway...This workaround will be removed in the next version."
            )
            os.makedirs(os.path.join(args.media, "115"))
        if not test_media_folder(args.media, t_paths):
            logging.error(
                "The %s doesn't contain the desired folders, please correct the --media parameter",
                args.media,
            )
            sys.exit(1)
        else:
            media = args.media.rstrip("/")
    
    # 使用改进的服务器选择
    if not args.url:
        best_servers = await pick_best_servers(s_pool)
        if best_servers:
            url = best_servers[0]  # 使用最佳服务器
        else:
            logger.info(
                "No servers are reachable, please check your Internet connection..."
            )
            sys.exit(1)
    else:
        url = args.url
    
    if urlparse(url).path != "/" and (args.purge or args.db):
        logger.warning("--db or --purge only support in root path mode")
        sys.exit(1)
    
    if urlparse(url).path == "/":
        sync_state.set_stage("检查远程文件数量")
        total_amount = current_amount(url + ".scan.list.gz", media, paths)
        logger.info("There are %d files in %s", total_amount, url)
    
    # 使用动态并发数
    max_concurrency = determine_optimal_concurrency(args.count)
    semaphore = asyncio.Semaphore(max_concurrency)
    logger.info(f"使用最佳并发数: {max_concurrency}")
    
    db_session = None
    if args.db or args.purge:
        assert sys.version_info >= (3, 12), "DB function requires Python 3.12+."
        if args.location:
            if test_db_folder(args.location) is True:
                db_location = args.location.rstrip("/")
            else:
                sys.exit(1)
        else:
            db_location = media
        localdb = os.path.join(db_location, ".localfiles.db")
        tempdb = os.path.join(db_location, ".tempfiles.db")
        
        # 初始化本地数据库
        if not os.path.exists(localdb):
            await generate_localdb(localdb, media, paths)
        elif args.db:
            os.remove(localdb)
            await generate_localdb(localdb, media, paths)
        else:
            # 检查本地数据库完整性
            try:
                async with aiosqlite.connect(localdb) as local_session:
                    local_amount = await get_total_items_count(local_session)
                    if (
                        local_amount > 0
                        and total_amount > 0
                        and abs(total_amount - local_amount) > 1000
                    ):
                        logger.warning("Local DB isn't intact. Regenerating...")
                        await local_session.execute("DELETE FROM files")
                        await local_session.execute("DELETE FROM strm_files")
                        await local_session.execute("DELETE FROM tvshow_files")
                        await local_session.commit()
                        await generate_localdb(localdb, media, paths)
            except Exception as e:
                logger.error("Error checking local DB: %s. Regenerating...", e)
                os.remove(localdb)
                await generate_localdb(localdb, media, paths)

                # 创建临时数据库
        db_session = await aiosqlite.connect(tempdb)
        await create_table(db_session)
    
    # 优化爬取过程
    sync_state.set_stage("爬取远程文件")
    logger.info("开始爬取远程文件...")
    
    # 使用优化的HTTP客户端
    tcp_connector = TCPConnector(ssl=False, limit=0, ttl_dns_cache=600)
    
    # 添加带宽限制支持
    if limiter:
        transport = LimitedTransport(limiter, tcp_connector)
        timeout = aiohttp.ClientTimeout(total=36000)
        session_kwargs = {
            'connector': None,  # 设为None因为我们使用自定义transport
            'timeout': timeout,
            'transport': transport
        }
    else:
        session_kwargs = {
            'connector': tcp_connector,
            'timeout': aiohttp.ClientTimeout(total=36000)
        }
    
    async with ClientSession(**session_kwargs) as session:
        await bulk_crawl_and_write(
            url=url,
            session=session,
            db_session=db_session,
            semaphore=semaphore,
            media=media,
            nfo=args.nfo,
            paths=paths,
            no_group_sync=args.no_group_sync
        )
    
    # 提交临时数据库并关闭
    if db_session:
        await db_session.commit()
        await db_session.close()
    
    # 执行文件同步
    if args.purge and db_session:
        sync_state.set_stage("确定文件变更")
        
        # 确定需要下载的文件
        files_to_download = await determine_sync_files(
            localdb, tempdb, no_group_sync=args.no_group_sync
        )
        
        # 确定需要删除的文件
        files_to_purge = await determine_purge_files(localdb, tempdb)
        
        # 下载文件
        if files_to_download:
            sync_state.set_stage("下载文件")
            best_servers = await pick_best_servers(s_pool, top_n=3)
            
            # 优化并发下载
            download_semaphore = asyncio.Semaphore(determine_optimal_concurrency(20))
            download_tasks = []
            
            for file_info in files_to_download:
                filename, timestamp, filesize = file_info
                file_path = os.path.join(media, filename.lstrip("/"))
                
                task = asyncio.create_task(
                    download_file(
                        (url, filename, timestamp, filesize),
                        session=session,
                        semaphore=download_semaphore,
                        media=media,
                        nfo=args.nfo,
                        limiter=limiter
                    )
                )
                download_tasks.append(task)
                
                # 批量执行下载任务
                if len(download_tasks) >= 100:
                    await asyncio.gather(*download_tasks)
                    download_tasks = []
                    
                    # 更新进度
                    sync_state.increment_processed(100)
            
            # 处理剩余下载任务
            if download_tasks:
                await asyncio.gather(*download_tasks)
                sync_state.increment_processed(len(download_tasks))
        
        # 清理文件
        if files_to_purge and args.purge:
            sync_state.set_stage("清理文件")
            for file in files_to_purge:
                logger.info("Purged %s", file)
                try:
                    os.remove(media + file)
                    sync_state.increment_purged()
                except Exception as e:
                    logger.error("Unable to remove %s due to %s", file, e)
            
            # 清理空目录
            sync_state.set_stage("清理空目录")
            remove_empty_folders(paths, media)
        
        # 数据库处理
        if not args.all:
            os.rename(tempdb, localdb)
            logger.info("Updated local database")
        else:
            os.remove(tempdb)
    
    sync_state.complete()
    logger.info("同步完成！")


# 创建主函数
if __name__ == "__main__":
    # 初始化状态跟踪
    sync_state = SyncState()
    
    # 版本检查
    assert sys.version_info >= (3, 10), "Script requires Python 3.10+."
    
    try:
        # 兼容性问题：确保 Flask 模块可选
        try:
            from flask import Flask, jsonify
        except ImportError:
            class DummyFlask:
                def __init__(self, *args, **kwargs):
                    pass
                def route(self, *args, **kwargs):
                    def decorator(f):
                        return f
                    return decorator
                def run(self, *args, **kwargs):
                    pass
            Flask = DummyFlask
            def jsonify(x): 
                return {}
        
        # 启动主程序
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断，正在退出...")
        sync_state.stop()
    except Exception as e:
        logger.exception("发生错误: %s", e)
        sync_state.error(str(e))

class LimitedTransport:
    def __init__(self, limiter, connector):
        self.limiter = limiter
        self.connector = connector
        
    async def __call__(self, *args, **kwargs):
        return LimitedClientRequest(self.limiter, *args, **kwargs)

class LimitedClientRequest(aiohttp.ClientRequest):
    def __init__(self, limiter, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.limiter = limiter
        
    async def send(self, conn):
        response = await super().send(conn)
        response.content = LimitedContent(response.content, self.limiter)
        return response

class LimitedContent:
    def __init__(self, content, limiter):
        self.content = content
        self.limiter = limiter
        
    async def read(self, n=-1):
        chunk = await self.content.read(n)
        if chunk and self.limiter:
            await self.limiter.limit(len(chunk))
        return chunk
        
    async def __aiter__(self):
        return self
        
    async def __anext__(self):
        chunk = await self.read(1024)
        if not chunk:
            raise StopAsyncIteration
        return chunk

async def download_file(file_tuple, **kwargs):
    """下载单个文件的包装函数"""
    url, filename, timestamp, filesize = file_tuple
    return await download((url, filename, timestamp, filesize), **kwargs)