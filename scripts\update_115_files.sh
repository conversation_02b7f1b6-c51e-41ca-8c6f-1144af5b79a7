#!/bin/bash

# 使用说明
# 本脚本用于自动更新和维护 115 网盘的文件
##### 此脚本在迪拜的vps上测试时，存在终端提示成功，实际上传失败的情况，即网盘没有文件，所以弃用了！！！
##### 现在使用 update_quark_files.sh 脚本替代，已验证成功！！！
# 安装步骤：
# 1. 创建服务文件：
# sudo nano /etc/systemd/system/update-115-files.service
#    
# [Unit]
# Description=Update 115 Files Service
# After=network.target docker.service
# Wants=docker.service

# [Service]
# Type=simple
# User=root
# ExecStart=/www/data/scripts/update_115_files.sh
# WorkingDirectory=/www/data
# Restart=always
# RestartSec=60
# 
# [Install]
# WantedBy=multi-user.target
# 
# 2. 创建必要目录并移动脚本：
#    sudo mkdir -p /www/data/scripts
#    sudo mv update_115_files.sh /www/data/scripts/
#    sudo chmod +x /www/data/scripts/update_115_files.sh
#
# 3. 启用并启动服务：
#    sudo systemctl daemon-reload              # 重新加载 systemd 配置
#    sudo systemctl enable update-115-files    # 设置开机自启
#    sudo systemctl start update-115-files     # 立即启动服务
#
# 4. 管理服务：
#    sudo systemctl status update-115-files    # 查看服务状态
#    sudo systemctl stop update-115-files      # 停止服务
#    sudo systemctl restart update-115-files   # 重启服务
#    sudo journalctl -u update-115-files -f    # 实时查看日志
#
# 5. 日志位置：
#    - 服务日志：journalctl -u update-115-files
#    - 脚本日志：/www/data/update_115.log
#
# 注意事项：
# - 需要安装 curl、jq 等依赖：
#   sudo apt update
#   sudo apt install -y curl jq
# - 确保 Alist 服务正常运行且可访问
# - 确保 115 网盘 Cookie 有效
# - 建议定期检查日志确保服务正常运行

# 配置参数
ALIST_URL="http://127.0.0.1:5244"
mkdir -p /www/data
WORK_DIR="/www/data"
LOG_DIR="/www/data"
MAX_LOGS=10
MAX_BACKUPS=3
FILES=("version.txt" "index.zip" "update.zip" "tvbox.zip")

# 下载源
SOURCES=(
    "http://docker.xiaoya.pro/update"
    "https://raw.githubusercontent.com/xiaoyaliu00/data/main"
    "http://har01d.org"
)

# AMD64 镜像列表
declare -A IMAGES=(
    [1]="ailg/g-box:hostmode"
    [2]="ailg/ggbond:latest"
    [3]="emby/embyserver:*******"
    [4]="emby/embyserver:********"
    [5]="jellyfin/jellyfin:10.9.6"
    [6]="nyanmisaka/jellyfin:latest"
    [7]="ddsderek/xiaoya-emd:latest"
    [8]="cloudnas/clouddrive2:latest"
)

# ARM64 镜像列表
declare -A ARM64_IMAGES=(
    [1]="ailg/g-box:hostmode"
    [2]="ailg/ggbond:latest"
    [3]="emby/embyserver_arm64v8:*******"
    [4]="emby/embyserver_arm64v8:********"
    [5]="jellyfin/jellyfin:10.9.6"
    [6]="nyanmisaka/jellyfin:latest-rockchip"
    [7]="ddsderek/xiaoya-emd:latest"
    [8]="cloudnas/clouddrive2:latest"
)

IMAGES_DIR="${WORK_DIR}/images"
IMAGES_REMOTE_DIR="ailg_images"  # 115网盘中的目录名
ARCH="amd64"

# 115网盘相关配置
ALIST_DB_PATH="/etc/ailg/alist/data.db"
ALIST_STORAGE_ID=12316
ALIST_MOUNT_115="/ailg_115"
COOKIE_FILE="${WORK_DIR}/115_cookie.txt"

# 全局状态变量
TOKEN_EXPIRE_TIME=0
FOLDER_ID=""  # 存储115网盘中ailg_images目录的ID
NEED_UPDATE_SHARE=false  # 跟踪是否有文件更新

# 统一日志文件名
LOG_FILE="${LOG_DIR}/update_115.log"

# 统一日志函数
log() {
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] $1" | tee -a "$LOG_FILE"
}

# 改进日志轮转
rotate_logs() {
    local today=$(date +%Y-%m-%d)
    
    if [ -s "$LOG_FILE" ]; then
        # 添加时间戳，避免同一天多次轮转时覆盖
        local timestamp=$(date +%H%M%S)
        mv "$LOG_FILE" "${LOG_DIR}/update_115-${today}-${timestamp}.log"
        touch "$LOG_FILE"
        
        # 保留最新的 MAX_LOGS 个日志文件
        ls -t ${LOG_DIR}/update_115-*.log | tail -n +$((MAX_LOGS + 1)) | xargs -r rm
    fi
}

# 从数据库获取115 cookie
get_115_cookie() {
    local db_path=$1
    local storage_id=$2
    
    # 检查数据库文件是否存在
    if [ ! -f "$db_path" ]; then
        log "数据库文件不存在: $db_path"
        return 1
    fi
    
    # 使用 sqlite3 查询数据库并提取 cookie
    local cookie=$(sqlite3 "$db_path" "SELECT json_extract(addition, '$.cookie') FROM x_storages WHERE id = $storage_id;")
    
    # 移除可能的引号
    cookie=$(echo "$cookie" | sed 's/^"//;s/"$//')
    
    if [ -z "$cookie" ]; then
        log "无法从数据库获取 cookie"
        return 1
    fi
    
    echo "$cookie"
    return 0
}

# 从cookie中提取user_id
extract_user_id() {
    local cookie=$1
    local uid_part=$(echo "$cookie" | grep -o "UID=[^;]*" | head -1)
    
    if [ -z "$uid_part" ]; then
        log "无法从cookie中提取UID"
        return 1
    fi
    
    local user_id=$(echo "$uid_part" | cut -d'=' -f2 | cut -d'_' -f1)
    
    if [ -z "$user_id" ]; then
        log "无法解析user_id"
        return 1
    fi
    
    echo "$user_id"
    return 0
}

# 获取或刷新Alist token
refresh_token() {
    # 如果token未过期，直接返回
    local current_time=$(date +%s)
    if [ $current_time -lt $TOKEN_EXPIRE_TIME ]; then
        return 0
    fi
    
    # token已过期或未设置，重新获取
    TOKEN=$(curl -s -X POST "http://127.0.0.1:5244/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"admin\",\"password\":\"AQG7q5bfwEVq5201314@\"}" | jq -r '.data.token')
    if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
        log "获取Alist token失败"
        return 1
    fi
    
    # 设置token过期时间（默认2小时）
    TOKEN_EXPIRE_TIME=$((current_time + 7200))
    return 0
}

# 在115网盘创建目录
create_115_directory() {
    local cookie="$1"
    local dir_name="$2"
    local parent_id="0"  # 根目录ID为0
    
    # 检查参数
    if [ -z "$cookie" ] || [ -z "$dir_name" ]; then
        log "创建目录参数错误: cookie或目录名为空"
        log "cookie长度: ${#cookie}, dir_name: $dir_name"
        return 1
    fi
    
    log "在115网盘创建目录: $dir_name"
    
    # 调试信息
    echo "DEBUG: cookie前20个字符: ${cookie:0:20}..."
    echo "DEBUG: dir_name: $dir_name"
    
    # 使用与您成功测试相同的方式
    local response=$(curl -s -X POST "https://webapi.115.com/files/add" \
        -H "Cookie: $cookie" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36" \
        -d "pid=$parent_id&cname=$dir_name")
    
    # 检查响应
    if ! echo "$response" | grep -q '"state":true'; then
        log "创建目录失败: $response"
        
        # 检查是否是登录超时
        if echo "$response" | grep -q "990001"; then
            log "登录已超时，请更新cookie"
        fi
        
        return 1
    fi
    
    # 提取目录ID
    local folder_id=$(echo "$response" | jq -r '.file_id')
    
    if [ -z "$folder_id" ] || [ "$folder_id" = "null" ]; then
        log "无法获取目录ID"
        return 1
    fi
    
    log "目录创建成功: $dir_name (ID: $folder_id)"
    echo "$folder_id"
    return 0
}

# 检查115网盘目录是否存在
check_115_directory() {
    local cookie=$1
    local dir_name=$2
    
    log "检查115网盘目录是否存在: $dir_name" >&2
    
    # 搜索目录
    local response=$(curl -s "https://webapi.115.com/files/search?cid=0&aid=1&show_dir=1&count=1&offset=0&search_value=$dir_name" \
        -H "Cookie: $cookie" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36")
    
    if ! echo "$response" | grep -q '"state":true'; then
        log "搜索目录失败: $response" >&2
        return 1
    fi
    
    # 根据响应中的结构提取目录信息
    # 查找完全匹配的目录 (注意: 目录项有cid而不是fid)
    local folder_info=$(echo "$response" | jq -r --arg name "$dir_name" '.data[] | select(.n==$name and .cid!=null and .pid=="0")')
    
    if [ -z "$folder_info" ]; then
        log "目录不存在: $dir_name" >&2
        return 1
    fi
    
    # 提取目录ID (使用cid字段)
    local folder_id=$(echo "$folder_info" | jq -r '.cid')
    
    if [ -z "$folder_id" ] || [ "$folder_id" = "null" ]; then
        log "无法获取目录ID" >&2
        return 1
    fi
    
    # 验证目录是否可访问
    local verify_response=$(curl -s "https://webapi.115.com/files?aid=1&cid=$folder_id&o=user_ptime&asc=0&offset=0&show_dir=1&limit=1" \
        -H "Cookie: $cookie" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36")
    
    if ! echo "$verify_response" | grep -q '"state":true'; then
        log "目录无法访问: $folder_id" >&2
        return 1
    fi
    
    log "目录存在且可访问: $dir_name (ID: $folder_id)" >&2
    echo "$folder_id"
    return 0
}

# 获取115网盘目录中的文件列表
get_115_file_list() {
    local cookie=$1
    local folder_id=$2
    
    log "获取115网盘目录文件列表: $folder_id"
    
    local response=$(curl -s "https://webapi.115.com/files?aid=1&cid=$folder_id&o=user_ptime&asc=0&offset=0&show_dir=1&limit=1000" \
        -H "Cookie: $cookie" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36")
    
    # 检查响应
    if ! echo "$response" | grep -q '"state":true'; then
        log "获取文件列表失败: $response"
        return 1
    fi
    
    echo "$response"
    return 0
}

# 从回收站恢复文件
restore_from_recycle() {
    local cookie=$1
    local file_id=$2
    
    log "尝试从回收站恢复文件: file_id=$file_id"
    
    local response=$(curl -s -X POST "https://webapi.115.com/rb/revert" \
        -H "Cookie: $cookie" \
        -H "User-Agent: Mozilla/5.0 115Browser/27.0.5.7" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -H "Origin: https://webapi.115.com" \
        -H "Referer: https://webapi.115.com/bridge_2.0.html" \
        -d "rid[0]=$file_id")
    
    if echo "$response" | jq -e '.state==true' > /dev/null; then
        log "成功从回收站恢复文件: $file_id"
        return 0
    else
        log "从回收站恢复文件失败: $(echo "$response" | jq -r '.error // "未知错误"')"
        return 1
    fi
}

# 从回收站获取文件的rid
get_recycle_rid() {
    local cookie=$1
    local filename=$2
    local offset=0
    local limit=100  # 每页100条记录
    
    log "从回收站查找文件: $filename" >&2
    
    # 先等待几秒钟确保文件已经进入回收站
    sleep 5
    
    while true; do
        # 获取回收站文件列表
        local api_url="https://webapi.115.com/rb?aid=7&cid=0&offset=$offset&limit=$limit&format=json"
        
        local response=$(curl -s "$api_url" \
            -H "Cookie: $cookie" \
            -H "User-Agent: Mozilla/5.0 115Browser/27.0.5.7" \
            -H "Content-Type: application/json")
        
        if ! echo "$response" | jq -e '.state==true' > /dev/null; then
            log "获取回收站列表失败: $response" >&2
            return 1
        fi
        
        # 查找匹配的文件并只获取第一个匹配项的rid
        local rid=$(echo "$response" | jq -r --arg name "$filename" '.data[] | select(.file_name==$name) | .id' | head -n 1)
        
        if [ ! -z "$rid" ] && [ "$rid" != "null" ]; then
            log "找到文件的rid: $rid" >&2
            echo "$rid"
            return 0
        fi
        
        # 检查是否还有更多数据
        local count=$(echo "$response" | jq -r '.count')
        if [ $((offset + limit)) -ge "$count" ]; then
            break
        fi
        
        # 增加偏移量继续查找
        offset=$((offset + limit))
    done
    
    log "在回收站中未找到文件: $filename" >&2
    return 1
}

# 检查并启动 alist 容器
check_and_start_alist() {
    
    # 检查 alist 容器是否运行
    if ! docker ps | grep -q "alist"; then
        log "alist 容器未运行，尝试启动..."
        docker start alist
        
        # 最多检查3次，每次等待10秒
        local max_checks=3
        local check_count=1
        
        while [ $check_count -le $max_checks ]; do
            log "等待 alist 容器启动... (第 $check_count 次检查)"
            sleep 10
            
            if docker ps | grep -q "alist"; then
                log "alist 容器已成功启动"
                # 额外等待10秒确保服务完全就绪
                log "等待服务就绪..."
                sleep 10
                refresh_token
                return 0
            fi
            
            check_count=$((check_count + 1))
        done
        
        log "alist 容器启动失败，已尝试 $max_checks 次"
        return 1
    else
        log "alist 容器正在运行"
    fi
    
    return 0
}

# 修改上传函数
upload_to_115() {
    local cookie=$1
    local folder_id=$2
    local file_path=$3
    local file_name=$(basename "$file_path")
    local file_size=$(stat -c%s "$file_path")
    local deleted_file_id=""
    
    log "通过Alist上传文件到115网盘: $file_name (大小: $file_size 字节)"
    
    # 检查文件是否已存在
    log "检查文件是否已存在..."
    local file_list=$(curl -s "https://webapi.115.com/files?aid=1&cid=$folder_id&o=user_ptime&asc=0&offset=0&show_dir=1&limit=1000" \
        -H "Cookie: $cookie" \
        -H "User-Agent: Mozilla/5.0 115Browser/27.0.5.7")
    
    if echo "$file_list" | jq -e '.state==true' > /dev/null; then
        # 查找同名文件
        local existing_file=$(echo "$file_list" | jq -r --arg name "$file_name" '.data[] | select(.n==$name)')
        if [ ! -z "$existing_file" ]; then
            deleted_file_id=$(echo "$existing_file" | jq -r '.fid')
            log "找到已存在的文件，移动到回收站: file_id=$deleted_file_id"
            
            # 移动到回收站
            local delete_response=$(curl -s "https://webapi.115.com/rb/delete" \
                -H "Cookie: $cookie" \
                -H "User-Agent: Mozilla/5.0 115Browser/27.0.5.7" \
                -H "Content-Type: application/x-www-form-urlencoded" \
                -d "fid[0]=$deleted_file_id")
            
            if ! echo "$delete_response" | jq -e '.state==true' > /dev/null; then
                log "移动文件到回收站失败: $delete_response"
                return 1
            fi
            log "成功移动文件到回收站"
        fi
    fi
    
    # 确保Alist token有效
    refresh_token || return 1
    
    # 步骤1: 检查Alist存储状态
    log "检查115网盘挂载状态..."
    local storage_list=$(curl -s "http://127.0.0.1:5244/api/admin/storage/list" \
        -H "Authorization: $TOKEN" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36")
    
    # 检查是否有匹配的存储
    local has_115_storage=false
    local storage_id=""
    local storage_mount_path=""
    
    # 查找匹配的存储
    if echo "$storage_list" | jq -e --arg id "$ALIST_STORAGE_ID" --arg path "$ALIST_MOUNT_115" \
        '.data.content[] | select(.id==($id|tonumber) and .mount_path==$path)' > /dev/null; then
        # 找到完全匹配的存储
        has_115_storage=true
        storage_mount_path="$ALIST_MOUNT_115"
        log "找到匹配的115网盘存储: ID=$ALIST_STORAGE_ID, 挂载路径=$storage_mount_path"
    elif echo "$storage_list" | jq -e --arg id "$ALIST_STORAGE_ID" '.data.content[] | select(.id==($id|tonumber))' > /dev/null; then
        # 找到ID匹配但路径不匹配的存储，需要删除并重建
        log "找到ID匹配但路径不匹配的存储，删除并重建..."
        if ! delete_alist_storage "$TOKEN" "$ALIST_STORAGE_ID"; then
            log "删除旧存储失败"
            return 1
        fi
        has_115_storage=false
    fi
    
    # 如果没有找到匹配的存储，创建新的
    if [ "$has_115_storage" != "true" ]; then
        log "未找到匹配的115网盘存储，尝试添加..."
        
        # 创建addition字符串，正确处理JSON转义
        local addition_json="{\\\"cookie\\\":\\\"$cookie\\\",\\\"qrcode_token\\\":\\\"\\\",\\\"root_folder_id\\\":\\\"$folder_id\\\",\\\"page_size\\\":300,\\\"limit_rate\\\":2}"
        
        # 添加115网盘存储
        local add_response=$(curl -s -X POST "http://127.0.0.1:5244/api/admin/storage/create" \
            -H "Authorization: $TOKEN" \
            -H "Content-Type: application/json" \
            -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36" \
            -d '{
                "id": '"$ALIST_STORAGE_ID"',
                "mount_path": "'"$ALIST_MOUNT_115"'",
                "order": 0,
                "remark": "",
                "cache_expiration": 30,
                "status": "work",
                "web_proxy": false,
                "webdav_policy": "302_redirect",
                "down_proxy_url": "",
                "extract_folder": "extract",
                "driver": "115 Cloud",
                "addition": "'"$addition_json"'"
            }')
        
        if echo "$add_response" | jq -e '.code == 200' > /dev/null; then
            has_115_storage=true
            storage_mount_path="$ALIST_MOUNT_115"
            log "成功添加115网盘存储"
        else
            log "添加115网盘存储失败: $(echo "$add_response" | jq -r '.message')"
            return 1
        fi
    fi
    
    if [ "$has_115_storage" != "true" ]; then
        log "无法找到或添加115网盘存储"
        return 1
    fi
    
    # 步骤2: 上传文件
    local target_path="${storage_mount_path}/${file_name}"
    log "开始上传文件到: $target_path"
    
    local upload_response=$(curl -s -X PUT "http://127.0.0.1:5244/api/fs/form" \
        -H "Authorization: $TOKEN" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36" \
        -F "file=@${file_path}" \
        -H "File-Path:${target_path}")
    
    log "上传响应: $upload_response"
     
    # 在这里添加重试逻辑
    local max_retries=3
    local retry_count=0
    
    while ([ -z "$upload_response" ] || ! echo "$upload_response" | jq -e '.code == 200' > /dev/null) && [ $retry_count -lt $max_retries ]; do
        if [ -z "$upload_response" ]; then
            log "=================================================="
            log "上传响应为空，检查 alist 容器状态..."
            log "=================================================="
            
            # 检查并尝试启动 alist 容器
            if ! check_and_start_alist; then
                log "alist 容器无法启动，放弃重试"
                return 1
            fi
        else
            # 如果有响应但响应码不是 200，等待后重试
            log "上传失败，等待10秒后重试... (尝试 $((retry_count + 1))/$max_retries)"
            log "错误响应: $upload_response"
            sleep 10
        fi
        
        upload_response=$(curl -s -X PUT "http://127.0.0.1:5244/api/fs/form" \
            -H "Authorization: $TOKEN" \
            -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36" \
            -F "file=@${file_path}" \
            -H "File-Path:${target_path}")
            
        log "重试上传响应: $upload_response"
        retry_count=$((retry_count + 1))
    done
    # 处理上传失败的情况
    if [ -z "$upload_response" ] || ! echo "$upload_response" | jq -e '.code == 200' > /dev/null; then
        log "上传失败"
        
        # 如果有被删除的文件，尝试从回收站恢复
        if [ ! -z "$deleted_file_id" ]; then
            log "尝试从回收站恢复文件..."
            # 先等待一下确保文件已经进入回收站
            sleep 2
            
            # 获取回收站中的rid，传入目录的cid
            local rid=$(get_recycle_rid "$cookie" "$file_name")
            if [ $? -eq 0 ] && [ ! -z "$rid" ]; then
                if restore_from_recycle "$cookie" "$rid"; then
                    log "成功恢复原文件"
                    return 0
                fi
            fi
            log "恢复原文件失败"
        fi
        return 1
    fi
    
    NEED_UPDATE_SHARE=true  # 移到这里，确认上传成功后再设置标志    
    log "文件上传成功: $file_name"
    return 0
}

# 创建115分享
create_115_share() {
    local cookie=$1
    local user_id=$2
    local folder_id=$3
    
    log "创建115分享: $folder_id" >&2
    
    local response=$(curl -s -X POST "https://webapi.115.com/share/send" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36" \
        -H "Referer: https://115.com/" \
        -H "Origin: https://115.com" \
        -H "Cookie: $cookie" \
        -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
        -d "user_id=${user_id}&file_ids=${folder_id}&ignore_warn=1&is_asc=0&order=user_ptime")
    
    # 检查响应
    if ! echo "$response" | grep -q '"state":true'; then
        log "创建分享失败: $response"
        return 1
    fi
    
    # 提取分享信息
    local share_code=$(echo "$response" | jq -r '.data.share_code')
    local receive_code=$(echo "$response" | jq -r '.data.receive_code')
    
    if [ -z "$share_code" ] || [ "$share_code" = "null" ] || [ -z "$receive_code" ] || [ "$receive_code" = "null" ]; then
        log "无法获取分享信息"
        return 1
    fi
    
    # 返回分享信息
    echo "$share_code $receive_code"
    return 0
}

# 更新Alist存储
update_alist_storage() {
    local token=$1
    local storage_id=$2
    local cookie=$3
    local folder_id=$4
    local share_code=$5
    local receive_code=$6
    local action=$7  # create 或 update
    
    log "更新Alist存储: $action"
    
    # 准备请求数据
    local addition="{\"cookie\":\"$cookie\",\"root_folder_id\":\"$folder_id\",\"qrcode_token\":\"\",\"qrcode_source\":\"linux\",\"page_size\":1500,\"limit_rate\":2,\"share_code\":\"$share_code\",\"receive_code\":\"$receive_code\"}"
    
    local data="{
        \"id\": $storage_id,
        \"mount_path\": \"/ailg_115\",
        \"status\": \"work\",
        \"order\": 0,
        \"remark\": \"\",
        \"cache_expiration\": 30,
        \"web_proxy\": false,
        \"webdav_policy\": \"302_redirect\",
        \"down_proxy_url\": \"\",
        \"extract_folder\": \"front\",
        \"enable_sign\": false,
        \"driver\": \"115 Share\",
        \"order_by\": \"name\",
        \"order_direction\": \"asc\",
        \"addition\": $addition
    }"
    
    # 执行请求
    local url="${ALIST_URL}/api/admin/storage/${action}"
    local response=$(curl -s -X POST "$url" \
        -H "Authorization: $token" \
        -H "Content-Type: application/json" \
        -d "$data")
    
    # 检查响应
    if ! echo "$response" | grep -q '"code":200'; then
        log "更新Alist存储失败: $response"
        return 1
    fi
    
    log "Alist存储更新成功"
    return 0
}

# 检查Alist存储是否存在
check_alist_storage() {
    local token=$1
    local storage_id=$2
    
    log "检查Alist存储是否存在: $storage_id"
    
    local response=$(curl -s "${ALIST_URL}/api/admin/storage/list" \
        -H "Authorization: $token")
    
    # 检查响应
    if ! echo "$response" | grep -q '"code":200'; then
        log "获取存储列表失败: $response"
        return 1
    fi
    
    # 查找存储
    if echo "$response" | jq -r '.data[] | select(.id=='$storage_id')' | grep -q .; then
        return 0  # 存在
    else
        return 1  # 不存在
    fi
}

# 删除Alist存储
delete_alist_storage() {
    local token=$1
    local storage_id=$2
    
    log "删除Alist存储: $storage_id"
    
    local response=$(curl -s -X POST "${ALIST_URL}/api/admin/storage/delete" \
        -H "Authorization: $token" \
        -H "Content-Type: application/json" \
        -d "{\"id\":$storage_id}")
    
    # 检查响应
    if ! echo "$response" | grep -q '"code":200'; then
        log "删除存储失败: $response"
        return 1
    fi
    
    log "Alist存储删除成功"
    return 0
}

# 检查远程文件是否存在
check_115_file_exists() {
    local cookie=$1
    local folder_id=$2
    local file_name=$3
    local local_size=$4
    
    log "检查115网盘文件是否存在: $file_name" >&2
    
    # 获取文件列表
    local response=$(curl -s "https://webapi.115.com/files?aid=1&cid=$folder_id&o=user_ptime&asc=0&offset=0&show_dir=1&limit=1000" \
        -H "Cookie: $cookie" \
        -H "User-Agent: Mozilla/5.0 115Browser/27.0.5.7" \
        -H "Content-Type: application/x-www-form-urlencoded")
    
    # 检查响应是否成功
    if ! echo "$response" | jq -e '.state==true' > /dev/null; then
        log "获取文件列表失败: $response" >&2
        return 1
    fi
    
    # 查找所有同名文件
    local found_match=false
    local delete_list=""
    local keep_size=""
    local keep_id=""
    
    # 使用jq直接处理所有匹配的文件
    while read -r file_size file_id; do
        if [ -z "$file_size" ] || [ -z "$file_id" ]; then
            continue
        fi
        
        if [ -z "$keep_id" ]; then
            # 第一个文件，先保存
            keep_size="$file_size"
            keep_id="$file_id"
            # 如果大小匹配，标记found_match
            [ "$file_size" = "$local_size" ] && found_match=true
        else
            if [ "$file_size" = "$local_size" ] && [ "$found_match" = "false" ]; then
                # 找到大小匹配的文件，删除之前保存的
                delete_list="$delete_list $keep_id"
                keep_size="$file_size"
                keep_id="$file_id"
                found_match=true
            else
                # 其他文件都加入删除列表
                delete_list="$delete_list $file_id"
            fi
        fi
    done < <(echo "$response" | jq -r --arg name "$file_name" \
        '.data[] | select(.n==$name) | "\(.s) \(.fid)"')
    
    # 如果没有找到任何文件
    if [ -z "$keep_id" ]; then
        echo "0"
        return 0
    fi
    
    # 移动不需要的文件到回收站
    if [ ! -z "$delete_list" ]; then
        for fid in $delete_list; do
            if [ ! -z "$fid" ]; then
                log "移动文件到回收站: file_id=$fid" >&2
                local delete_response=$(curl -s "https://webapi.115.com/rb/delete" \
                    -H "Cookie: $cookie" \
                    -H "User-Agent: Mozilla/5.0 115Browser/27.0.5.7" \
                    -H "Content-Type: application/x-www-form-urlencoded" \
                    -d "fid[0]=$fid")
                
                if echo "$delete_response" | jq -e '.state==true' > /dev/null; then
                    log "成功移动文件到回收站: $fid" >&2
                else
                    log "移动文件到回收站失败: $delete_response" >&2
                fi
            fi
        done
    fi
    
    # 返回保留的文件大小
    log "保留文件: $file_name (大小: $keep_size)" >&2
    echo "$keep_size"
    return 0
}

# 检查是否需要更新
check_update_needed() {
    local image=$1
    local arch=$2
    local image_check="${image}-${arch}"
    local update_needed=0
    
    # 先检查镜像是否存在
    if ! docker image inspect "$image" >/dev/null 2>&1; then
        log "镜像不存在，需要下载"
        # 尝试下载镜像，最多重试3次
        local retry_count=0
        local max_retries=3
        
        while [ $retry_count -lt $max_retries ]; do
            # 捕获docker pull的输出以提取SHA值
            local pull_output
            pull_output=$(docker pull --platform linux/"$arch" "$image" 2>&1)
            local pull_status=$?
            
            if [ $pull_status -eq 0 ]; then
                log "下载镜像 $image 成功"
                
                # 从输出中提取SHA值
                local sha_value
                sha_value=$(echo "$pull_output" | grep -o "Digest: sha256:[a-f0-9]*" | cut -d':' -f3)
                
                if [ ! -z "$sha_value" ]; then
                    # 保存SHA值到文件
                    echo "${image}-${arch} ${sha_value}" >> "${WORK_DIR}/ailg_sha_local.txt"
                    log "已保存SHA值: ${image}-${arch} ${sha_value}"
                else
                    log "警告：无法从拉取输出中提取SHA值"
                fi
                
                docker tag "$image" "$image_check"  # 保存一个副本用于比较
                return 0  # 返回0表示需要更新（导出并上传）
            fi
            
            ((retry_count++))
            if [ $retry_count -lt $max_retries ]; then
                log "下载镜像 $image 失败，第 $retry_count 次重试..."
                sleep 3  # 失败后等待3秒再重试
            else
                log "下载镜像 $image 失败，已达到最大重试次数"
            fi
        done
        
        return 1  # 返回1表示处理失败
    fi
    
    # 对于已存在的镜像，根据标签类型检查更新
    if [[ $image == *":latest" ]]; then
        check_image "$image" "latest" "$arch"
        return $?
    elif [[ $image == *":hostmode" ]]; then
        check_image "$image" "hostmode" "$arch"
        return $?
    elif [[ $image == *":latest-rockchip" ]]; then
        check_image "$image" "latest-rockchip" "$arch"
        return $?
    fi
    
    # 固定版本的镜像，已存在则无需更新
    return 2  # 返回2表示无需更新
}

# 检查镜像是否需要更新
check_image() {
    local image=$1
    local tag=$2
    local arch=$3
    local image_check="${image}-${arch}"
    local org_name=$(echo $image | cut -d'/' -f1)
    local img_name=$(echo $image | cut -d'/' -f2 | cut -d':' -f1)
    
    log "检查 $image (架构: $arch) 是否为最新版本..."
    
    # 获取远程镜像SHA
    local remote_sha
    remote_sha=$(curl -s -m 20 "https://hub.docker.com/v2/repositories/${org_name}/${img_name}/tags/${tag}" | grep -oE '[0-9a-f]{64}' | tail -1)
    if [ -z "$remote_sha" ]; then
        log "无法获取远程镜像SHA"
        return 1
    fi
    
    # 从文件中获取本地SHA
    local local_sha=""
    if [ -f "${WORK_DIR}/ailg_sha_local.txt" ]; then
        local_sha=$(grep "${image}-${arch}" "${WORK_DIR}/ailg_sha_local.txt" | awk '{print $2}' | tail -1)
    fi
    
    # 如果文件中没有找到SHA，尝试从docker inspect获取（仅适用于与主机架构相同的镜像）
    if [ -z "$local_sha" ] && [ "$arch" = "$(uname -m | sed 's/x86_64/amd64/' | sed 's/aarch64/arm64/')" ]; then
        if docker image inspect "$image_check" >/dev/null 2>&1; then
            local_sha=$(docker inspect -f'{{index .RepoDigests 0}}' "$image_check" 2>/dev/null | cut -f2 -d: || echo "")
            
            # 如果成功获取到SHA，保存到文件中
            if [ ! -z "$local_sha" ]; then
                echo "${image}-${arch} ${local_sha}" >> "${WORK_DIR}/ailg_sha_local.txt"
                log "已从docker inspect获取并保存SHA值: ${image}-${arch} ${local_sha}"
            fi
        fi
    fi
    
    log "远程SHA: $remote_sha, 本地SHA: $local_sha"
    
    if [ -z "$local_sha" ] || [ "$local_sha" != "$remote_sha" ]; then
        log "发现新版本，正在更新..."
        
        # 如果本地存在镜像，先标记为旧版本
        if docker image inspect "$image_check" >/dev/null 2>&1; then
            docker tag "$image_check" "${image_check}-old"
            docker rmi "$image_check" >/dev/null 2>&1
        fi
        
        local retry_count=0
        while [ $retry_count -lt 3 ]; do
            # 捕获docker pull的输出以提取SHA值
            local pull_output
            pull_output=$(docker pull --platform linux/"$arch" "$image" 2>&1)
            local pull_status=$?
            
            if [ $pull_status -eq 0 ]; then
                log "更新成功"
                
                # 从输出中提取SHA值
                local new_sha
                new_sha=$(echo "$pull_output" | grep -o "Digest: sha256:[a-f0-9]*" | cut -d':' -f3)
                
                if [ ! -z "$new_sha" ]; then
                    # 更新SHA值到文件
                    sed -i "\|${image}-${arch}|d" "${WORK_DIR}/ailg_sha_local.txt" 2>/dev/null || true
                    echo "${image}-${arch} ${new_sha}" >> "${WORK_DIR}/ailg_sha_local.txt"
                    log "已更新SHA值: ${image}-${arch} ${new_sha}"
                else
                    log "警告：无法从拉取输出中提取SHA值"
                fi
                
                docker tag "$image" "$image_check"
                
                # 如果存在旧版本，删除它
                if docker image inspect "${image_check}-old" >/dev/null 2>&1; then
                    docker rmi "${image_check}-old" >/dev/null 2>&1
                fi
                
                return 0
            fi
            
            ((retry_count++))
            log "下载失败，第 $retry_count 次重试..."
            sleep 3
        done
        
        log "更新失败，回滚到旧版本"
        if docker image inspect "${image_check}-old" >/dev/null 2>&1; then
            docker tag "${image_check}-old" "$image_check"
            docker rmi "${image_check}-old" >/dev/null 2>&1
        fi
        return 1
    else
        log "已是最新版本"
        docker tag "$image_check" "$image"
        return 2
    fi
}

# 验证导出的镜像文件
verify_exported_image() {
    local file=$1
    
    # 使用 file 命令检查文件类型和完整性
    local file_info=$(file "$file")
    
    # 检查是否为gzip文件
    if ! echo "$file_info" | grep -q "gzip compressed data"; then
        log "错误：不是有效的gzip压缩文件"
        return 1
    fi
    
    # 检查是否被截断
    if echo "$file_info" | grep -q "truncated"; then
        log "错误：文件不完整（被截断）"
        return 1
    fi
    
    # 检查gzip文件的完整性
    if ! gunzip -t "$file" 2>/dev/null; then
        log "错误：gzip文件损坏"
        return 1
    fi
    
    # 检查tar文件的内容和manifest.json
    local file_list=$(gunzip -c "$file" | tar -tf - 2>/dev/null)
    if [ $? -ne 0 ] || [ -z "$file_list" ]; then
        log "错误：tar文件是空的或无法读取内容"
        return 1
    fi
    
    if ! echo "$file_list" | grep -q "manifest.json"; then
        log "错误：镜像文件缺少必要的manifest.json文件"
        return 1
    fi
    
    log "镜像文件验证通过"
    return 0
}

# 处理单个镜像的函数
process_image() {
    local image=$1
    local arch=$2
    local filename=$(echo "$image" | tr '/:' '.' | sed 's/\.$//')".$arch.tar.gz"
    local output_path="${IMAGES_DIR}/${filename}"
    
    log "=================================================="
    log "正在处理 $image (架构: $arch)"
    log "=================================================="
    # 检查是否需要更新
    check_update_needed "$image" "$arch"
    local update_needed=$?
    
    if [ $update_needed -eq 1 ]; then
        log "处理镜像 $image 失败，跳过"
        return
    fi
    
    # 检查是否需要处理（更新或上传）
    local remote_size=0
    log "检查是否需要更新: update_needed=$update_needed"
    if [ $update_needed -eq 2 ]; then
        local local_size=0
        if [ -f "$output_path" ]; then
            local_size=$(stat -c%s "$output_path" 2>/dev/null || stat -f%z "$output_path")
            log "本地文件存在，大小为: $local_size ，output_path=$output_path"
        else
            log "本地文件不存在"
        fi
        
        # 检查远程文件是否存在
        log "开始检查远程文件..."
        log "检查远程文件是否存在: FOLDER_ID=$FOLDER_ID, filename=$filename, local_size=$local_size"
        remote_size=$(check_115_file_exists "$COOKIE" "$FOLDER_ID" "$filename" "$local_size" || echo "1")
        log "远程文件与本地大小: remote_size=$remote_size ，local_size=$local_size"
        
        # 如果远程文件存在且大小相同，则无需处理
        if [ "$remote_size" != "0" ] && [ "$local_size" != "0" ] && [ "$remote_size" = "$local_size" ]; then
            log "远程文件存在且大小相同"
            if [ $update_needed -eq 2 ]; then
                log "本地是最新版本且远程文件已存在，无需处理"
                return
            fi
        elif [ "$remote_size" = "1" ]; then
            log "未能获取远程文件大小，跳过本轮处理"
            return
        else
            log "需要处理: remote_size=$remote_size, local_size=$local_size, update_needed=$update_needed"
        fi
    fi
    
    # 如果本地已有文件，先备份再导出新的
    if [ -f "$output_path" ]; then
        log "本地已有镜像文件，先进行备份"
        backup_image_file "$filename"
        log "删除旧的镜像文件"
        rm -f "$output_path"
    fi
    
    # 导出新镜像
    log "正在导出 $image"
    if ! docker save "$image" | gzip > "$output_path"; then
        log "导出 $image 失败"
        [ -f "$output_path" ] && rm -f "$output_path"
        return
    fi
    log "成功导出到: $output_path"
    
    # 验证文件
    if ! verify_exported_image "$output_path"; then
        log "镜像文件验证失败，重试..."
        rm -f "$output_path"
        
        # 重新导出一次
        if ! docker save "$image" | gzip > "$output_path" || ! verify_exported_image "$output_path"; then
            log "重新导出镜像仍然失败，跳过处理"
            [ -f "$output_path" ] && rm -f "$output_path"
            return
        fi
    fi
    
    # 设置文件权限
    chmod 777 "$output_path"
    log "已设置文件权限"
    
    # 上传到115网盘
    if ! upload_to_115 "$COOKIE" "$FOLDER_ID" "$output_path"; then
        log "上传镜像文件 $filename 到115网盘失败"
        return
    fi
    
    log "镜像 $image 处理完成"
    log "=================================================="
}

# 检查115 cookie是否有效
check_115_cookie() {
    local cookie=$1
    local user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/********-b478491100 Safari/537.36 Channel/pckk_other_ch"
    
    # 先获取根目录文件列表
    local list_response=$(curl -s "https://webapi.115.com/files?aid=1&cid=0&o=user_ptime&asc=0&offset=0&show_dir=1&limit=1" \
        -H "Cookie: $cookie" \
        -H "User-Agent: $user_agent" \
        -H "Referer: https://appversion.115.com/1/web/1.0/api/chrome")
    
    # 检查是否能访问
    if ! echo "$list_response" | grep -q '"state":true'; then
        log "Cookie无效：无法访问文件列表" >&2
        return 1
    fi
    
    # 递归查找第一个小文件（<100MB）
    find_first_small_file() {
        local cookie=$1
        local cid=$2
        local user_agent=$3
        
        local files_response=$(curl -s "https://webapi.115.com/files?aid=1&cid=$cid&o=user_ptime&asc=0&offset=0&show_dir=1&limit=1000" \
            -H "Cookie: $cookie" \
            -H "User-Agent: $user_agent" \
            -H "Referer: https://appversion.115.com/1/web/1.0/api/chrome")
        
        # 检查响应是否有效
        if ! echo "$files_response" | jq -e . >/dev/null 2>&1; then
            log "获取文件列表响应无效" >&2
            return 1
        fi
        
        if ! echo "$files_response" | grep -q '"state":true'; then
            log "获取文件列表失败" >&2
            return 1
        fi
        
        # 先查找小文件（<100MB）
        local file_info=$(echo "$files_response" | jq -r '.data[] | select(.fid != null and (.s|tonumber? // 0) < 104857600) | .pc' | head -n 1)
        if [ ! -z "$file_info" ] && [ "$file_info" != "null" ]; then
            echo "$file_info"
            return 0
        fi
        
        # 如果没有小文件，递归查找目录
        local first_dir=$(echo "$files_response" | jq -r '.data[] | select(.cid != null) | .cid' | head -n 1)
        if [ ! -z "$first_dir" ] && [ "$first_dir" != "null" ]; then
            find_first_small_file "$cookie" "$first_dir" "$user_agent"
            return $?
        fi
        
        return 1
    }
    
    # 从根目录开始查找小文件
    local pick_code=$(find_first_small_file "$cookie" "0" "$user_agent")
    if [ $? -ne 0 ]; then
        # 如果找不到小文件，只要能访问文件列表就认为cookie有效
        log "未找到合适的测试文件，但cookie可以访问文件列表" >&2
        return 0
    fi
    
    # 尝试获取文件的下载链接
    local download_response=$(curl -s "https://webapi.115.com/files/download?pickcode=$pick_code" \
        -H "Cookie: $cookie" \
        -H "User-Agent: $user_agent" \
        -H "Referer: https://appversion.115.com/1/web/1.0/api/chrome")
    
    # 检查响应是否有效
    if ! echo "$download_response" | jq -e . >/dev/null 2>&1; then
        log "获取下载链接响应无效" >&2
        return 1
    fi
    
    # 检查响应
    if echo "$download_response" | jq -e '.state==true or (.state==false and .is_vip != null)' > /dev/null; then
        log "Cookie有效：可以访问文件系统" >&2
        return 0
    fi
    
    log "Cookie无效：无法正确访问文件系统" >&2
    return 1
}

# 获取有效的115 cookie
get_valid_115_cookie() {
    local db_path=$1
    local storage_id=$2
    local cookie_file=$3
    
    # 首先尝试从数据库获取cookie
    local cookie=$(get_115_cookie "$db_path" "$storage_id")
    if [ $? -eq 0 ] && [ ! -z "$cookie" ]; then
        # 检查从数据库获取的cookie是否有效
        if check_115_cookie "$cookie"; then
            log "使用数据库中的有效cookie" >&2  # 重定向到标准错误输出
            echo "$cookie"
            return 0
        fi
        log "数据库中的cookie已失效" >&2  # 重定向到标准错误输出
    fi
    
    # 如果数据库中的cookie无效，尝试从文件获取
    if [ -f "$cookie_file" ]; then
        cookie=$(cat "$cookie_file")
        if [ ! -z "$cookie" ] && check_115_cookie "$cookie"; then
            log "使用文件中的有效cookie" >&2  # 重定向到标准错误输出
            echo "$cookie"
            return 0
        fi
        log "文件中的cookie已失效" >&2  # 重定向到标准错误输出
    fi
    
    log "无法获取有效的115 cookie" >&2  # 重定向到标准错误输出
    return 1
}

# 取消115分享
cancel_115_share() {
    local cookie=$1
    local share_code=$2
    
    log "=================================================="
    log "取消旧的115分享: $share_code"
    log "=================================================="
    
    local response=$(curl -s "https://webapi.115.com/share/updateshare" \
        -H "Cookie: $cookie" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 115Browser/35.8.0.1" \
        -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
        -H "Origin: https://115.com" \
        -H "Referer: https://115.com/" \
        --data-urlencode "share_code=$share_code" \
        --data-urlencode "action=cancel")
        
    if ! echo "$response" | jq -e '.state==true' > /dev/null; then
        log "取消分享失败: $response"
        return 1
    fi
    
    log "旧分享已成功取消"
    return 0
}

# 更新镜像主函数
update_images() {
    local arch=$1
    shift
    local custom_images=("$@")
    
    log "=================================================="
    log "开始检查镜像更新..."
    log "=================================================="
    mkdir -p "$IMAGES_DIR"
    
    # 创建SHA文件（如果不存在）
    touch "${WORK_DIR}/ailg_sha_local.txt"
    
    # 清理镜像
    docker images | grep "<none>" | awk '{print $3}' | xargs -r docker rmi
    
    # 获取有效的115 cookie
    COOKIE=$(get_valid_115_cookie "$ALIST_DB_PATH" "$ALIST_STORAGE_ID" "$COOKIE_FILE")
    if [ $? -ne 0 ]; then
        log "无法获取有效的115 cookie，退出"
        return 1
    fi
    
    # 提取user_id
    USER_ID=$(extract_user_id "$COOKIE")
    if [ $? -ne 0 ]; then
        log "无法提取user_id，退出"
        return 1
    fi
    
    # 检查并创建目录
    FOLDER_ID=$(check_115_directory "$COOKIE" "$IMAGES_REMOTE_DIR")
    if [ $? -ne 0 ]; then
        log "目录不存在，创建新目录"
        FOLDER_ID=$(create_115_directory "$COOKIE" "$IMAGES_REMOTE_DIR")
        if [ $? -ne 0 ]; then
            log "创建目录失败，退出"
            return 1
        fi
    fi
    
    # 处理自定义镜像数组
    if [ ${#custom_images[@]} -gt 0 ]; then
        log "=================================================="
        log "处理自定义镜像..."
        log "=================================================="
        ARCH="$arch"
        NEED_UPDATE_SHARE=false
        for image in "${custom_images[@]}"; do
            process_image "$image" "$ARCH"
        done
    else
        # 处理预定义的AMD64镜像
        log "=================================================="
        log "处理 AMD64 架构镜像..."
        log "=================================================="
        ARCH="amd64"
        NEED_UPDATE_SHARE=false
        for image in "${!IMAGES[@]}"; do
            process_image "${IMAGES[$image]}" "$ARCH"
        done
        
        # 处理预定义的ARM64镜像
        log "=================================================="
        log "处理 ARM64 架构镜像..."
        log "=================================================="
        ARCH="arm64"
        for image in "${!ARM64_IMAGES[@]}"; do
            process_image "${ARM64_IMAGES[$image]}" "$ARCH"
        done
    fi
    
    # 更新docker_list.txt（仅针对预定义镜像）
    if [ ${#custom_images[@]} -eq 0 ]; then
        : > "${WORK_DIR}/115/docker_list.txt"
        for image in "${!IMAGES[@]}"; do
            local filename=$(echo "${IMAGES[$image]}" | tr '/:' '.' | sed 's/\.$//')".$ARCH.tar.gz"
            echo "${IMAGES[$image]} amd64 $filename" >> "${WORK_DIR}/115/docker_list.txt"
        done
        for image in "${!ARM64_IMAGES[@]}"; do
            local filename=$(echo "${ARM64_IMAGES[$image]}" | tr '/:' '.' | sed 's/\.$//')".$ARCH.tar.gz"
            echo "${ARM64_IMAGES[$image]} arm64 $filename" >> "${WORK_DIR}/115/docker_list.txt"
        done
    fi
    
    # 只有在有文件更新时才创建新分享
    if [ "$NEED_UPDATE_SHARE" = true ]; then
        log "=================================================="
        log "检测到文件更新，重新创建115分享..."
        log "=================================================="
        
        # 创建新分享
        local share_info=$(create_115_share "$COOKIE" "$USER_ID" "$FOLDER_ID")
        if [ $? -ne 0 ]; then
            log "创建分享失败，退出"
            return 1
        fi
        
        # 提取分享信息
        local share_code=$(echo "$share_info" | awk '{print $1}')  # 提取第一个字段作为分享码
        local receive_code=$(echo "$share_info" | awk '{print $NF}')  # 提取最后一个字段作为提取码
        
        if [ -z "$share_code" ] || [ -z "$receive_code" ]; then
            log "无法正确解析分享码和提取码"
            return 1
        fi
        
        # 检查分享码和提取码格式
        if ! [[ "$share_code" =~ ^[a-z0-9]+$ ]] || ! [[ "$receive_code" =~ ^[a-z0-9]+$ ]]; then
            log "分享码或提取码格式不正确"
            log "分享码: $share_code"
            log "提取码: $receive_code"
            return 1
        fi

        # 创建分享前，先取消旧的分享
        if [ -f "/www/data/115/my_115_share.txt" ]; then
            local old_share_code=$(head -n 1 "/www/data/115/my_115_share.txt" | awk '{print $1}')
            if [ ! -z "$old_share_code" ]; then
                if ! cancel_115_share "$COOKIE" "$old_share_code"; then
                    log "取消旧分享失败，但继续创建新分享"
                fi
            fi
        fi
        
        # 保存分享信息到文件
        mkdir -p "/www/data/115"  # 先创建目录
        echo "${share_code} ${receive_code}" > "/www/data/115/my_115_share.txt"
        log "分享信息已保存到 /www/data/115/my_115_share.txt"
    else
        log "=================================================="
        log "所有文件均未发生变化，无需更新分享"
        log "=================================================="
    fi
    
    # # 刷新Alist token
    # if ! refresh_token; then
    #     log "刷新token失败，退出"
    #     return 1
    # fi
    
    # # 删除旧的存储
    # if check_alist_storage "$TOKEN" "$ALIST_STORAGE_ID"; then
    #     if ! delete_alist_storage "$TOKEN" "$ALIST_STORAGE_ID"; then
    #         log "删除旧存储失败，继续尝试更新"
    #     fi
    # fi
    
    # # 创建新的存储
    # if ! update_alist_storage "$TOKEN" "$ALIST_STORAGE_ID" "$COOKIE" "$FOLDER_ID" "$share_code" "$receive_code" "create"; then
    #     log "创建新存储失败，退出"
    #     return 1
    # fi
    log "=================================================="
    log "所有镜像更新检查完成"
    log "=================================================="
}

# 主循环
main() {
    local image_check_counter=0
    while true; do
        log "=================================================="
        log "开始新一轮检查"
        log "=================================================="
        
        # 检查并轮转日志
        rotate_logs
        
        # 刷新 token
        if ! refresh_token; then
            log "等待下一轮检查..."
            sleep 1800
            continue
        fi
        
        # 执行镜像更新
        update_images "amd64" "${IMAGES[@]}" "${ARM64_IMAGES[@]}"
        
        log "=================================================="
        log "等待6小时后进行下一次检查..."
        log "=================================================="
        sleep 21600
    done
}

# 备份镜像文件
backup_image_file() {
    local filename=$1
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="${IMAGES_DIR}/backup_${timestamp}"
    
    log "开始备份镜像文件到 $backup_dir"
    mkdir -p "$backup_dir"
    
    if [ -f "${IMAGES_DIR}/${filename}" ]; then
        cp "${IMAGES_DIR}/${filename}" "${backup_dir}/"
        log "已备份 $filename"
        
        # 保留最新的3个备份
        ls -td ${IMAGES_DIR}/backup_* | tail -n +$((MAX_BACKUPS + 1)) | xargs -r rm -rf
        return 0
    else
        log "警告：文件 ${IMAGES_DIR}/${filename} 不存在，无法备份"
        rm -rf "$backup_dir"
        return 1
    fi
}

# 彻底删除回收站中的文件
clean_recycle() {
    local cookie=$1
    local rid=$2
    local password=$3
    
    log "彻底删除回收站文件: $rid"
    
    local response=$(curl -s "https://webapi.115.com/rb/clean" \
        -H "Cookie: $cookie" \
        -H "User-Agent: Mozilla/5.0 115Browser/27.0.5.7" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "rid[0]=$rid" \
        -d "password=$password")
        
    if ! echo "$response" | jq -e '.state==true' > /dev/null; then
        log "彻底删除文件失败: $response"
        return 1
    fi
    
    log "文件已彻底删除"
    return 0
}

# 启动脚本
main 