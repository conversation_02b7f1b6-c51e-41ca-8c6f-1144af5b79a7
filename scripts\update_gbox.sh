#!/bin/bash
exec > >(tee -a /update_gbox/auto_update.log) 2>&1
echo "本次自动更新时间: $(date '+%Y-%m-%d %H:%M:%S')"
list=$(curl -s --unix-socket /var/run/docker.sock http://localhost/containers/json?all=true)
docker_name=$(echo $list | jq -r '.[] | select(.Image == "ailg/g-box:hostmode") | .Names[0] | ltrimstr("/")')
curl -s --unix-socket /var/run/docker.sock -X DELETE "http://localhost/containers/${docker_name}?force=true"

if [ -n "${1}" ]; then
    curl --unix-socket /var/run/docker.sock -X POST "http://localhost/images/ailg/g-box:hostmode/tag?repo=ailg/g-box&tag=old"
    for i in {1..3}; do
        echo "正在下载镜像，第 $i 次..."
        curl -s --unix-socket /var/run/docker.sock -X POST "http://localhost/images/create?fromImage=ailg/g-box:hostmode"
        
        local_sha=$(curl -s --unix-socket /var/run/docker.sock "http://localhost/images/ailg/g-box:hostmode/json" | jq -r '.RepoDigests[0]' | cut -f2 -d:)
        if [ -n "$local_sha" ]; then
            echo "镜像下载成功"
			curl -s -X DELETE --unix-socket /var/run/docker.sock "http://localhost/images/ailg/g-box:old?force=true"
            break
        else
            echo "第 $i 次镜像下载失败"
        fi
		
        if [ $i -eq 3 ]; then
            echo -e "[\033[1;31mERROR\033[0m] 镜像下载失败，请检查网络连接，将为你恢复旧版本容器，本次更新失败！"
            curl -s --unix-socket /var/run/docker.sock -X POST "http://localhost/images/ailg/g-box:old/tag?repo=ailg/g-box&tag=hostmode"
            exit 1
        fi
    done
fi
MOUNTS_STR=$(cat /update_gbox/mounts.bind)
echo -e "MOUNTS_STR:${MOUNTS_STR}"
curl -s --unix-socket /var/run/docker.sock \
    -X POST "http://localhost/containers/create?name=g-box" \
    -H "Content-Type: application/json" \
    -d '{
        "Image": "ailg/g-box:hostmode",
        "HostConfig": {
            "NetworkMode": "host",
            "Binds": ['$MOUNTS_STR']
        }
    }'

curl -s --unix-socket /var/run/docker.sock -X POST "http://localhost/containers/g-box/start"
rm -f /update_gbox/mounts.bind /update_gbox/data.db*
echo -e "[\033[1;32mINFO\033[0m]update complete--自动更新完成！"
curl -s --unix-socket /var/run/docker.sock -X DELETE "http://localhost/containers/update-gbox?force=true"