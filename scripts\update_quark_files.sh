#!/bin/bash

# 配置参数
ALIST_URL="http://127.0.0.1:5244"
mkdir -p /www/data
WORK_DIR="/www/data"
LOG_DIR="/www/data"
MAX_LOGS=10
MAX_BACKUPS=3
FILES=("version.txt" "index.zip" "update.zip" "tvbox.zip" "115share_list.txt")

# 附加目录配置
THUNDER_DIR="/thunder/迅雷分享/小雅A-list启动4文件"

# 下载源
SOURCES=(
    "http://docker.xiaoya.pro/update"
    "https://raw.githubusercontent.com/xiaoyaliu00/data/main"
    "https://raw.githubusercontent.com/xiaoyaDev/data/main"
    "http://har01d.org"
)

# AMD64 镜像列表
declare -A IMAGES=(
    [1]="ailg/g-box:hostmode"
    [2]="ailg/ggbond:latest"
    [3]="emby/embyserver:*******"
    [4]="emby/embyserver:********"
    [5]="jellyfin/jellyfin:10.9.6"
    [6]="nyanmisaka/jellyfin:latest"
    [7]="ddsderek/xiaoya-emd:latest"
    [8]="cloudnas/clouddrive2:latest"
)

# ARM64 镜像列表
declare -A ARM64_IMAGES=(
    [1]="ailg/g-box:hostmode"
    [2]="ailg/ggbond:latest"
    [3]="emby/embyserver_arm64v8:*******"
    [4]="emby/embyserver_arm64v8:********"
    [5]="jellyfin/jellyfin:10.9.6"
    [6]="nyanmisaka/jellyfin:latest-rockchip"
    [7]="ddsderek/xiaoya-emd:latest"
    [8]="cloudnas/clouddrive2:latest"
)

IMAGES_DIR="${WORK_DIR}/images"
IMAGES_REMOTE_DIR="/gbox常用镜像"
ARCH="amd64"

# 全局变量，存储token的过期时间
TOKEN_EXPIRE_TIME=0

# 统一日志文件名
LOG_FILE="${LOG_DIR}/update.log"

# 统一日志函数
log() {
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] $1" | tee -a "$LOG_FILE"
}

# 改进日志轮转
rotate_logs() {
    local today=$(date +%Y-%m-%d)
    local current_size=$(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE")
    
    # 只有当日志文件大于 1MB 时才进行轮转
    if [ -s "$LOG_FILE" ] && [ $current_size -gt 1048576 ]; then
        # 添加时间戳，避免同一天多次轮转时覆盖
        local timestamp=$(date +%H%M%S)
        mv "$LOG_FILE" "${LOG_DIR}/update-${today}-${timestamp}.log"
        touch "$LOG_FILE"
        
        # 保留最新的 MAX_LOGS 个日志文件
        ls -t ${LOG_DIR}/update-*.log | tail -n +$((MAX_LOGS + 1)) | xargs -r rm
    fi
}

# 备份文件
backup_files() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="${WORK_DIR}/backup_${timestamp}"
    
    log "开始备份文件到 $backup_dir"
    mkdir -p "$backup_dir"
    
    for file in "${FILES[@]}"; do
        if [ -f "${WORK_DIR}/${file}" ]; then
            cp "${WORK_DIR}/${file}" "${backup_dir}/"
            log "已备份 $file"
        else
            log "警告：文件 $file 不存在，无法备份"
            rm -rf "$backup_dir"
            return 1
        fi
    done
    
    # 保留最新的3个备份
    ls -td ${WORK_DIR}/backup_* | tail -n +$((MAX_BACKUPS + 1)) | xargs -r rm -rf
    return 0
}

# 从最新备份恢复文件
restore_from_backup() {
    local latest_backup=$(ls -td ${WORK_DIR}/backup_* | head -n 1)
    
    if [ -z "$latest_backup" ]; then
        log "错误：没有找到可用的备份"
        return 1
    fi
    
    log "从备份 $latest_backup 恢复文件"
    for file in "${FILES[@]}"; do
        if [ -f "${latest_backup}/${file}" ]; then
            cp "${latest_backup}/${file}" "${WORK_DIR}/${file}"
            log "已恢复 $file"
        else
            log "错误：备份中不存在文件 $file"
            return 1
        fi
    done
    return 0
}

# 检查远程版本
check_remote_version() {
    local success=false
    
    for source in "${SOURCES[@]}"; do
        log "尝试从 $source 获取版本信息"
        if wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" \
            -T 30 -t 2 "$source/version.txt" -O "${WORK_DIR}/version.txt.new" >> "${LOG_FILE}" 2>&1; then
            success=true
            break
        fi
    done
    
    if [ "$success" = false ]; then
        log "错误：无法获取远程版本信息"
        rm -f "${WORK_DIR}/version.txt.new"
        return 1
    fi
    return 0
}

# 下载文件
download_file() {
    local file=$1
    local success=false
    
    for source in "${SOURCES[@]}"; do
        log "尝试从 $source 下载 $file"
        if wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" \
            -T 30 -t 2 "$source/$file" -O "${WORK_DIR}/${file}.tmp" >> "${LOG_FILE}" 2>&1; then
            mv "${WORK_DIR}/${file}.tmp" "${WORK_DIR}/${file}"
            log "成功下载 $file"
            success=true
            break
        fi
    done
    
    if [ "$success" = false ]; then
        log "错误：所有源都无法下载 $file"
        rm -f "${WORK_DIR}/${file}.tmp"
        return 1
    fi
    return 0
}

# 删除远程文件
delete_remote_file() {
    local file=$1
    local dir=${2:-"/quark"}  # 默认目录为 /quark
    
    log "删除远程文件 ${dir}/${file}"
    curl -s -X POST "${ALIST_URL}/api/fs/remove" \
        -H "Authorization: ${TOKEN}" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json, text/plain, */*" \
        -d "{
            \"dir\": \"${dir}\",
            \"names\": [\"${file}\"]
        }" >> "${LOG_FILE}" 2>&1
    
    # 使用 jq 精确检查文件是否存在
    local check_count=0
    while [ $check_count -lt 3 ]; do
        local list_response=$(curl -s -X POST "${ALIST_URL}/api/fs/list" \
            -H "Authorization: ${TOKEN}" \
            -H "Content-Type: application/json" \
            -d "{
                \"path\": \"${dir}\",
                \"refresh\": true
            }")
        
        # 使用 jq 检查特定文件是否在列表中
        local file_exists=$(echo "$list_response" | jq -r --arg fname "$file" '.data.content[] | select(.name == $fname) | .name')
        
        if [ -z "$file_exists" ]; then
            log "文件 $file 已确认删除"
            return 0
        else
            log "文件 $file 仍然存在，等待后重试..."
            sleep 5
            check_count=$((check_count + 1))
        fi
    done
    
    log "警告：文件 $file 在多次尝试后仍未删除"
    # 输出文件的详细信息以便调试
    echo "$list_response" | jq -r --arg fname "$file" '.data.content[] | select(.name == $fname)'
    return 1
}

# 上传文件到远程
upload_file() {
    local file=$1
    local dir=${2:-"/quark"}
    local file_path="${WORK_DIR}/${file}"
    local retry_count=0
    local max_retries=3
    
    # 如果是镜像文件，使用IMAGES_DIR
    if [[ $dir == "${IMAGES_REMOTE_DIR}" ]]; then
        file_path="${IMAGES_DIR}/${file}"
    fi
    
    # 检查文件是否存在
    if [ ! -f "$file_path" ]; then
        log "错误：文件不存在: $file_path"
        return 1
    fi
    
    # 获取文件大小用于日志
    local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path")
    log "开始上传文件 $file (大小: $file_size bytes)"
    
    while [ $retry_count -lt $max_retries ]; do
        log "上传文件 $file 到远程 $dir (第$((retry_count + 1))次尝试)"
        
        # 刷新 token
        if ! refresh_token; then
            log "刷新 token 失败"
            return 1
        fi
        
        # 使用新的上传方式
        if curl -s -X PUT "${ALIST_URL}/api/fs/form" \
            -H "Authorization: ${TOKEN}" \
            -H "File-Path: ${dir}/${file}" \
            -F "file=@${file_path}"; then
            log "上传成功"
            return 0
        fi
        
        log "上传失败，等待30秒后重试..."
        sleep 10
        ((retry_count++))
    done
    
    log "达到最大重试次数，上传失败"
    return 1
}

# 检查工作目录中的文件完整性
# check_files_integrity() {
#     for file in "${FILES[@]}"; do
#         if [ ! -f "${WORK_DIR}/${file}" ]; then
#             log "错误：文件 $file 不存在"
#             return 1
#         fi
#     done
#     return 0
# }      

# 获取或刷新token
refresh_token() {
    # 如果token未过期，直接返回
    local current_time=$(date +%s)
    if [ $current_time -lt $TOKEN_EXPIRE_TIME ]; then
        return 0
    fi
    
    # token已过期或未设置，重新获取
    
    TOKEN=$(curl -s -X POST "http://127.0.0.1:5244/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"admin\",\"password\":\"AQG7q5bfwEVq5201314@\"}" | jq -r '.data.token')
    if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
        log "获取token失败"
        return 1
    fi
    
    # 设置token过期时间（默认24小时）
    TOKEN_EXPIRE_TIME=$((current_time + 7200))
    return 0
}

# 检查远程文件是否完整
check_remote_files() {
    local missing_files=false
    local dir=${1:-"/quark"}
    
    # 刷新 token
    if ! refresh_token; then
        log "刷新 token 失败"
        return 2  # 改为返回 2 表示错误
    fi
    
    # 获取远程文件列表
    local list_response=$(curl -s -X POST "${ALIST_URL}/api/fs/list" \
        -H "Authorization: ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"path\": \"${dir}\",
            \"refresh\": true
        }")
    
    # 检查 curl 是否成功
    if [ $? -ne 0 ] || [ -z "$list_response" ]; then
        log "获取文件列表失败"
        return 2
    fi
    
    # 检查每个必需文件
    for file in "${FILES[@]}"; do
        # 跳过index.zip文件的检查
        if [ "$file" = "index.zip" ]; then
            log "跳过检查index.zip文件"
            continue
        fi
        
        local file_exists=$(echo "$list_response" | jq -r --arg fname "$file" '.data.content[] | select(.name == $fname) | .name')
        if [ -z "$file_exists" ]; then
            log "远程文件 ${dir}/${file} 不存在，需要更新"
            return 1  # 返回 1 表示需要更新
        fi
    done
    
    log "目录 ${dir} 中所有文件都存在"
    return 0  # 返回 0 表示文件完整
}

# 更新指定目录的文件
update_dir_files() {
    local dir=$1
    
    log "开始更新目录 ${dir} 的文件"
    
    # 更新远程文件
    local all_success=true
    for file in "${FILES[@]}"; do
        # 跳过115share_list.txt文件，因为已经单独处理过了
        if [ "$file" = "115share_list.txt" ]; then
            log "跳过115share_list.txt，已单独处理"
            continue
        fi
        
        # 跳过index.zip文件
        if [ "$file" = "index.zip" ]; then
            log "跳过index.zip文件，不进行远程更新"
            continue
        fi
        
        # 尝试删除文件
        if ! delete_remote_file "$file" "$dir"; then
            log "删除文件 ${dir}/${file} 失败，跳过后续操作"
            all_success=false
            break
        fi
        sleep 1

        # 尝试上传文件
        if ! upload_file "$file" "$dir"; then
            log "上传文件 ${dir}/${file} 失败，跳过后续操作"
            all_success=false
            break
        fi
        sleep 1
    done

    if [ "$all_success" = true ]; then
        log "目录 ${dir} 的文件更新成功"
        return 0
    else
        log "目录 ${dir} 的文件更新失败"
        return 1
    fi
}

# 主要更新流程
update_files() {
    # 检查是否是首次运行
    local first_run=false
    if [ ! -f "${WORK_DIR}/version.txt" ]; then
        first_run=true
        log "首次运行，将下载所有文件"
    fi
    
    # 单独处理115share_list.txt文件
    log "单独检查115share_list.txt文件更新"
    local share_list_file="115share_list.txt"
    local need_update_share_list=false
    
    # 下载远程115share_list.txt到临时文件
    local temp_share_list="${WORK_DIR}/${share_list_file}.tmp"
    local success=false
    
    for source in "${SOURCES[@]}"; do
        log "尝试从 $source 下载 $share_list_file"
        if wget --user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" \
            -T 30 -t 2 "$source/$share_list_file" -O "$temp_share_list" >> "${LOG_FILE}" 2>&1; then
            success=true
            break
        fi
    done
    
    if [ "$success" = true ]; then
        # 检查本地文件是否存在
        if [ ! -f "${WORK_DIR}/${share_list_file}" ]; then
            log "本地115share_list.txt不存在，需要更新"
            need_update_share_list=true
        else
            # 比较文件大小
            local remote_size=$(stat -f%z "$temp_share_list" 2>/dev/null || stat -c%s "$temp_share_list")
            local local_size=$(stat -f%z "${WORK_DIR}/${share_list_file}" 2>/dev/null || stat -c%s "${WORK_DIR}/${share_list_file}")
            
            if [ "$remote_size" != "$local_size" ]; then
                log "115share_list.txt文件大小不同，需要更新 (本地: $local_size, 远程: $remote_size)"
                need_update_share_list=true
            else
                log "115share_list.txt文件大小相同，无需更新"
                rm -f "$temp_share_list"
            fi
        fi
        
        # 如果需要更新，替换本地文件
        if [ "$need_update_share_list" = true ]; then
            log "正在更新115share_list.txt文件"
            mv -f "$temp_share_list" "${WORK_DIR}/${share_list_file}"
            chmod 777 "${WORK_DIR}/${share_list_file}"
            
            # 更新远程文件
            log "正在更新远程115share_list.txt文件"
            local update_115share_quark_success=false
            local update_115share_thunder_success=false
            if delete_remote_file "$share_list_file" "/quark"; then
                sleep 1
                if upload_file "$share_list_file" "/quark"; then
                    update_115share_quark_success=true
                else
                    log "更新/quark目录115share_list.txt文件失败，跳过更新"
                fi
                sleep 1
            else
                log "删除远程115share_list.txt文件失败，跳过更新"
            fi
            if delete_remote_file "$share_list_file" "$THUNDER_DIR"; then
                sleep 1
                if upload_file "$share_list_file" "$THUNDER_DIR"; then
                    update_115share_thunder_success=true
                else
                    log "更新$THUNDER_DIR目录115share_list.txt文件失败，跳过更新"
                fi
                sleep 1
            else
                log "删除远程115share_list.txt文件失败，跳过更新"
            fi
            
            log "115share_list.txt文件更新完成,/quark: $update_115share_quark_success, $THUNDER_DIR: $update_115share_thunder_success"
        fi
    else
        log "下载115share_list.txt失败，跳过更新"
        rm -f "$temp_share_list" 2>/dev/null
    fi
    
    # 获取远程版本
    if ! check_remote_version; then
        log "无法获取远程版本信息，退出更新"
        rm -f "${WORK_DIR}/version.txt.new" 2>/dev/null
        return 1
    fi
    
    # 检查是否需要更新
    local need_update=false
    local remote_version=$(cat "${WORK_DIR}/version.txt.new")
    
    if [ "$first_run" = true ]; then
        need_update=true
    else
        local local_version=$(cat "${WORK_DIR}/version.txt")
        
        if [ "$remote_version" != "$local_version" ]; then
            log "发现新版本：$remote_version，当前版本：$local_version"
            need_update=true
        else
            log "版本相同，检查远程文件完整性"
            # 检查各个目录中的文件是否完整
            check_remote_files "/quark"
            local quark_status=$?
            
            check_remote_files "$THUNDER_DIR"
            local thunder_status=$?
            
            log "检查状态 - /quark: $quark_status, $THUNDER_DIR: $thunder_status"
            
            if [ $quark_status -eq 1 ] || [ $thunder_status -eq 1 ]; then
                log "远程文件不完整，需要更新"
                need_update=true
            else
                log "所有远程文件完整，无需更新"
                rm -f "${WORK_DIR}/version.txt.new" 2>/dev/null
                return 0
            fi
        fi
    fi
    
    # 如果需要更新，先备份当前文件
    if [ "$need_update" = true ] && [ "$first_run" = false ]; then
        if ! backup_files; then
            log "备份失败，取消更新"
            rm -f "${WORK_DIR}/version.txt.new" 2>/dev/null
            return 1
        fi
    fi
    
    # 如果需要更新，下载所有文件
    if [ "$need_update" = true ]; then
        log "开始下载文件"
        local download_success=true
        for file in "${FILES[@]}"; do
            # 跳过115share_list.txt文件，因为已经单独处理过了
            if [ "$file" = "115share_list.txt" ]; then
                log "跳过115share_list.txt，已单独处理"
                continue
            fi
            
            # 跳过index.zip文件
            if [ "$file" = "index.zip" ]; then
                log "跳过index.zip文件，不进行下载"
                continue
            fi
            
            if ! download_file "$file"; then
                download_success=false
                break
            fi
        done
        
        # 如果下载失败，恢复备份并退出
        if [ "$download_success" = false ]; then
            log "下载失败，正在恢复备份"
            rm -f "${WORK_DIR}/version.txt.new" 2>/dev/null
            
            # 只有非首次运行才需要恢复备份
            if [ "$first_run" = false ]; then
                if ! restore_from_backup; then
                    log "严重错误：恢复备份失败"
                fi
            fi
            return 1
        fi
    fi
    
    # 设置文件权限
    for file in "${FILES[@]}"; do
        chmod 777 "${WORK_DIR}/${file}"
    done
    
    # 更新/quark目录
    log "开始更新 /quark 目录"
    local quark_success=true
    if ! update_dir_files "/quark"; then
        log "更新 /quark 目录失败"
        quark_success=false
    fi
    
    # 更新thunder目录
    log "开始更新 $THUNDER_DIR 目录"
    local thunder_success=true
    if ! update_dir_files "$THUNDER_DIR"; then
        log "更新 $THUNDER_DIR 目录失败"
        thunder_success=false
    fi
    
    # 如果两个目录都更新失败，恢复备份
    if [ "$quark_success" = false ] && [ "$thunder_success" = false ]; then
        log "所有目录更新失败，尝试恢复备份"
        rm -f "${WORK_DIR}/version.txt.new" 2>/dev/null
        
        if [ "$first_run" = false ]; then
            if ! restore_from_backup; then
                log "严重错误：恢复备份失败"
            fi
        fi
        return 1
    fi
    
    # 至少有一个目录更新成功，更新本地版本文件
    if [ -f "${WORK_DIR}/version.txt.new" ]; then
        mv "${WORK_DIR}/version.txt.new" "${WORK_DIR}/version.txt" 2>/dev/null
        log "更新完成，当前版本：$(cat ${WORK_DIR}/version.txt)"
    fi
    
    return 0
}

# 检查并更新镜像
check_image() {
    local image=$1
    local tag=$2
    local arch=$3
    local org_name=$(echo $image | cut -d'/' -f1)
    local img_name=$(echo $image | cut -d'/' -f2 | cut -d':' -f1)
    
    log "检查 $image (架构: $arch) 是否为最新版本..."
    
    # 获取远程镜像SHA
    local remote_sha
    remote_sha=$(curl -s -m 20 "https://hub.docker.com/v2/repositories/${org_name}/${img_name}/tags/${tag}" | grep -oE '[0-9a-f]{64}' | tail -1)
    if [ -z "$remote_sha" ]; then
        log "无法获取远程镜像SHA"
        return 1
    fi
    
    # 从文件中获取本地SHA
    local local_sha=""
    if [ -f "${WORK_DIR}/ailg_sha_local.txt" ]; then
        local_sha=$(grep "${image}-${arch}" "${WORK_DIR}/ailg_sha_local.txt" | awk '{print $2}' | tail -1)
    fi
    
    # 如果文件中没有找到SHA，尝试从docker inspect获取（仅适用于与主机架构相同的镜像）
    if [ -z "$local_sha" ] && [ "$arch" = "$(uname -m | sed 's/x86_64/amd64/' | sed 's/aarch64/arm64/')" ]; then
        if docker image inspect "$image_check" >/dev/null 2>&1; then
            local_sha=$(docker inspect -f'{{index .RepoDigests 0}}' "$image_check" 2>/dev/null | cut -f2 -d: || echo "")
            
            # 如果成功获取到SHA，保存到文件中
            if [ ! -z "$local_sha" ]; then
                echo "${image}-${arch} ${local_sha}" >> "${WORK_DIR}/ailg_sha_local.txt"
                log "已从docker inspect获取并保存SHA值: ${image}-${arch} ${local_sha}"
            fi
        fi
    fi
    
    log "远程SHA: $remote_sha, 本地SHA: $local_sha"
    
    if [ -z "$local_sha" ] || [ "$local_sha" != "$remote_sha" ]; then
        log "发现新版本，正在更新..."
        
        # 如果本地存在镜像，先标记为旧版本
        if docker image inspect "$image_check" >/dev/null 2>&1; then
            docker tag "$image_check" "${image_check}-old"
            docker rmi "$image_check" >/dev/null 2>&1
        fi
        
        local retry_count=0
        while [ $retry_count -lt 3 ]; do
            # 捕获docker pull的输出以提取SHA值
            local pull_output
            pull_output=$(docker pull --platform linux/"$arch" "$image" 2>&1)
            local pull_status=$?
            
            if [ $pull_status -eq 0 ]; then
                log "更新成功"
                
                # 从输出中提取SHA值
                local new_sha
                new_sha=$(echo "$pull_output" | grep -o "Digest: sha256:[a-f0-9]*" | cut -d':' -f3)
                
                if [ ! -z "$new_sha" ]; then
                    # 更新SHA值到文件
                    sed -i "\|${image}-${arch}|d" "${WORK_DIR}/ailg_sha_local.txt" 2>/dev/null || true
                    echo "${image}-${arch} ${new_sha}" >> "${WORK_DIR}/ailg_sha_local.txt"
                    log "已更新SHA值: ${image}-${arch} ${new_sha}"
                else
                    log "警告：无法从拉取输出中提取SHA值"
                fi
                
                docker tag "$image" "$image_check"
                
                # 如果存在旧版本，删除它
                if docker image inspect "${image_check}-old" >/dev/null 2>&1; then
                    docker rmi "${image_check}-old" >/dev/null 2>&1
                fi
                
                return 0
            fi
            
            ((retry_count++))
            log "下载失败，第 $retry_count 次重试..."
            sleep 3
        done
        
        log "更新失败，回滚到旧版本"
        if docker image inspect "${image_check}-old" >/dev/null 2>&1; then
            docker tag "${image_check}-old" "$image_check"
            docker rmi "${image_check}-old" >/dev/null 2>&1
        fi
        return 1
    else
        log "已是最新版本"
        docker tag "$image_check" "$image"
        return 2
    fi
}

# 备份镜像文件
backup_image_file() {
    local file=$1
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="${IMAGES_DIR}/backup_${timestamp}"
    
    mkdir -p "$backup_dir"
    if [ -f "${IMAGES_DIR}/${file}" ]; then
        cp "${IMAGES_DIR}/${file}" "${backup_dir}/"
        log "已备份镜像文件 $file"
        
        # 保留最新的3个备份
        ls -td ${IMAGES_DIR}/backup_* | tail -n +$((MAX_BACKUPS + 1)) | xargs -r rm -rf
        return 0
    fi
    return 1
}

# 获取远程文件信息
get_remote_file_info() {
    local file=$1
    local dir=${2:-"/quark"}
    
    # 将日志输出重定向到标准错误，这样不会影响函数返回值
    log "正在查询文件: ${dir}/${file}" >&2
    
    # 使用 list 接口获取目录内容
    local list_response=$(curl -s -X POST "${ALIST_URL}/api/fs/list" \
        -H "Authorization: ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"path\": \"${dir}\",
            \"refresh\": true
        }")
    
    # 从列表中提取指定文件的信息
    echo "$list_response" | jq -r --arg fname "$file" '.data.content[] | select(.name == $fname)'
}

# 检查远程文件是否存在
check_remote_file_exists() {
    local file=$1
    local dir=${2:-"/quark"}
    
    log "检查远程文件是否存在: ${dir}/${file}"
    
    # 先尝试使用list接口（更快）
    local list_response=$(curl -s -X POST "${ALIST_URL}/api/fs/list" \
        -H "Authorization: ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"path\": \"${dir}\",
            \"refresh\": true
        }")
    
    # 使用jq检查文件是否在列表中
    local file_exists=$(echo "$list_response" | jq -r --arg fname "$file" '.data.content[] | select(.name == $fname) | .name')
    if [ ! -z "$file_exists" ]; then
        log "文件存在 (通过 list 接口确认)"
        return 0
    fi
    
    # 如果list接口没找到，再用get接口确认（更准确但更慢）
    local get_response=$(curl -s -X POST "${ALIST_URL}/api/fs/get" \
        -H "Authorization: ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"path\": \"${dir}/${file}\"
        }")
    
    if [ "$(echo "$get_response" | jq -r '.code')" = "200" ]; then
        log "文件存在 (通过 get 接口确认)"
        return 0
    fi
    
    log "文件不存在"
    return 1
}

# 验证上传文件
verify_uploaded_file() {
    local file=$1
    local dir=${2:-"/quark"}
    local file_path="${WORK_DIR}/${file}"
    local retry_count=0
    local max_retries=3
    
    # 如果是镜像文件，使用IMAGES_DIR
    if [[ $dir == "${IMAGES_REMOTE_DIR}" ]]; then
        file_path="${IMAGES_DIR}/${file}"
    fi
    
    while [ $retry_count -lt $max_retries ]; do
        # 获取本地文件大小
        local local_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path")
        
        # 等待文件上传完成（增加等待时间）
        sleep 5
        
        # 获取远程文件信息并记录完整响应
        local remote_info=$(get_remote_file_info "$file" "$dir")
        log "远程文件信息: $remote_info"
        
        # 检查响应是否包含成功状态码
        if ! echo "$remote_info" | grep -q "$file"; then
            log "获取远程文件信息失败，等待后重试..."
            sleep 10
            ((retry_count++))
            continue
        fi
        
        # 解析文件大小
        local remote_size=$(echo "$remote_info" | jq -r '.size')
        
        log "验证文件 $file - 本地大小: $local_size, 远程大小: $remote_size"
        
        if [ "$local_size" = "$remote_size" ] && [ "$remote_size" -gt 0 ]; then
            log "文件大小验证成功"
            return 0
        else
            ((retry_count++))
            
            if [ $retry_count -lt $max_retries ]; then
                # 只有在成功获取到远程文件大小且不匹配时才执行删除和重传
                if [ "$remote_size" != "null" ] && [ -n "$remote_size" ]; then
                    log "文件大小不匹配，本地: $local_size, 远程: $remote_size，第 $retry_count 次重试..."
                    
                    # 删除错误的远程文件
                    if delete_remote_file "$file" "$dir"; then
                        sleep 2
                        
                        # 重新上传
                        if upload_file "$file" "$dir"; then
                            log "重新上传成功，继续验证..."
                            continue
                        else
                            log "重新上传失败"
                        fi
                    else
                        log "删除远程文件失败，跳过重传"
                    fi
                else
                    # 未能获取远程文件大小，等待后重试验证
                    log "未能获取远程文件大小，等待30秒后重试验证..."
                    sleep 30
                    continue
                fi
            else
                log "达到最大重试次数，验证失败"
                return 1
            fi
        fi
    done
    
    return 1
}

# 验证导出的镜像文件
verify_exported_image() {
    local file=$1
    
    # 使用 file 命令检查文件类型和完整性
    local file_info=$(file "$file")
    
    # 检查是否为gzip文件
    if ! echo "$file_info" | grep -q "gzip compressed data"; then
        log "错误：不是有效的gzip压缩文件"
        return 1
    fi
    
    # 检查是否被截断
    if echo "$file_info" | grep -q "truncated"; then
        log "错误：文件不完整（被截断）"
        return 1
    fi
    
    # 一次性获取文件列表，检查内容和manifest.json
    local file_list=$(gunzip -c "$file" | tar -tf - 2>/dev/null)
    if [ -z "$file_list" ]; then
        log "错误：tar文件是空的或无法读取内容"
        return 1
    fi
    
    if ! echo "$file_list" | grep -q "manifest.json"; then
        log "错误：镜像文件缺少必要的manifest.json文件"
        return 1
    fi
    
    log "镜像文件验证通过"
    return 0
}

verify_file_size() {
    remote_size=$(get_remote_file_info $1 $2 | jq -r '.size') 
    local_size=$(stat -f%z "$3" 2>/dev/null || stat -c%s "$3")
    if [ "$local_size" = "$remote_size" ] && [ "$remote_size" -gt 0 ]; then
        log "文件大小验证成功"
        return 0
    else
        log "本地与远程大小不一致，需要更新"
        return 1
    fi
}

# 检查是否需要更新
check_update_needed() {
    local image=$1
    local arch=$2
    image_check="${image}-${arch}"
    local update_needed=0
    
    # 先检查镜像是否存在
    if ! docker image inspect "$image_check" >/dev/null 2>&1; then
        log "镜像不存在，需要下载"
        # 尝试下载镜像，最多重试3次
        local retry_count=0
        local max_retries=3
        
        while [ $retry_count -lt $max_retries ]; do
            # 捕获docker pull的输出以提取SHA值
            local pull_output
            pull_output=$(docker pull --platform linux/"$arch" "$image" 2>&1)
            local pull_status=$?
            
            if [ $pull_status -eq 0 ]; then
                log "下载镜像 $image 成功"
                
                # 从输出中提取SHA值
                local sha_value
                sha_value=$(echo "$pull_output" | grep -o "Digest: sha256:[a-f0-9]*" | cut -d':' -f3)
                
                if [ ! -z "$sha_value" ]; then
                    # 保存SHA值到文件
                    echo "${image}-${arch} ${sha_value}" >> "${WORK_DIR}/ailg_sha_local.txt"
                    log "已保存SHA值: ${image}-${arch} ${sha_value}"
                else
                    log "警告：无法从拉取输出中提取SHA值"
                fi
                
                docker tag "$image" "$image_check"  # 保存一个副本用于比较
                return 0  # 返回0表示需要更新（导出并上传）
            fi
            
            ((retry_count++))
            if [ $retry_count -lt $max_retries ]; then
                log "下载镜像 $image 失败，第 $retry_count 次重试..."
                sleep 3  # 失败后等待3秒再重试
            else
                log "下载镜像 $image 失败，已达到最大重试次数"
            fi
        done
        
        return 1  # 返回1表示处理失败
    fi
    
    # 对于已存在的镜像，根据标签类型检查更新
    if [[ $image == *":latest" ]]; then
        check_image "$image" "latest" "$arch"
        return $?
    elif [[ $image == *":hostmode" ]]; then
        check_image "$image" "hostmode" "$arch"
        return $?
    elif [[ $image == *":latest-rockchip" ]]; then
        check_image "$image" "latest-rockchip" "$arch"
        return $?
    fi
    
    # 固定版本的镜像，已存在则无需更新
    return 2  # 返回2表示无需更新
}

# 更新镜像主函数
update_images() {
    log "开始检查镜像更新..."
    log "=================================================="
    mkdir -p "$IMAGES_DIR"
    
    # 创建SHA文件（如果不存在）
    touch "${WORK_DIR}/ailg_sha_local.txt"
    
    # 清理镜像
    docker images | grep "<none>" | awk '{print $3}' | xargs -r docker rmi
    
    # 先处理 AMD64 镜像
    log "处理 AMD64 架构镜像..."
    ARCH="amd64"
    for image in "${!IMAGES[@]}"; do
        process_image "${IMAGES[$image]}" "$ARCH"
    done
    
    # 再处理 ARM64 镜像
    log "处理 ARM64 架构镜像..."
    ARCH="arm64"
    for image in "${!ARM64_IMAGES[@]}"; do
        process_image "${ARM64_IMAGES[$image]}" "$ARCH"
    done
    
    log "所有架构镜像更新检查完成"
    log "=================================================="
}

# 处理单个镜像的函数
process_image() {
    local image=$1
    local arch=$2
    local filename=$(echo "$image" | tr '/:' '.' | sed 's/\.$//')".$arch.tar.gz"
    local output_path="${IMAGES_DIR}/${filename}"
    
    log "正在处理 $image (架构: $arch)"
    
    # 检查是否需要更新
    check_update_needed "$image" "$arch"
    local update_needed=$?
    
    if [ $update_needed -eq 1 ]; then
        log "处理镜像 $image 失败，跳过"
        return
    fi
    
    # 检查是否需要处理（更新或上传）
    if [ $update_needed -eq 0 ] || \
           ([ $update_needed -eq 2 ] && ( ! check_remote_file_exists "$filename" "${IMAGES_REMOTE_DIR}" || \
            ! verify_file_size "$filename" "${IMAGES_REMOTE_DIR}" "${output_path}" ) \
           ); then
        
        # 如果本地已有文件，先备份再导出新的
        if [ -f "$output_path" ]; then
            log "本地已有镜像文件，先进行备份"
            backup_image_file "$filename"
            log "删除旧的镜像文件"
            rm -f "$output_path"
        fi
        
        # 导出新镜像
        log "正在导出 $image"
        if ! docker save "$image" | gzip > "$output_path"; then
            log "导出 $image 失败"
            [ -f "$output_path" ] && rm -f "$output_path"
            return
        fi
        log "成功导出到: $output_path"
        
        # 验证文件
        if ! verify_exported_image "$output_path"; then
            log "镜像文件验证失败，重试..."
            rm -f "$output_path"
            
            # 重新导出一次
            if ! docker save "$image" | gzip > "$output_path" || ! verify_exported_image "$output_path"; then
                log "重新导出镜像仍然失败，跳过处理"
                [ -f "$output_path" ] && rm -f "$output_path"
                return
            fi
        fi
        
        # 设置文件权限
        chmod 777 "$output_path"
        log "已设置文件权限"
        
        # 上传到Alist
        if ! delete_remote_file "$filename" "${IMAGES_REMOTE_DIR}"; then
            log "删除远程镜像文件 $filename 失败"
            return
        fi
        sleep 1
        
        if ! upload_file "$filename" "${IMAGES_REMOTE_DIR}"; then
            log "上传镜像文件 $filename 失败"
            return
        fi
        
        # 验证上传
        if ! verify_uploaded_file "$filename" "${IMAGES_REMOTE_DIR}"; then
            log "镜像文件 $filename 验证失败"
            return
        fi
        
        log "镜像 $image 处理完成"
        log "=================================================="
    else
        log "本地是最新版本且远程文件已存在，无需处理"
        log "=================================================="
    fi
}

# 使用管道和 Docker API 加载镜像
load_image_pipe_api() {
    local image_file=$1
    local save_dir=$2
    local full_path="${save_dir}/${image_file}"
    
    log "正在通过管道加载镜像: $image_file"
    
    # 使用管道直接将解压后的数据传给 Docker API
    if ! gunzip -c "$full_path" | curl -s -X POST --unix-socket /var/run/docker.sock \
        -H "Content-Type: application/x-tar" \
        --data-binary @- \
        http://localhost/images/load; then
        log "通过 API 加载镜像失败"
        return 1
    fi
    
    log "镜像加载成功"
    return 0
}

# 主循环
main() {
    while true; do
        # 添加明显的分隔符
        log "======================================"
        log "开始新一轮检查"
        log "======================================"
        
        # 检查并轮转日志（只在文件大于1MB时）
        rotate_logs
        
        # 刷新 token
        if ! refresh_token; then
            log "等待下一轮检查..."
            sleep 1800
            continue
        fi
        
        # 执行文件更新
        log "开始检查文件更新"
        update_files
        
        # 等待30分钟        
        log "等待30分钟后进行下一次检查..."
        sleep 1800
    done
}

# 启动脚本
main