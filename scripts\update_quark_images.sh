#!/bin/bash

# 颜色定义
Blue="\033[1;34m"
Green="\033[1;32m"
Red="\033[1;31m"
Yellow="\033[1;33m"
NC="\033[0m"

# 提示函数
INFO() { echo -e "${Green}[INFO]${NC} $1"; }
ERROR() { echo -e "${Red}[ERROR]${NC} $1"; }
WARN() { echo -e "${Yellow}[WARN]${NC} $1"; }

# 固定配置
arch="amd64"
save_dir="/www/data/images"

# 镜像列表
declare -A images=(
    [1]="ailg/g-box:hostmode"
    [2]="ailg/ggbond:latest"
    [3]="emby/embyserver:*******"
    [4]="emby/embyserver:********"
    [5]="jellyfin/jellyfin:10.9.6"
    [6]="nyanmisaka/jellyfin:latest"
    [7]="ddsderek/xiaoya-emd:latest"
    [8]="cloudnas/clouddrive2:latest"
)

# 检查并创建目录
mkdir -p "$save_dir" || { ERROR "创建目录失败"; exit 1; }

# 检查并更新镜像
check_image() {
    local image=$1
    local tag=$2
    local org_name=$(echo $image | cut -d'/' -f1)
    local img_name=$(echo $image | cut -d'/' -f2 | cut -d':' -f1)
    
    INFO "检查 $image 是否为最新版本..."
    
    # 获取远程镜像SHA
    local remote_sha
    remote_sha=$(curl -s -m 20 "https://hub.docker.com/v2/repositories/${org_name}/${img_name}/tags/${tag}" | grep -oE '[0-9a-f]{64}' | tail -1)
    if [ -z "$remote_sha" ]; then
        ERROR "无法获取远程镜像SHA"
        return 1
    fi
    
    # 如果本地存在镜像，检查SHA
    if docker image inspect "$image" >/dev/null 2>&1; then
        local local_sha
        local_sha=$(docker inspect -f'{{index .RepoDigests 0}}' "$image" | cut -f2 -d:)
        
        if [ "$local_sha" != "$remote_sha" ]; then
            WARN "发现新版本，正在更新..."
            docker tag "$image" "${image}-old"
            docker rmi "$image" >/dev/null 2>&1
            
            local retry_count=0
            while [ $retry_count -lt 3 ]; do
                if docker pull "$image"; then
                    INFO "更新成功"
                    docker rmi "${image}-old" >/dev/null 2>&1
                    return 0
                fi
                ((retry_count++))
                WARN "下载失败，第 $retry_count 次重试..."
                sleep 3
            done
            
            ERROR "更新失败，回滚到旧版本"
            docker tag "${image}-old" "$image"
            docker rmi "${image}-old" >/dev/null 2>&1
            return 1
        else
            INFO "已是最新版本"
        fi
    else
        WARN "本地未找到镜像，正在下载..."
        local retry_count=0
        while [ $retry_count -lt 3 ]; do
            if docker pull "$image"; then
                INFO "下载成功"
                return 0
            fi
            ((retry_count++))
            WARN "下载失败，第 $retry_count 次重试..."
            sleep 3
        done
        ERROR "下载失败"
        return 1
    fi
    return 0
}

# 下载镜像的函数
download_image() {
    local image=$1
    local retry_count=0
    while [ $retry_count -lt 3 ]; do
        if docker pull "$image"; then
            INFO "下载成功"
            return 0
        fi
        ((retry_count++))
        WARN "下载失败，第 $retry_count 次重试..."
        sleep 3
    done
    ERROR "下载失败"
    return 1
}

# 主循环函数
update_images() {
    INFO "开始检查更新..."
    for i in "${!images[@]}"; do
        image="${images[$i]}"
        filename=$(echo "$image" | tr '/:' '.' | sed 's/\.$//')".$arch.tar.gz"
        output_path="$save_dir/$filename"
        
        echo -e "\n${Yellow}正在处理 $image${NC}"
        
        # 根据标签类型检查更新
        if [[ $image == *":latest" ]]; then
            check_image "$image" "latest" || continue
        elif [[ $image == *":hostmode" ]]; then
            check_image "$image" "hostmode" || continue
        elif ! docker image inspect "$image" >/dev/null 2>&1; then
            WARN "镜像不存在，正在下载..."
            download_image "$image" || continue
        fi
        
        # 如果存在旧文件，先删除
        [ -f "$output_path" ] && rm -f "$output_path"
        
        echo -e "${Yellow}正在导出 $image${NC}"
        if docker save "$image" | gzip > "$output_path"; then
            INFO "成功导出到: $output_path"
        else
            ERROR "导出 $image 失败"
            [ -f "$output_path" ] && rm -f "$output_path"
        fi
    done
    INFO "本轮检查更新完成"
}

# 直接开始主循环
while true; do
    update_images
    INFO "等待12小时后进行下一次检查..."
    sleep 12h
done 