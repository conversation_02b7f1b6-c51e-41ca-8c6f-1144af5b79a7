#!/bin/bash

# 使用说明
# 本脚本用于自动更新和维护 ARM64 架构的 Docker 镜像
##### 此脚本在迪拜的vps上测试时，存在终端提示成功，实际上传失败的情况，即网盘没有文件，所以弃用了！！！
##### 现在使用 update_quark_files.sh 脚本替代，已验证成功！！！集合了amd64和arm64的镜像更新！！！
# 安装步骤：
# 1. 创建服务文件：
#    sudo nano /etc/systemd/system/update-quark-images-arm64.service
#    
#    [Unit]
#    Description=Update Quark Images ARM64 Service
#    After=network.target docker.service
#    Wants=docker.service
#    
#    [Service]
#    Type=simple
#    User=root
#    ExecStart=/www/data/scripts/update_quark_images_arm64.sh
#    Restart=on-failure
#    RestartSec=60
#    
#    [Install]
#    WantedBy=multi-user.target
#
# 2. 创建必要目录并移动脚本：
#    sudo mkdir -p /www/data/scripts
#    sudo mv update_quark_images_arm64.sh /www/data/scripts/
#    sudo chmod +x /www/data/scripts/update_quark_images_arm64.sh
#
# 3. 启用并启动服务：
#    sudo systemctl daemon-reload              # 重新加载 systemd 配置
#    sudo systemctl enable update-quark-images-arm64  # 设置开机自启
#    sudo systemctl start update-quark-images-arm64   # 立即启动服务
#
# 4. 管理服务：
#    sudo systemctl status update-quark-images-arm64  # 查看服务状态
#    sudo systemctl stop update-quark-images-arm64    # 停止服务
#    sudo systemctl restart update-quark-images-arm64 # 重启服务
#    sudo journalctl -u update-quark-images-arm64 -f  # 实时查看日志
#
# 5. 日志位置：
#    - 服务日志：journalctl -u update-quark-images-arm64
#    - 脚本日志：/www/data/update_images.log
#
# 注意事项：
# - 本脚本适用于 ARM64 架构的 Ubuntu/Debian 系统
# - 需要安装 docker、curl、jq 等依赖：
#   sudo apt update
#   sudo apt install -y docker.io curl jq
# - 确保 Alist 服务正常运行且可访问
# - 确保有足够的磁盘空间用于存储镜像
# - 建议定期检查日志确保服务正常运行

# 配置参数
ALIST_URL="http://127.0.0.1:5244"
mkdir -p /www/data
WORK_DIR="/www/data"
LOG_DIR="/www/data"
MAX_LOGS=10
MAX_BACKUPS=3

# 镜像列表
declare -A IMAGES=(
    [1]="ailg/g-box:hostmode"
    [2]="ailg/ggbond:latest"
    [3]="emby/embyserver_arm64v8:*******"
    [4]="emby/embyserver_arm64v8:********"
    [5]="jellyfin/jellyfin:10.9.6"
    [6]="nyanmisaka/jellyfin:latest-rockchip"
    [7]="ddsderek/xiaoya-emd:latest"
    [8]="cloudnas/clouddrive2:latest"
)

IMAGES_DIR="${WORK_DIR}/images"
IMAGES_REMOTE_DIR="/gbox常用镜像"
ARCH="arm64"

# 日志函数
log() {
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] $1" | tee -a "${LOG_DIR}/update_images.log"
}

# 日志文件轮转
rotate_logs() {
    local today=$(date +%Y-%m-%d)
    local current_log="${LOG_DIR}/update_images.log"
    
    if [ -s "$current_log" ]; then
        mv "$current_log" "${LOG_DIR}/update_images-${today}.log"
        touch "$current_log"
        ls -t ${LOG_DIR}/update_images-*.log | tail -n +$((MAX_LOGS + 1)) | xargs -r rm
    fi
}

# 获取 token 函数
get_token() {
    local token
    token=$(curl -s -X POST "http://127.0.0.1:5244/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"admin\",\"password\":\"AQG7q5bfwEVq5201314@\"}" | jq -r '.data.token')
    
    if [ -z "$token" ] || [ "$token" = "null" ]; then
        return 1
    fi
    echo "$token"
    return 0
}

# 刷新 token，包含重试逻辑
refresh_token() {
    local token
    token=$(get_token)
    
    if [ $? -ne 0 ]; then
        log "获取 token 失败，10秒后重试..."
        sleep 10
        token=$(get_token)
        
        if [ $? -ne 0 ]; then
            log "获取 token 第二次失败，跳过本轮更新"
            return 1
        fi
    fi
    
    TOKEN="$token"
    return 0
}

# 删除远程文件
delete_remote_file() {
    local file=$1
    local dir=${2:-"/quark"}  # 默认目录为 /quark
    
    log "删除远程文件 ${dir}/${file}"
    curl -s -X POST "${ALIST_URL}/api/fs/remove" \
        -H "Authorization: ${TOKEN}" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json, text/plain, */*" \
        -d "{
            \"dir\": \"${dir}\",
            \"names\": [\"${file}\"]
        }" >> "${LOG_DIR}/update_images.log" 2>&1
    
    # 使用 jq 精确检查文件是否存在
    local check_count=0
    while [ $check_count -lt 3 ]; do
        local list_response=$(curl -s -X POST "${ALIST_URL}/api/fs/list" \
            -H "Authorization: ${TOKEN}" \
            -H "Content-Type: application/json" \
            -d "{
                \"path\": \"${dir}\",
                \"refresh\": true
            }")
        
        # 使用 jq 检查特定文件是否在列表中
        local file_exists=$(echo "$list_response" | jq -r --arg fname "$file" '.data.content[] | select(.name == $fname) | .name')
        
        if [ -z "$file_exists" ]; then
            log "文件 $file 已确认删除"
            return 0
        else
            log "文件 $file 仍然存在，等待后重试..."
            sleep 5
            check_count=$((check_count + 1))
        fi
    done
    
    log "警告：文件 $file 在多次尝试后仍未删除"
    return 1
}

# 上传文件到远程
upload_file() {
    local file=$1
    local dir=${2:-"/quark"}
    local file_path="${WORK_DIR}/${file}"
    local retry_count=0
    local max_retries=3
    
    # 如果是镜像文件，使用IMAGES_DIR
    if [[ $dir == "${IMAGES_REMOTE_DIR}" ]]; then
        file_path="${IMAGES_DIR}/${file}"
    fi
    
    # 检查文件是否存在
    if [ ! -f "$file_path" ]; then
        log "错误：文件不存在: $file_path"
        return 1
    fi
    
    # 获取文件大小用于日志
    local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path")
    log "开始上传文件 $file (大小: $file_size bytes)"
    
    while [ $retry_count -lt $max_retries ]; do
        log "上传文件 $file 到远程 $dir (第$((retry_count + 1))次尝试)"
        
        # 刷新 token
        if ! refresh_token; then
            log "刷新 token 失败"
            return 1
        fi
        
        # 使用新的上传方式
        if curl -s -X POST "${ALIST_URL}/api/fs/form" \
            -H "Authorization: ${TOKEN}" \
            -H "File-Path: ${dir}/${file}" \
            -F "file=@${file_path}"; then
            log "上传成功"
            return 0
        fi
        
        log "上传失败，等待30秒后重试..."
        sleep 30
        ((retry_count++))
    done
    
    log "达到最大重试次数，上传失败"
    return 1
}

# 检查并更新镜像
check_image() {
    local image=$1
    local tag=$2
    local org_name=$(echo $image | cut -d'/' -f1)
    local img_name=$(echo $image | cut -d'/' -f2 | cut -d':' -f1)
    
    log "检查 $image 是否为最新版本..."
    
    # 获取远程镜像SHA
    local remote_sha
    remote_sha=$(curl -s -m 20 "https://hub.docker.com/v2/repositories/${org_name}/${img_name}/tags/${tag}" | grep -oE '[0-9a-f]{64}' | tail -1)
    if [ -z "$remote_sha" ]; then
        log "无法获取远程镜像SHA"
        return 1
    fi
    
    # 如果本地存在镜像，检查SHA
    if docker image inspect "$image" >/dev/null 2>&1; then
        local local_sha
        local_sha=$(docker inspect -f'{{index .RepoDigests 0}}' "$image" | cut -f2 -d:)
        
        if [ "$local_sha" != "$remote_sha" ]; then
            log "发现新版本，正在更新..."
            docker tag "$image" "${image}-old"
            docker rmi "$image" >/dev/null 2>&1
            
            local retry_count=0
            while [ $retry_count -lt 3 ]; do
                if docker pull "$image"; then
                    log "更新成功"
                    docker rmi "${image}-old" >/dev/null 2>&1
                    return 0
                fi
                ((retry_count++))
                log "下载失败，第 $retry_count 次重试..."
                sleep 3
            done
            
            log "更新失败，回滚到旧版本"
            docker tag "${image}-old" "$image"
            docker rmi "${image}-old" >/dev/null 2>&1
            return 1
        else
            log "已是最新版本"
            return 2  # 返回2表示无需更新
        fi
    else
        log "本地未找到镜像，正在下载..."
        local retry_count=0
        while [ $retry_count -lt 3 ]; do
            if docker pull "$image"; then
                log "下载成功"
                return 0
            fi
            ((retry_count++))
            log "下载失败，第 $retry_count 次重试..."
            sleep 3
        done
        log "下载失败"
        return 1
    fi
}

# 备份镜像文件
backup_image_file() {
    local file=$1
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="${IMAGES_DIR}/backup_${timestamp}"
    
    mkdir -p "$backup_dir"
    if [ -f "${IMAGES_DIR}/${file}" ]; then
        cp "${IMAGES_DIR}/${file}" "${backup_dir}/"
        log "已备份镜像文件 $file"
        
        # 保留最新的3个备份
        ls -td ${IMAGES_DIR}/backup_* | tail -n +$((MAX_BACKUPS + 1)) | xargs -r rm -rf
        return 0
    else
        log "镜像文件 $file 不存在，跳过备份"
        return 1
    fi
}

# 获取远程文件信息
get_remote_file_info() {
    local file=$1
    local dir=${2:-"/quark"}
    
    # 将日志输出重定向到标准错误，这样不会影响函数返回值
    log "正在查询文件: ${dir}/${file}" >&2
    
    # 使用 list 接口获取目录内容
    local list_response=$(curl -s -X POST "${ALIST_URL}/api/fs/list" \
        -H "Authorization: ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"path\": \"${dir}\",
            \"refresh\": true
        }")

    # 从列表中提取指定文件的信息
    echo "$list_response" | jq -r --arg fname "$file" '.data.content[] | select(.name == $fname)'
}

verify_file_size() {
    remote_size=$(get_remote_file_info $1 $2 | jq -r '.size') 
    local_size=$(stat -f%z "$3" 2>/dev/null || stat -c%s "$3")
    if [ "$local_size" = "$remote_size" ] && [ "$remote_size" -gt 0 ]; then
        log "文件大小验证成功"
        return 0
    else
        log "本地与远程大小不一致，需要更新"
        return 1
    fi
}


# 验证上传文件
verify_uploaded_file() {
    local file=$1
    local dir=${2:-"/quark"}
    local file_path="${WORK_DIR}/${file}"
    local retry_count=0
    local max_retries=3
    
    # 如果是镜像文件，使用IMAGES_DIR
    if [[ $dir == "${IMAGES_REMOTE_DIR}" ]]; then
        file_path="${IMAGES_DIR}/${file}"
    fi
    
    while [ $retry_count -lt $max_retries ]; do
        # 获取本地文件大小
        local local_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path")
        
        # 等待文件上传完成
        sleep 5
        
        # 获取远程文件信息
        local remote_info=$(get_remote_file_info "$file" "$dir")
        local remote_size=$(echo "$remote_info" | jq -r '.size')
        
        log "验证文件 $file - 本地大小: $local_size, 远程大小: $remote_size"
        
        if [ "$local_size" = "$remote_size" ] && [ "$remote_size" -gt 0 ]; then
            log "文件大小验证成功"
            return 0
        else
            ((retry_count++))
            
            if [ $retry_count -lt $max_retries ]; then
                log "文件大小不匹配或远程文件无效，第 $retry_count 次重试..."
                
                # 删除错误的远程文件
                delete_remote_file "$file" "$dir"
                sleep 2
                
                # 重新上传
                if upload_file "$file" "$dir"; then
                    log "重新上传成功，继续验证..."
                    continue
                else
                    log "重新上传失败"
                fi
            else
                log "达到最大重试次数，验证失败"
                return 1
            fi
        fi
    done
    
    return 1
}

# 验证导出的镜像文件
verify_exported_image() {
    local file=$1
    
    # 使用 file 命令检查文件类型和完整性
    local file_info=$(file "$file")
    
    # 检查是否为gzip文件
    if ! echo "$file_info" | grep -q "gzip compressed data"; then
        log "错误：不是有效的gzip压缩文件"
        return 1
    fi
    
    # 检查是否被截断
    if echo "$file_info" | grep -q "truncated"; then
        log "错误：文件不完整（被截断）"
        return 1
    fi
    
    # 一次性获取文件列表，检查内容和manifest.json
    local file_list=$(tar -tf "$file")
    if [ -z "$file_list" ]; then
        log "错误：tar文件是空的或无法读取内容"
        return 1
    fi
    
    if ! echo "$file_list" | grep -q "manifest.json"; then
        log "错误：镜像文件缺少必要的manifest.json文件"
        return 1
    fi
    
    log "镜像文件验证通过"
    return 0
}

# 检查远程文件是否存在
check_remote_file_exists() {
    local file=$1
    local dir=${2:-"/quark"}
    
    log "检查远程文件是否存在: ${dir}/${file}"
    
    # 先尝试使用list接口（更快）
    local list_response=$(curl -s -X POST "${ALIST_URL}/api/fs/list" \
        -H "Authorization: ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"path\": \"${dir}\",
            \"refresh\": true
        }")
    
    # 使用jq检查文件是否在列表中
    local file_exists=$(echo "$list_response" | jq -r --arg fname "$file" '.data.content[] | select(.name == $fname) | .name')
    if [ ! -z "$file_exists" ]; then
        log "文件存在 (通过 list 接口确认)"
        return 0
    fi
    
    log "文件不存在"
    return 1
}

# 更新镜像
update_images() {
    log "开始检查镜像更新..."
    mkdir -p "$IMAGES_DIR"
    
    for image in "${IMAGES[@]}"; do
        local filename=$(echo "$image" | tr '/:' '.' | sed 's/\.$//')".$ARCH.tar.gz"
        local output_path="${IMAGES_DIR}/${filename}"
        
        log "正在处理 $image"
        
        # 根据标签类型检查更新
        local update_needed=0
        if [[ $image == *":latest" ]]; then
            check_image "$image" "latest"
            update_needed=$?
        elif [[ $image == *":hostmode" ]]; then
            check_image "$image" "hostmode"
            update_needed=$?
        elif [[ $image == *":latest-rockchip" ]]; then
            check_image "$image" "latest-rockchip"
            update_needed=$?
        elif ! docker image inspect "$image" >/dev/null 2>&1; then
            log "镜像不存在，需要下载"
            update_needed=0
        fi
        
        # 检查是否需要处理（更新或上传）
        if [ $update_needed -eq 0 ] || \
           ([ $update_needed -eq 2 ] && ( ! check_remote_file_exists "$filename" "${IMAGES_REMOTE_DIR}" || \
            ! verify_file_size "$filename" "${IMAGES_REMOTE_DIR}" "${output_path}" ) \
           ); then
            
            # 如果本地已有文件，先备份再导出新的
            if [ -f "$output_path" ]; then
                log "本地已有镜像文件，先进行备份"
                backup_image_file "$filename"
                log "删除旧的镜像文件"
                rm -f "$output_path"
            fi
            
            # 导出新镜像
            log "正在导出 $image"
            if ! docker save "$image" | gzip > "$output_path"; then
                log "导出 $image 失败"
                [ -f "$output_path" ] && rm -f "$output_path"
                continue
            fi
            log "成功导出到: $output_path"
            
            # 验证文件
            if ! verify_exported_image "$output_path"; then
                log "镜像文件验证失败，重试..."
                rm -f "$output_path"
                
                # 重新导出一次
                if ! docker save "$image" | gzip > "$output_path" || ! verify_exported_image "$output_path"; then
                    log "重新导出镜像仍然失败，跳过处理"
                    [ -f "$output_path" ] && rm -f "$output_path"
                    continue
                fi
            fi
            
            # 设置文件权限
            chmod 777 "$output_path"
            log "已设置文件权限"
            
            # 上传到Alist
            if ! delete_remote_file "$filename" "${IMAGES_REMOTE_DIR}"; then
                log "删除远程镜像文件 $filename 失败"
                continue
            fi
            sleep 1
            
            if ! upload_file "$filename" "${IMAGES_REMOTE_DIR}"; then
                log "上传镜像文件 $filename 失败"
                continue
            fi
            
            # 验证上传
            if ! verify_uploaded_file "$filename" "${IMAGES_REMOTE_DIR}"; then
                log "镜像文件 $filename 验证失败"
                continue
            fi
            
            log "镜像 $image 处理完成"
            log "=================================================="
        else
            log "本地是最新版本且远程文件已存在，无需处理"
            log "=================================================="
        fi
    done
    log "镜像更新检查完成"
    log "=================================================="
}

# 主循环
main() {
    while true; do
        # 刷新 token
        if ! refresh_token; then
            log "等待下一轮检查..."
            sleep 43200  # 12小时
            continue
        fi
        
        # 检查并轮转日志
        rotate_logs
        
        # 执行镜像更新
        log "开始检查镜像更新"
        update_images
        
        # 等待12小时
        log "等待4小时后进行下一次检查..."
        sleep 14400
    done
}

# 启动脚本
main 