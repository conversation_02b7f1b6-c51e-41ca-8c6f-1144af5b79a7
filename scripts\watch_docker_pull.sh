#!/bin/bash

# 监视目录和文件
WATCH_DIR="/www/data/115"
PULL_FILE="docker_pull.txt"
LOG_FILE="docker_pull.log"
LIST_FILE="docker_list.txt"
LOCK_FILE="docker_pull.lock"
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"

# 导入update_115_files.sh中的函数
source "${SCRIPT_DIR}/update_115_files.sh"

# 检查镜像名格式是否有效
check_image_name() {
    local image=$1
    # 检查基本格式：组织名/镜像名:标签
    if [[ ! $image =~ ^[a-z0-9]+([a-z0-9\-\_\.]*/)?[a-z0-9\-\_\.]+:[a-z0-9\-\_\.]+$ ]]; then
        return 1
    fi
    return 0
}

# 检查镜像是否有效（使用remote_sha检查）
validate_image() {
    local image=$1
    local org_name=$(echo $image | cut -d'/' -f1)
    local img_name=$(echo $image | cut -d'/' -f2- | cut -d':' -f1)
    local tag=$(echo $image | cut -d':' -f2)
    
    # 如果镜像名不包含/，调整解析结果
    if [[ ! $image =~ "/" ]]; then
        img_name=$org_name
        org_name="library"
    fi
    
    # 获取远程镜像SHA
    local remote_sha
    remote_sha=$(curl -s -m 20 "https://hub.docker.com/v2/repositories/${org_name}/${img_name}/tags/${tag}" | grep -oE '[0-9a-f]{64}' | tail -1)
    if [ -z "$remote_sha" ]; then
        return 1
    fi
    return 0
}

# 记录日志
log_message() {
    local message=$1
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] $message" >> "${WATCH_DIR}/${LOG_FILE}"
}

# 处理docker_pull.txt文件
process_pull_file() {
    # 检查锁文件
    if [ -f "${WATCH_DIR}/${LOCK_FILE}" ]; then
        local lock_pid=$(cat "${WATCH_DIR}/${LOCK_FILE}")
        if kill -0 $lock_pid 2>/dev/null; then
            log_message "系统正忙，请稍后再试（PID: $lock_pid）"
            return 1
        else
            rm -f "${WATCH_DIR}/${LOCK_FILE}"
        fi
    fi
    
    # 创建锁文件
    echo $$ > "${WATCH_DIR}/${LOCK_FILE}"
    
    # 创建新的日志文件
    : > "${WATCH_DIR}/${LOG_FILE}"
    log_message "开始处理 docker_pull.txt"
    
    # 读取并验证镜像
    local valid_images=()
    while IFS= read -r line || [ -n "$line" ]; do
        # 跳过空行和注释
        [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]] && continue
        
        # 处理每个镜像名
        for image in $line; do
            if check_image_name "$image"; then
                if validate_image "$image"; then
                    log_message "验证镜像成功: $image"
                    # 检查是否已在docker_list.txt中
                    if [ -f "${WATCH_DIR}/${LIST_FILE}" ] && grep -q "^$image " "${WATCH_DIR}/${LIST_FILE}"; then
                        log_message "镜像 $image 已存在于列表中，跳过处理"
                        continue
                    fi
                    valid_images+=("$image")
                else
                    log_message "无效的镜像: $image（在Docker Hub上未找到）"
                fi
            else
                log_message "无效的镜像名格式: $image"
            fi
        done
    done < "${WATCH_DIR}/${PULL_FILE}"
    
    # 如果没有有效镜像，清理并退出
    if [ ${#valid_images[@]} -eq 0 ]; then
        log_message "没有找到有效的镜像，处理结束"
        rm -f "${WATCH_DIR}/${LOCK_FILE}"
        return 0
    fi
    
    # 处理有效镜像
    log_message "开始处理 ${#valid_images[@]} 个有效镜像"
    
    # 调用update_images处理镜像
    update_images "amd64" "${valid_images[@]}"
    
    # 更新docker_list.txt
    for image in "${valid_images[@]}"; do
        local filename=$(echo "$image" | tr '/:' '.' | sed 's/\.$//')".$ARCH.tar.gz"
        echo "$image amd64 $filename" >> "${WATCH_DIR}/${LIST_FILE}"
    done
    
    # 清理文件
    rm -f "${WATCH_DIR}/${PULL_FILE}" "${WATCH_DIR}/${LOCK_FILE}"
    log_message "处理完成"
}

# 主循环
main() {
    while true; do
        if [ -f "${WATCH_DIR}/${PULL_FILE}" ]; then
            process_pull_file
        fi
        sleep 3
    done
}

# 启动监视
main 