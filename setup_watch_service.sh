#!/bin/bash

# 创建服务文件
cat > /etc/systemd/system/watch-docker-pull.service << EOF
[Unit]
Description=Watch Docker Pull Service
After=network.target docker.service
Wants=docker.service

[Service]
Type=simple
User=root
ExecStart=/www/data/scripts/watch_docker_pull.sh
WorkingDirectory=/www/data
Restart=always
RestartSec=60

[Install]
WantedBy=multi-user.target
EOF

# 设置权限
chmod +x /www/data/scripts/watch_docker_pull.sh
chmod 777 /www/data/115

# 启动服务
systemctl daemon-reload
systemctl enable watch-docker-pull
systemctl start watch-docker-pull 