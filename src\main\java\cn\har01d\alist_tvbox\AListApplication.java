package cn.har01d.alist_tvbox;

import cn.har01d.alist_tvbox.config.AppProperties;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.InputStream;
import java.io.PrintStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.io.FileInputStream;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.Random;
import java.util.logging.Logger;

@EnableAsync
@EnableScheduling
@EnableConfigurationProperties(AppProperties.class)
@SpringBootApplication
public class AListApplication {

    private static final Logger logger = Logger.getLogger(AListApplication.class.getName());

    public static void main(String[] args) {
        // logger.info("==========使用了 qrcode 分支的代码进行编译");
        SpringApplication app = new SpringApplication(AListApplication.class);
        app.run(args);

        // 输出随机选择的 ASCII 艺术图案
        printRandomAsciiArt("/var/lib/banner");
    }

    private static void printRandomAsciiArt(String folderPath) {
        File folder = new File(folderPath);
        File[] files = folder.listFiles((dir, name) -> name.startsWith("banner_") && name.endsWith(".txt"));
        if (files != null && files.length > 0) {
            Random random = new Random();
            File randomFile = files[random.nextInt(files.length)];
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(randomFile), StandardCharsets.UTF_8))) {
                String line;
                int maxLength = 0;
                while ((line = reader.readLine()) != null) {
                    System.out.println(line); // 使用 System.out.println 输出
                    maxLength = Math.max(maxLength, line.length());
                }
                // 从另一个文件中随机选择一行并输出
                System.out.println();
                printRandomLineFromFile("/var/lib/banner/banner.txt", maxLength);
            } catch (IOException e) {
                logger.severe("Error reading ASCII art file: " + e.getMessage());
            }
        } else {
            logger.warning("No banner files found in the specified folder.");
        }
    }

    private static void printRandomLineFromFile(String filePath, int maxLength) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8))) {
            String[] lines = reader.lines().toArray(String[]::new);
            if (lines.length > 0) {
                Random random = new Random();
                String randomLine = lines[random.nextInt(lines.length)];
                int tipLength = randomLine.codePoints().map(cp -> Character.isIdeographic(cp) ? 2 : 1).sum();
                int paddingLength = (maxLength - tipLength - 2) / 2;

                String leftPadding = "-".repeat(paddingLength);
                String rightPadding = "-".repeat(paddingLength);
                if ((maxLength - tipLength - 2) % 2 != 0) {
                    rightPadding += "-";
                }

                String formattedTip = leftPadding + " \033[1;36m" + randomLine + "\033[0m " + rightPadding;
                System.out.println(formattedTip); // 使用 System.out.println 输出
            } else {
                logger.warning("The file is empty.");
            }
        } catch (IOException e) {
            logger.severe("Error reading file: " + e.getMessage());
        }
    }
}