package cn.har01d.alist_tvbox.controller;

import cn.har01d.alist_tvbox.service.AlipanTokenService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class AlipanTokenController {
    private final AlipanTokenService alipanTokenService;

    public AlipanTokenController(AlipanTokenService alipanTokenService) {
        this.alipanTokenService = alipanTokenService;
    }

    @PostMapping("/oauth/alipan/token")
    public ResponseEntity<Map<String, Object>> refreshToken(@RequestBody Map<String, String> request) {
        try {
            String refreshToken = request.get("refresh_token");
            if (refreshToken == null || refreshToken.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", "");
                errorResponse.put("message", "");
                errorResponse.put("token_type", "Bearer");
                errorResponse.put("access_token", "");
                errorResponse.put("refresh_token", "");
                errorResponse.put("expires_in", 0);
                return ResponseEntity.ok(errorResponse);
            }
            Map<String, Object> response = alipanTokenService.refreshToken(refreshToken);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "");
            errorResponse.put("message", "");
            errorResponse.put("token_type", "Bearer");
            errorResponse.put("access_token", "");
            errorResponse.put("refresh_token", "");
            errorResponse.put("expires_in", 0);
            return ResponseEntity.ok(errorResponse);
        }
    }
}
