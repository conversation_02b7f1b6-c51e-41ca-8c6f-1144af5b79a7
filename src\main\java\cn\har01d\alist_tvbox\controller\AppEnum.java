package cn.har01d.alist_tvbox.controller;

public enum AppEnum {
    WEB(1),
    IOS(6),
    IOS_115(8),
    ANDROID(9),
    ANDROID_115(11),
    IPAD_115(14),
    TV(15),
    QANDROID(16),
    WINDOWS(19),
    MAC(20),
    LINUX(21),
    WECHATMINI(22),
    ALIPAYMINI(23);

    private final int value;

    AppEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static String getEnumName(int value) {
        for (AppEnum appEnum : values()) {
            if (appEnum.getValue() == value) {
                return appEnum.name();
            }
        }
        return null;
    }

    public static String getEnumName(String name) {
        try {
            return AppEnum.valueOf(name.toUpperCase()).name();
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
