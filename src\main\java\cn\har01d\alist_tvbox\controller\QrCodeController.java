package cn.har01d.alist_tvbox.controller;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.logging.Logger;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLDecoder;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.web.bind.annotation.*;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.awt.image.BufferedImage;

import javax.imageio.ImageIO;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;

import cn.har01d.alist_tvbox.controller.QrCodeController.QRCodeGenerator;
import cn.har01d.alist_tvbox.dto.OpenApiDto;
import cn.har01d.alist_tvbox.service.SettingService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import java.net.HttpCookie;
// import cn.har01d.alist_tvbox.entity.Setting;
import cn.har01d.alist_tvbox.service.ShareService;

import org.springframework.web.client.RestTemplate;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

@RestController
@RequestMapping("/api")
public class QrCodeController {

    private static final Logger logger = Logger.getLogger(QrCodeController.class.getName());
    private final Map<String, Map<String, String>> sessionStore = new ConcurrentHashMap<>();
    private String timestamp;
    private String uniqueId;
    private String wifimac;
    @Autowired
    private SettingService settingService; // 变量名修改为 settingService
    @Autowired
    private ShareService shareService;
    @Autowired
    private RestTemplate restTemplate;

    // 添加阿里云盘V2扫码相关的成员变量
    private final Map<String, AliCloudV2Session> aliV2Sessions = new ConcurrentHashMap<>();

    @GetMapping("/qrcode")
    public ResponseEntity<Map<String, String>> generateQRCode(@RequestParam String client) throws IOException {
        // 获取二维码 token
        Map<String, Object> tokenResponse = getQRCodeToken();
        Map<String, Object> data = (Map<String, Object>) tokenResponse.get("data");
        String uid = (String) data.get("uid");
        String time = String.valueOf(data.get("time"));
        String sign = (String) data.get("sign");

        // 获取最终二维码 URL
        String finalQRCodeUrl = getFinalQRCodeUrl(uid);

        // 获取二维码图片并编码为 Base64
        String base64QRCode = fetchAndEncodeQRCode(finalQRCodeUrl);

        Map<String, String> response = new HashMap<>();
        response.put("qrcode", base64QRCode);
        response.put("uid", uid); // 返回 uid 以便前端使用
        response.put("time", time); // 返回 time 以便前端使用
        response.put("sign", sign); // 返回 sign 以便前端使用
        response.put("client", client); // 返回 client 以便前端使用

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private Map<String, Object> getQRCodeToken() throws IOException {
        String api = "https://qrcodeapi.115.com/api/1.0/web/1.0/token/";
        URL url = new URL(api);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String inputLine;
        StringBuilder content = new StringBuilder();
        while ((inputLine = in.readLine()) != null) {
            content.append(inputLine);
        }
        in.close();
        conn.disconnect();
    
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(content.toString(), Map.class);
    }    

    private String getFinalQRCodeUrl(String uid) {
        return "https://qrcodeapi.115.com/api/1.0/web/1.0/qrcode?uid=" + uid;
    }

    private String fetchAndEncodeQRCode(String url) throws IOException {
        URL qrCodeUrl = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) qrCodeUrl.openConnection();
        conn.setRequestMethod("GET");
        InputStream inputStream = conn.getInputStream();
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] qrCodeImageBytes = byteArrayOutputStream.toByteArray();
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(qrCodeImageBytes);
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, String>> getQRCodeStatus(@RequestParam String uid, @RequestParam String time, @RequestParam String sign, @RequestParam String client) throws IOException {
        Map<String, String> payload = new HashMap<>();
        payload.put("uid", uid);
        payload.put("time", time);
        payload.put("sign", sign);
        Map<String, Object> statusResponse = getQRCodeStatus(payload);
        Map<String, Object> data = (Map<String, Object>) statusResponse.get("data");

        Map<String, String> response = new HashMap<>();
        if (data != null) {
            Integer status = (Integer) data.get("status");
            if (status != null && status == 2) { // 扫码成功
                String cookie = postQRCodeResult(uid, client); // 根据前端选择的客户端获取cookie
                response.put("status", "success");
                response.put("cookie", cookie);
            } else if (status != null && (status == -1 || status == -2)) { // 扫码失败
                response.put("status", "failure");
            } else if (status != null && status == 1) { // 扫码成功但未确认
                response.put("status", "waiting");
            } else {
                response.put("status", "pending"); // 二维码还没有被扫描
            }
        } else {
            response.put("status", "unknown");
        }

        return new ResponseEntity<>(response, HttpStatus.OK);
    }


    private Map<String, Object> getQRCodeStatus(Map<String, String> payload) throws IOException {
        String statusApi = "https://qrcodeapi.115.com/get/status/?" + payload.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        URL url = new URL(statusApi);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String inputLine;
        StringBuilder content = new StringBuilder();
        while ((inputLine = in.readLine()) != null) {
            content.append(inputLine);
        }
        in.close();
        conn.disconnect();
    
        // ==========添加日志记录
        // logger.info("Request URL: " + statusApi);
        // logger.info("Response: " + content.toString());

        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(content.toString(), Map.class);
    }

    private String postQRCodeResult(String uid, String app) throws IOException {
        String appName = AppEnum.getEnumName(app);
        if (appName == null) {
            throw new IllegalArgumentException("Invalid app name: " + app);
        }
        String api = "https://passportapi.115.com/app/1.0/" + appName.toLowerCase() + "/1.0/login/qrcode/";
        URL url = new URL(api);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        String payload = "app=" + appName + "&account=" + uid;
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = payload.getBytes("utf-8");
            os.write(input, 0, input.length);
        }
        BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
        StringBuilder response = new StringBuilder();
        String responseLine;
        while ((responseLine = in.readLine()) != null) {
            response.append(responseLine.trim());
        }
        in.close();
        conn.disconnect();

        // 解析响应获取 cookie
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> responseMap = mapper.readValue(response.toString(), Map.class);
        Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
        Map<String, String> cookieData = (Map<String, String>) data.get("cookie");
        
        // 添加日志记录
        // logger.info("========== QR Code Result Response: " + responseMap.toString());
        
        return String.join("; ", cookieData.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .toArray(String[]::new));
    }

    @GetMapping("/get_tv_token")
    public Map<String, String> getTvToken() throws IOException {
        try {
            // 获取timestamp
            String timestampUrl = "http://api.extscreen.com/timestamp";
            String timestampResponse = sendGetRequest(timestampUrl);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode timestampJson = mapper.readTree(timestampResponse);
            timestamp = timestampJson.get("data").get("timestamp").asText(); // 更新全局变量

            // 模拟Python服务获取二维码链接和sid
            String url = "http://api.extscreen.com/aliyundrive/qrcode";
            Map<String, String> params = new HashMap<>();
            params.put("scopes", "user:base,file:all:read,file:all:write");
            params.put("width", "300");
            params.put("height", "300");

            // 构造请求头
            uniqueId = UUID.randomUUID().toString().replace("-", ""); // 更新全局变量
            wifimac = String.valueOf(100000000000L + Math.abs(new Random().nextLong() % 900000000000L)); // 更新全局变量，生成12位随机字符串

            Map<String, String> headers = getHeaders(timestamp, uniqueId, wifimac);

            // 发送POST请求获取二维码链接和sid
            String response = sendPostRequest(url, params, headers, false);
            JsonNode jsonNode = mapper.readTree(response);
            String qrLink = "https://www.aliyundrive.com/o/oauth/authorize?sid=" + jsonNode.get("data").get("sid").asText();
            String sid = jsonNode.get("data").get("sid").asText();

            // 打印调试信息
            // System.out.println("==========getTvToken - timestamp: " + timestamp);
            // System.out.println("==========getTvToken - uniqueId: " + uniqueId);
            // System.out.println("==========getTvToken - wifimac: " + wifimac);

            // 生成二维码图片并编码为 Base64
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(qrLink, BarcodeFormat.QR_CODE, 300, 300);
            BufferedImage qrImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(qrImage, "png", baos);
            String base64QRCode = Base64.getEncoder().encodeToString(baos.toByteArray());

            // 返回二维码图片的 Base64 编码
            Map<String, String> result = new HashMap<>();
            result.put("qr_code", base64QRCode);
            result.put("sid", sid);
            // // ===1130 增加open token认证地址检查
            // Setting openTokenSetting = settingService.get("open_token_url");
            // String currentUrl = openTokenSetting != null ? openTokenSetting.getValue() : null;
            // String expectedUrl = "https://www.voicehub.top/api/v1/oauth/alipan/token";

            // if (currentUrl == null || !expectedUrl.equals(currentUrl)) {
            //     Setting clientIdSetting = settingService.get("open_api_client_id");
            //     Setting clientSecretSetting = settingService.get("open_api_client_secret");
            //     String clientId = clientIdSetting != null ? clientIdSetting.getValue() : "";
            //     String clientSecret = clientSecretSetting != null ? clientSecretSetting.getValue() : "";
            //     settingService.update(new Setting("open_token_url", expectedUrl));
            //     OpenApiDto dto = new OpenApiDto();
            //     dto.setUrl(expectedUrl);
            //     dto.setClientId(clientId);  // 使用当前值
            //     dto.setClientSecret(clientSecret);  // 使用当前值
            //     shareService.updateOpenTokenUrl(dto);
            //     result.put("url_updated", "true");
            // } else {
            //     result.put("url_updated", "false");  // 明确设置为 false
            // }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new IOException("获取二维码失败", e);
        }
    }

    @GetMapping("/check_qr_status")
    public Map<String, String> checkQrStatus(@RequestParam String sid) throws IOException {
        try {
            // 模拟Python服务检查二维码状态
            String url = "https://openapi.alipan.com/oauth/qrcode/" + sid + "/status";
            String response = sendGetRequest(url);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response);
            String status = jsonNode.get("status").asText();
            String authCode = null;
            if ("LoginSuccess".equals(status)) {
                authCode = jsonNode.get("authCode").asText();
            }

            Map<String, String> result = new HashMap<>();
            result.put("auth_code", authCode);
            return result;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Request interrupted", e);
        }
    }

    @PostMapping("/get_tokens")
    public Map<String, String> getTokens(@RequestBody Map<String, String> request) throws IOException {
        try {
            String authCode = request.get("auth_code");
            String sid = request.get("sid");
            // System.out.println("authCode: " + authCode);
            // System.out.println("sid: " + sid);

            // 打印调试信息
            // System.out.println("==========getTokens - timestamp: " + timestamp);
            // System.out.println("==========getTokens - uniqueId: " + uniqueId);
            // System.out.println("==========getTokens - wifimac: " + wifimac);

            String url = "http://api.extscreen.com/aliyundrive/v3/token";
            Map<String, String> params = new HashMap<>();
            params.put("code", authCode);

            // 构造请求头
            Map<String, String> headers = getHeaders(timestamp, uniqueId, wifimac);

            // 发送POST请求获取token数据
            String response = sendPostRequest(url, params, headers, false);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response);
            String ciphertext = jsonNode.get("data").get("ciphertext").asText();
            String iv = jsonNode.get("data").get("iv").asText();
            // System.out.println("==========ciphertext: " + ciphertext);
            // System.out.println("==========iv: " + iv);

            // 解密token数据
            String tokenData = decrypt(ciphertext, iv, timestamp, uniqueId, wifimac);
            JsonNode tokenJson = mapper.readTree(tokenData);
            String refreshToken = tokenJson.get("refresh_token").asText();

            Map<String, String> result = new HashMap<>();
            result.put("refresh_token", refreshToken);
            return result;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Request interrupted", e);
        } catch (Exception e) {
            throw new IOException("Decryption failed", e);
        }
    }


    public class ParameterStringBuilder {
        public static String getParamsString(Map<String, String> params) throws UnsupportedEncodingException {
            StringBuilder result = new StringBuilder();

            for (Map.Entry<String, String> entry : params.entrySet()) {
                result.append(URLEncoder.encode(entry.getKey(), "UTF-8"));
                result.append("=");
                result.append(URLEncoder.encode(entry.getValue(), "UTF-8"));
                result.append("&");
            }

            String resultString = result.toString();
            return resultString.length() > 0
                    ? resultString.substring(0, resultString.length() - 1)
                    : resultString;
        }
    }

    
    private String sendPostRequest(String url, Map<String, String> params, Map<String, String> headers, boolean isJson) throws IOException, InterruptedException {
        // 允许设置受限制的头字段
        System.setProperty("sun.net.http.allowRestrictedHeaders", "true");
    
        URL obj = new URL(url);
        HttpURLConnection con = (HttpURLConnection) obj.openConnection();
        con.setRequestMethod("POST");
        con.setDoOutput(true);
    
        // 如果没有传递headers，使用一个空的headers
        if (headers == null) {
            headers = new HashMap<>();
        }
    
        // 添加头字段
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            con.setRequestProperty(entry.getKey(), entry.getValue());
        }
    
        // 添加接受压缩的header
        con.setRequestProperty("Accept-Encoding", "gzip, deflate");
    
        // 添加参数
        if (isJson) {
            ObjectMapper mapper = new ObjectMapper();
            String requestBody = mapper.writeValueAsString(params);
            try (DataOutputStream out = new DataOutputStream(con.getOutputStream())) {
                out.writeBytes(requestBody);
                out.flush();
            }
        } else {
            DataOutputStream out = new DataOutputStream(con.getOutputStream());
            out.writeBytes(ParameterStringBuilder.getParamsString(params));
            out.flush();
            out.close();
        }
    
        int responseCode = con.getResponseCode();
        // System.out.println("Response Code: " + responseCode);
    
        // 处理响应
        InputStream inputStream;
        String contentEncoding = con.getHeaderField("Content-Encoding");
        
        // 根据响应码选择输入流
        if (responseCode >= 400) {
            inputStream = con.getErrorStream();
        } else {
            inputStream = con.getInputStream();
        }
        
        // 如果响应是压缩的，解压缩它
        if ("gzip".equalsIgnoreCase(contentEncoding)) {
            inputStream = new GZIPInputStream(inputStream);
        }
    
        // 读取响应
        try (BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            String responseStr = response.toString();
            // System.out.println("Full response: " + responseStr);
            return responseStr;
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
    

    private boolean isValidJson(String json) {
        try {
            final ObjectMapper mapper = new ObjectMapper();
            mapper.readTree(json);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    private String sendGetRequest(String url) throws IOException, InterruptedException {
        return sendGetRequest(url, null);
    }
    
    private String sendGetRequest(String url, Map<String, String> headers) throws IOException, InterruptedException {
        // 允许设置受限制的头字段
        System.setProperty("sun.net.http.allowRestrictedHeaders", "true");
    
        URL obj = new URL(url);
        HttpURLConnection con = (HttpURLConnection) obj.openConnection();
        con.setRequestMethod("GET");
    
        // 如果没有传递headers，使用一个空的headers
        if (headers == null) {
            headers = new HashMap<>();
        }
    
        // 添加头字段
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            con.setRequestProperty(entry.getKey(), entry.getValue());
        }
    
        int responseCode = con.getResponseCode();
        System.out.println("Response Code: " + responseCode);
    
        BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
        String inputLine;
        StringBuilder response = new StringBuilder();
        while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
        }
        in.close();
    
        // 提取 Set-Cookie 头字段
        Map<String, List<String>> headerFields = con.getHeaderFields();
        StringBuilder cookies = new StringBuilder();
        for (Map.Entry<String, List<String>> entry : headerFields.entrySet()) {
            if ("Set-Cookie".equalsIgnoreCase(entry.getKey())) {
                for (String cookie : entry.getValue()) {
                    // 只提取cookie的名称和值
                    String[] cookieParts = cookie.split(";");
                    if (cookieParts.length > 0) {
                        cookies.append(cookieParts[0].trim()).append("; ");
                    }
                }
            }
        }
    
        // 打印所有响应头字段
        // System.out.println("Response Headers: " + headerFields);
    
        // 将响应体和 cookies 一起返回
        return response.toString() + "\nCookies: " + cookies.toString().replaceAll("; $", "");
    }
    
    
    
    private byte[] sendGetRequestBytes(String url) throws IOException, InterruptedException {
        return sendGetRequestBytes(url, null);
    }

    // 双参数方法，带headers
    private byte[] sendGetRequestBytes(String url, Map<String, String> headers) throws IOException, InterruptedException {
        // System.out.println("Sending GET request to: " + url);

        System.setProperty("sun.net.http.allowRestrictedHeaders", "true");

        URL obj = new URL(url);
        HttpURLConnection con = (HttpURLConnection) obj.openConnection();
        con.setRequestMethod("GET");

        // 如果没有传递headers，使用一个空的headers
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 添加头字段
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            con.setRequestProperty(entry.getKey(), entry.getValue());
        }

        int responseCode = con.getResponseCode();
        System.out.println("Response Code: " + responseCode);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (InputStream in = con.getInputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
        }

        return baos.toByteArray();
    }
    
    private String decrypt(String ciphertext, String iv, String timestamp, String uniqueId, String wifimac) throws Exception {
        String key = generateKey(timestamp, uniqueId, wifimac);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(hexStringToByteArray(iv));
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decodedCiphertext = Base64.getDecoder().decode(ciphertext);
        byte[] decryptedBytes = cipher.doFinal(decodedCiphertext);
        // 打印调试信息
        // System.out.println("==========decrypt - ciphertext: " + ciphertext);
        // System.out.println("==========decrypt - iv: " + iv);
        // System.out.println("==========decrypt - timestamp: " + timestamp);
        // System.out.println("==========decrypt - uniqueId: " + uniqueId);
        // System.out.println("==========decrypt - wifimac: " + wifimac);
        // System.out.println("==========decrypt - key: " + key);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
    

    private String generateKey(String timestamp, String uniqueId, String wifimac) throws NoSuchAlgorithmException {
        Map<String, String> params = getParams(timestamp, uniqueId, wifimac);
        List<String> sortedKeys = new ArrayList<>(params.keySet());
        Collections.sort(sortedKeys);
        StringBuilder concatenatedParams = new StringBuilder();
        for (String key : sortedKeys) {
            if (!key.equals("t")) {
                concatenatedParams.append(params.get(key));
            }
        }
        String hashedKey = h(concatenatedParams.toString().toCharArray(), timestamp);
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(hashedKey.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        // 打印调试信息
        // System.out.println("==========generateKey - timestamp: " + timestamp);
        // System.out.println("==========generateKey - uniqueId: " + uniqueId);
        // System.out.println("==========generateKey - wifimac: " + wifimac);
        // System.out.println("==========generateKey - concatenatedParams: " + concatenatedParams.toString());
        // System.out.println("==========generateKey - hashedKey: " + hashedKey);
        // System.out.println("==========generateKey - finalKey: " + sb.toString());
        return sb.toString();
    }
    

    private byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    private String h(char[] charArray, String modifier) {
        List<Character> uniqueChars = new ArrayList<>();
        for (char c : charArray) {
            if (!uniqueChars.contains(c)) {
                uniqueChars.add(c);
            }
        }
        int numericModifier = Integer.parseInt(modifier.substring(7));
        StringBuilder transformedString = new StringBuilder();
        for (char c : uniqueChars) {
            int transformedChar = Math.abs(c - (numericModifier % 127) - 1);
            transformedString.append((char) (transformedChar < 33 ? transformedChar + 33 : transformedChar));
        }
        return transformedString.toString();
    }

    private Map<String, String> getHeaders(String timestamp, String uniqueId, String wifimac) {
        Map<String, String> headers = new HashMap<>();
        headers.put("token", "6733b42e28cdba32");
        headers.put("User-Agent", "Mozilla/5.0 (Linux; U; Android 9; zh-cn; SM-S908E Build/TP1A.220624.014) AppleWebKit/533.1 (KHTML, like Gecko) Mobile Safari/533.1");
        headers.put("Host", "api.extscreen.com");
        headers.putAll(getParams(timestamp, uniqueId, wifimac));
        return headers;
    }

    private Map<String, String> getParams(String timestamp, String uniqueId, String wifimac) {
        Map<String, String> params = new HashMap<>();
        params.put("akv", "2.8.1496");
        params.put("apv", "1.3.6");
        params.put("b", "OnePlus");
        params.put("d", uniqueId);
        params.put("m", "PJX110");
        params.put("mac", "");
        params.put("n", "PJX110");
        params.put("t", timestamp);
        params.put("wifiMac", wifimac);
        return params;
    }

    // ===1106 修改阿里token获取
    @RestController
    public class AliTokenController {

        private static final String QR_CODE_URL = "https://api.xhofe.top/alist/ali/qr";
        private static final String CHECK_QR_STATUS_URL = "https://api.xhofe.top/alist/ali/ck";
        private static final Map<String, String> HEADERS = Map.ofEntries(
            Map.entry("Accept", "*/*"),
            Map.entry("Accept-Encoding", "gzip, deflate, br, zstd"),
            Map.entry("Accept-Language", "zh-CN,zh;q=0.9"),
            Map.entry("Content-Type", "application/json"),
            Map.entry("Origin", "https://alist.nn.ci"),
            Map.entry("Referer", "https://alist.nn.ci/"),
            Map.entry("Sec-Ch-Ua", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\""),
            Map.entry("Sec-Ch-Ua-Mobile", "?0"),
            Map.entry("Sec-Ch-Ua-Platform", "\"Windows\""),
            Map.entry("Sec-Fetch-Dest", "empty"),
            Map.entry("Sec-Fetch-Mode", "cors"),
            Map.entry("Sec-Fetch-Site", "cross-site"),
            Map.entry("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        );

        private volatile String ck;
        private volatile String t;

        @GetMapping("/api/get_ali_qr_code")
        public Map<String, String> getAliQRCode() throws IOException {
            try {
                //==1106 修改阿里token
                // System.out.println("Fetching QR code data from: " + QR_CODE_URL);
                String response = sendGetRequest(QR_CODE_URL);
                //==1106 修改阿里token
                // System.out.println("QR code data response: " + response);

                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(response);
                t = jsonNode.get("content").get("data").get("t").asText();
                String codeContent = jsonNode.get("content").get("data").get("codeContent").asText();
                ck = jsonNode.get("content").get("data").get("ck").asText();
                String qrUrl = "https://api.xhofe.top/qr/?size=400&text=" + codeContent;

                //==1106 修改阿里token
                // System.out.println("Fetching QR code image from: " + qrUrl);
                byte[] qrResponseBytes = sendGetRequestBytes(qrUrl);
                //==1106 修改阿里token
                // System.out.println("QR code image response (raw bytes): " + Arrays.toString(qrResponseBytes));

                ByteArrayInputStream imageStream = new ByteArrayInputStream(qrResponseBytes);
                BufferedImage qrImage = ImageIO.read(imageStream);
                if (qrImage == null) {
                    throw new IllegalArgumentException("Failed to decode QR code image!");
                }
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(qrImage, "png", baos);
                String base64QRCode = Base64.getEncoder().encodeToString(baos.toByteArray());

                //==1106 修改阿里token
                System.out.println("QR code generated successfully.");
                Map<String, String> result = new HashMap<>();
                result.put("qr_code", base64QRCode);
                return result;
            } catch (Exception e) {
                e.printStackTrace();
                throw new IOException("获取二维码失败", e);
            }
        }

        @GetMapping("/api/check_ali_qr_status")
        public Map<String, String> checkAliQrStatus() {
            Map<String, String> result = new HashMap<>();
            try {
                Map<String, String> data = Map.of("ck", ck, "t", t);
                //==1106 修改阿里token
                // System.out.println("==========Checking QR code status with data: " + data);
                String response = sendPostRequest(CHECK_QR_STATUS_URL, data, HEADERS, true);
                //==1106 修改阿里token
                // System.out.println("==========QR code status response: " + response);

                if (isValidJson(response)) {
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode jsonNode = mapper.readTree(response);
                    String qrCodeStatus = jsonNode.get("content").get("data").get("qrCodeStatus").asText();
                    if ("CONFIRMED".equals(qrCodeStatus)) {
                        String bizExt = jsonNode.get("content").get("data").get("bizExt").asText();
                        String decodedBizExt = new String(Base64.getDecoder().decode(bizExt), StandardCharsets.UTF_8);
                        JsonNode bizExtJson = mapper.readTree(decodedBizExt);
                        String refreshToken = bizExtJson.get("pds_login_result").get("refreshToken").asText();
                        result.put("status", "success");
                        result.put("refresh_token", refreshToken);
                        //==1106 修改阿里token
                        // System.out.println("==========QR code confirmed. Refresh token: " + refreshToken);
                    } else {
                        result.put("status", "unknown");
                        //==1106 修改阿里token
                        // System.out.println("QR code status unknown.");
                    }
                } else {
                    result.put("status", "failure");
                    // System.out.println("==========Invalid JSON response.");
                }
            } catch (Exception e) {
                e.printStackTrace();
                result.put("status", "failure");
                //==1106 修改阿里token
                // System.out.println("==========Failed to check QR code status.");
            }
            return result;
        }
    }

    @RestController
    public class AliOpenTokenController {

        // private static final String QR_CODE_URL = "https://api-cf.nn.ci/alist/ali_open/qr";
        // private static final String CHECK_QR_STATUS_URL = "https://api-cf.nn.ci/proxy/https://open.aliyundrive.com/oauth/qrcode/";
        private static final String QR_CODE_URL = "https://api.nn.ci/alist/ali_open/qr";
        private static final String CHECK_QR_STATUS_URL = "https://api.nn.ci/proxy/https://openapi.alipan.com/oauth/qrcode/";
        // private static final String CHECK_QR_STATUS_URL = "https://api.nn.ci/proxy/https://open.aliyundrive.com/oauth/qrcode/";
        private volatile String qrCodeId;
        // private volatile String lastStatus = "unknown";
        private volatile String refreshToken;
        private static String extractQRCodeId(String qrCodeUrl) throws Exception {
            URL url = new URL(qrCodeUrl);
            String path = url.getPath();
            List<String> components = Arrays.asList(path.split("/"));
            int qrcodeIndex = components.indexOf("qrcode");
            if (qrcodeIndex != -1 && qrcodeIndex + 1 < components.size()) {
                return components.get(qrcodeIndex + 1); // 只提取 qrcode 后面的第一个部分
            } else {
                throw new IllegalArgumentException("Invalid QR Code URL");
            }
        }

        private static final Map<String, String> HEADERS_3 = Map.ofEntries(
            Map.entry("Accept", "application/json"),
            Map.entry("Accept-Language", "zh-CN,zh;q=0.9"),
            Map.entry("Content-Length", "2"),
            Map.entry("Content-Type", "application/json"),
            Map.entry("Origin", "https://alist.nn.ci"),
            Map.entry("Priority", "u=1, i"),
            Map.entry("Referer", "https://alist.nn.ci/"),
            Map.entry("Sec-CH-UA", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\""),
            Map.entry("Sec-CH-UA-Mobile", "?0"),
            Map.entry("Sec-CH-UA-Platform", "\"Windows\""),
            Map.entry("Sec-Fetch-Dest", "empty"),
            Map.entry("Sec-Fetch-Mode", "cors"),
            Map.entry("Sec-Fetch-Site", "cross-site"),
            Map.entry("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        );

        @GetMapping("/api/get_ali_open_qrcode")
        public Map<String, String> getAliOpenQRCode() throws IOException {
            
            try {
                System.out.println("===Fetching QR code data from: " + QR_CODE_URL);
                // String response = sendGetRequest(QR_CODE_URL);

                // 使用请求头发送 POST 请求
                String response = sendPostRequest(QR_CODE_URL, new HashMap<>(), HEADERS_3, true);
                // String response = sendGetRequest(QR_CODE_URL, HEADERS_3);
                System.out.println("===QR code data response: " + response);

                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(response);
                String qrCodeUrl = jsonNode.get("qrCodeUrl").asText();
                qrCodeId = extractQRCodeId(qrCodeUrl);

                byte[] qrResponseBytes = sendGetRequestBytes(qrCodeUrl);
                // System.out.println("QR code image response (raw bytes): " + Arrays.toString(qrResponseBytes));

                ByteArrayInputStream imageStream = new ByteArrayInputStream(qrResponseBytes);
                BufferedImage qrImage = ImageIO.read(imageStream);
                if (qrImage == null) {
                    throw new IllegalArgumentException("Failed to decode QR code image!");
                }
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(qrImage, "png", baos);
                String base64QRCode = Base64.getEncoder().encodeToString(baos.toByteArray());

                System.out.println("QR code generated successfully.");
                Map<String, String> result = new HashMap<>();
                result.put("qr_code", base64QRCode);
                return result;
            } catch (Exception e) {
                e.printStackTrace();
                throw new IOException("获取二维码失败", e);
            }
        }

        @GetMapping("/api/check_ali_open_qr_status")
        public Map<String, String> checkAliOpenQrStatus() {
            Map<String, String> result = new HashMap<>();
            try {
                String url = CHECK_QR_STATUS_URL + qrCodeId + "/status";
                System.out.println("===Checking QR code status with URL: " + url);
                String response = sendGetRequest(url);
                System.out.println("===QR code status response: " + response);

                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(response);
                String status = jsonNode.get("status").asText();
                if ("LoginSuccess".equals(status)) {
                    String authCode = jsonNode.get("authCode").asText();
                    Map<String, String> data = Map.of(
                        "code", authCode,
                        "grant_type", "authorization_code",
                        "client_id", "",
                        "client_secret", ""
                    );
                    String tokenResponse = sendPostRequest("https://api.nn.ci/alist/ali_open/code", data, HEADERS_3, true);
                    JsonNode tokenJson = mapper.readTree(tokenResponse);
                    refreshToken = tokenJson.get("refresh_token").asText();
                    // lastStatus = "success";
                    result.put("status", "success");
                    result.put("refresh_token", refreshToken);
                    System.out.println("===QR code confirmed. Refresh token: " + refreshToken);
                } else {
                    // lastStatus = "unknown";
                    result.put("status", "unknown");
                    System.out.println("===QR code status unknown.");
                }
            } catch (Exception e) {
                e.printStackTrace();
                // lastStatus = "failure";
                result.put("status", "failure");
                System.out.println("===Failed to check QR code status.");
            }
            return result;
        }
    }

    @RestController
    public class AliOpenTokenController1 {
    
        private static final String QR_CODE_URL = "https://api.xhofe.top/alist/ali_open/qr";
        // private static final String CHECK_QR_STATUS_URL = "https://api.xhofe.top/proxy/https://open.aliyundrive.com/oauth/qrcode/";
        private static final String CHECK_QR_STATUS_URL = "https://api.xhofe.top/proxy/https://openapi.alipan.com/oauth/qrcode/";
        private volatile String qrCodeId;
        private volatile String refreshToken;
    
        private static final Map<String, String> HEADERS = Map.ofEntries(
            Map.entry("Accept", "*/*"),
            Map.entry("Accept-Encoding", "gzip, deflate, br, zstd"),
            Map.entry("Accept-Language", "zh-CN,zh;q=0.9"),
            Map.entry("Content-Type", "application/json"),
            Map.entry("Origin", "https://alist.nn.ci"),
            Map.entry("Referer", "https://alist.nn.ci/"),
            Map.entry("Sec-Ch-Ua", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\""),
            Map.entry("Sec-Ch-Ua-Mobile", "?0"),
            Map.entry("Sec-Ch-Ua-Platform", "\"Windows\""),
            Map.entry("Sec-Fetch-Dest", "empty"),
            Map.entry("Sec-Fetch-Mode", "cors"),
            Map.entry("Sec-Fetch-Site", "cross-site"),
            Map.entry("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        );

        private static final Map<String, String> HEADERS_2 = Map.ofEntries(
            Map.entry("Accept", "*/*"),
            Map.entry("Accept-Encoding", "gzip, deflate, br, zstd"),
            Map.entry("Accept-Language", "zh-CN,zh;q=0.9"),
            Map.entry("Content-Length", "111"),
            Map.entry("Content-Type", "application/json"),
            Map.entry("Origin", "https://alist.nn.ci"),
            Map.entry("Priority", "u=1, i"),
            Map.entry("Referer", "https://alist.nn.ci/"),
            Map.entry("Sec-CH-UA", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\""),
            Map.entry("Sec-CH-UA-Mobile", "?0"),
            Map.entry("Sec-CH-UA-Platform", "\"Windows\""),
            Map.entry("Sec-Fetch-Dest", "empty"),
            Map.entry("Sec-Fetch-Mode", "cors"),
            Map.entry("Sec-Fetch-Site", "cross-site"),
            Map.entry("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        );
    
        @GetMapping("/api/get_ali_open_qrcode1")
        public Map<String, String> getAliOpenQRCode1() throws IOException, InterruptedException {
            try {
                // System.out.println("Fetching QR code data from: " + QR_CODE_URL);
                String response = sendGetRequest(QR_CODE_URL, HEADERS);
                // System.out.println("QR code data response: " + response);

                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(response);
                String qrCodeUrl = jsonNode.get("qrCodeUrl").asText();
                qrCodeId = qrCodeUrl.substring(qrCodeUrl.lastIndexOf('/') + 1);

                byte[] qrResponseBytes = sendGetRequestBytes(qrCodeUrl, HEADERS);
                // System.out.println("QR code image response (raw bytes): " + Arrays.toString(qrResponseBytes));

                ByteArrayInputStream imageStream = new ByteArrayInputStream(qrResponseBytes);
                BufferedImage qrImage = ImageIO.read(imageStream);
                if (qrImage == null) {
                    throw new IllegalArgumentException("Failed to decode QR code image!");
                }
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(qrImage, "png", baos);
                String base64QRCode = Base64.getEncoder().encodeToString(baos.toByteArray());

                System.out.println("QR code generated successfully.");
                Map<String, String> result = new HashMap<>();
                result.put("qr_code", base64QRCode);
                return result;
            } catch (Exception e) {
                e.printStackTrace();
                throw new IOException("获取二维码失败", e);
            }
        }

        @GetMapping("/api/check_ali_open_qr_status1")
        public Map<String, String> checkAliOpenQrStatus1() {
            Map<String, String> result = new HashMap<>();
            try {
                String url = CHECK_QR_STATUS_URL + qrCodeId + "/status";
                // System.out.println("Checking QR code status with URL: " + url);
                String response = sendGetRequest(url, HEADERS);
                // System.out.println("QR code status response: " + response);

                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(response);
                String status = jsonNode.get("status").asText();
                if ("LoginSuccess".equals(status)) {
                    String authCode = jsonNode.get("authCode").asText();
                    Map<String, String> data = new HashMap<>();
                    data.put("code", authCode);
                    data.put("grant_type", "authorization_code");
                    data.put("client_id", "");
                    data.put("client_secret", "");
                    String tokenResponse = sendPostRequest("https://api.xhofe.top/alist/ali_open/code", data, HEADERS_2, true);
                    JsonNode tokenJson = mapper.readTree(tokenResponse);
                    refreshToken = tokenJson.get("refresh_token").asText();
                    result.put("status", "success");
                    result.put("refresh_token", refreshToken);
                    // System.out.println("QR code confirmed. Refresh token: " + refreshToken);
                } else {
                    result.put("status", "unknown");
                    // System.out.println("QR code status unknown.");
                }
            } catch (Exception e) {
                e.printStackTrace();
                result.put("status", "failure");
                // System.out.println("Failed to check QR code status.");
            }
            return result;
        }
    }

    public class QRCodeGenerator {
        public static byte[] generateQRCodeImage(String text, int width, int height) throws WriterException, IOException {
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height);

            ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", pngOutputStream);
            return pngOutputStream.toByteArray();
        }
    }

    private String extractCookie(String response) {
        List<HttpCookie> cookies = HttpCookie.parse(response);
        StringBuilder cookieString = new StringBuilder();
        for (HttpCookie cookie : cookies) {
            cookieString.append(cookie.getName()).append("=").append(cookie.getValue()).append("; ");
        }
        return cookieString.toString().trim();
    }

    private String extractCookiesFromResponse(String response) {
        String[] parts = response.split("\nCookies: ");
        if (parts.length > 1) {
            return parts[1];
        }
        return "";
    }

    // ===1229 获取puus字段修复cookie问题
    private String getPuusCookie(String baseCookie, boolean isUC) throws IOException, InterruptedException {
        // 根据不同网盘设置不同的URL和Referer
        String url = isUC 
            ? "https://pc-api.uc.cn/1/clouddrive/file/sort?pr=UCBrowser&fr=pc&pdir_fid=0&_page=1&_size=50&_fetch_total=1&_fetch_sub_dirs=0&_sort=file_type:asc,updated_at:desc"
            : "https://drive-pc.quark.cn/1/clouddrive/config?pr=ucpro&fr=pc&uc_param_str=";
        String referer = isUC ? "https://drive.uc.cn" : "https://pan.quark.cn";
        
        // 使用已有的headers map来设置请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", baseCookie);
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/18.3.5.4-b478491100 Safari/537.36 Channel/pckk_other_ch");
        headers.put("Referer", referer);

        // ===1229调试cookie获取
        // System.out.println("1229==========URL: " + url);
        // System.out.println("1229==========Headers: " + headers);
        
        // 使用现有的sendGetRequest方法发送请求
        String response = sendGetRequest(url, headers);
        // System.out.println("1229==========Response: " + response);
        
        // 从响应中提取set-cookie（不区分大小写）
        String[] lines = response.split("\n");
        String puusCookie = "";
        for (String line : lines) {
            // System.out.println("1229==========Processing line: " + line);
            if (line.toLowerCase().startsWith("cookies:")) {
                String cookieValue = line.substring(line.indexOf(":") + 1).trim();
                // System.out.println("1229==========Found cookies: " + cookieValue);
                if (cookieValue.contains("__puus=")) {
                    int startIndex = cookieValue.indexOf("__puus=");
                    int endIndex = cookieValue.indexOf(";", startIndex);
                    if (endIndex == -1) {
                        puusCookie = cookieValue.substring(startIndex);
                    } else {
                        puusCookie = cookieValue.substring(startIndex, endIndex);
                    }
                    // System.out.println("1229==========Extracted puusCookie: " + puusCookie);
                    break;
                }
            }
        }
        
        // 返回完整的cookie字符串，将puusCookie放在最前面
        String finalCookie = puusCookie.isEmpty() ? baseCookie : puusCookie + "; " + baseCookie;
        // System.out.println("1229==========Final Cookie: " + finalCookie); // 调试输出最终的cookie
        return finalCookie;
    }

    @RestController
    public class QuarkController {

        private static final String CLIENT_ID = "532";
        private static final String QR_CODE_URL = "https://uop.quark.cn/cas/ajax/getTokenForQrcodeLogin?client_id=" + CLIENT_ID + "&v=1.2&request_id=";
        private static final String STATUS_URL = "https://uop.quark.cn/cas/ajax/getServiceTicketByQrcodeToken?client_id=" + CLIENT_ID + "&v=1.2&token=";
        private static final String INFO_URL = "https://pan.quark.cn/account/info?st=";

        @GetMapping("/api/qrcode_quark")
        public ResponseEntity<byte[]> getQRCodeForQuark() throws IOException, InterruptedException, WriterException {
            String requestId = UUID.randomUUID().toString();
            // System.out.println("Request ID: " + requestId);
            
            String qrCodeResponse = sendGetRequest(QR_CODE_URL + "?client_id=" + CLIENT_ID + "&v=1.2&request_id=" + requestId);
            // System.out.println("QR Code Response: " + qrCodeResponse);
            
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(qrCodeResponse);
            String token = jsonNode.get("data").get("members").get("token").asText();
            // System.out.println("Token: " + token);

            String qrCodeUrl = "https://su.quark.cn/4_eMHBJ?token=" + token + "&client_id=" + CLIENT_ID + "&ssb=weblogin&uc_param_str=&uc_biz_str=S%3Acustom%7COPT%3ASAREA%400%7COPT%3AIMMERSIVE%401%7COPT%3ABACK_BTN_STYLE%400";
            // System.out.println("QR Code URL: " + qrCodeUrl);
            
            byte[] qrCodeImage = QRCodeGenerator.generateQRCodeImage(qrCodeUrl, 300, 300);
            // System.out.println("Generated QR Code Image: " + Arrays.toString(qrCodeImage));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.set("token", token);
            return ResponseEntity.ok().headers(headers).body(qrCodeImage);
        }

        @GetMapping("/api/status_quark")
        public Map<String, String> getQRCodeStatusForQuark(@RequestParam String token) throws IOException, InterruptedException {
            String statusResponse = sendGetRequest(STATUS_URL + token + "&request_id=" + UUID.randomUUID().toString());
            // System.out.println("Status Response: " + statusResponse);
            
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(statusResponse);
            Map<String, String> result = new HashMap<>();
            if (jsonNode.get("status").asInt() == 2000000) {
                String serviceTicket = jsonNode.get("data").get("members").get("service_ticket").asText();
                // System.out.println("Service Ticket: " + serviceTicket);
                
                String infoResponse = sendGetRequest(INFO_URL + serviceTicket + "&lw=scan");
                // System.out.println("Info Response: " + infoResponse);
                
                String cookies = extractCookiesFromResponse(infoResponse);
                String fullCookies = getPuusCookie(cookies, false);
                // System.out.println("Extracted Cookies: " + fullCookies);
                
                result.put("status", "success");
                result.put("cookie", fullCookies);
            } else if (jsonNode.get("status").asInt() == 50004002) {
                result.put("status", "failure");
            } else {
                result.put("status", "unknown");
            }
            return result;
        }
    }
    
    @RestController
    public class UCController {

        private static final String CLIENT_ID = "381";
        private static final String QR_CODE_URL = "https://api.open.uc.cn/cas/ajax/getTokenForQrcodeLogin";
        private static final String STATUS_URL = "https://api.open.uc.cn/cas/ajax/getServiceTicketByQrcodeToken";
        private static final String INFO_URL = "https://drive.uc.cn/account/info?st=";

        @GetMapping("/api/qrcode_uc")
        public ResponseEntity<byte[]> getQRCodeForUC() throws IOException, InterruptedException, WriterException {
            long __t = System.currentTimeMillis();
            String qrCodeResponse = sendPostRequest(QR_CODE_URL + "?__dt=" + new Random().nextInt(900) + 100 + "&__t=" + __t, Map.of("client_id", CLIENT_ID, "v", "1.2", "request_id", String.valueOf(__t)), null, false);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(qrCodeResponse);
            String token = jsonNode.get("data").get("members").get("token").asText();
            // System.out.println("Token: " + token); 

            String qrCodeUrl = "https://su.uc.cn/1_n0ZCv?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&token=" + token + "&client_id=" + CLIENT_ID + "&uc_biz_str=S%3Acustom%7CC%3Atitlebar_fix";
            byte[] qrCodeImage = QRCodeGenerator.generateQRCodeImage(qrCodeUrl, 300, 300);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.set("token", token);
            return ResponseEntity.ok().headers(headers).body(qrCodeImage);
        }

        @GetMapping("/api/status_uc")
        public Map<String, String> getQRCodeStatusForUC(@RequestParam String token) throws IOException, InterruptedException {
            long __t = System.currentTimeMillis();
            String statusResponse = sendPostRequest(STATUS_URL + "?__dt=" + new Random().nextInt(900) + 100 + "&__t=" + __t, Map.of("client_id", CLIENT_ID, "v", "1.2", "request_id", String.valueOf(__t), "token", token), null, false);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(statusResponse);
            Map<String, String> result = new HashMap<>();
            if (jsonNode.get("status").asInt() == 2000000) {
                String serviceTicket = jsonNode.get("data").get("members").get("service_ticket").asText();
                String infoResponse = sendGetRequest(INFO_URL + serviceTicket);
                String cookies = extractCookiesFromResponse(infoResponse);
                String fullCookies = getPuusCookie(cookies, true);
                // System.out.println("Extracted Cookies: " + fullCookies);
                result.put("status", "success");
                result.put("cookie", fullCookies);
            } else if (jsonNode.get("status").asInt() == 50004002) {
                result.put("status", "failure");
            } else {
                result.put("status", "unknown");
            }
            return result;
        }
    }

    // 生成阿里云盘V2二维码
    @GetMapping("/alicloud_v2/generate_qr")
    public ResponseEntity<Map<String, String>> generateAliCloudV2QR() throws IOException {
        try {
            System.out.println("==========250804 开始生成阿里云盘V2二维码");

            // 1. 获取csrf_token和umid_token
            Map<String, String> tokens = getAliLoginPageTokens();
            System.out.println("==========250804 获取到tokens: " + tokens.toString());

            // 2. 生成二维码 - 先用基本参数测试
            String qrUrl = "https://passport.alipan.com/newlogin/qrcode/generate.do";
            Map<String, String> params = new HashMap<>();
            params.put("appName", "aliyun_drive");
            params.put("fromSite", "52");
            params.put("appEntrance", "web_default");
            params.put("_csrf_token", tokens.get("csrf_token"));
            params.put("umidToken", tokens.get("umid_token"));
            params.put("isMobile", "false");
            params.put("lang", "zh_CN");
            params.put("returnUrl", "");
            params.put("bizParams", "");

            // 先注释掉可能有问题的参数，逐步测试
            // params.put("hsiz", "115d9f5f2cf2f87850a93a793aaaecb4");
            // params.put("bizParams", "taobaoBizLoginFrom=web_default&renderRefer=https%3A%2F%2Fauth.alipan.com%2F");
            // params.put("mainPage", "false");
            // params.put("umidTag", "SERVER");

            System.out.println("==========250804 发送二维码生成请求: " + qrUrl);
            String response = sendGetRequestWithParams(qrUrl, params);
            System.out.println("==========250804 二维码生成响应: " + response);

            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response);

            if (jsonNode.has("content") && jsonNode.get("content").has("data")) {
                JsonNode data = jsonNode.get("content").get("data");

                // 检查是否有错误
                if (data.has("errorCode")) {
                    String errorCode = data.get("errorCode").asText();
                    String titleMsg = data.has("titleMsg") ? data.get("titleMsg").asText() : "未知错误";
                    System.out.println("==========250804 阿里云盘API错误: " + errorCode + " - " + titleMsg);
                    throw new RuntimeException("阿里云盘API错误: " + errorCode + " - " + titleMsg);
                }

                // 检查必要字段是否存在
                if (!data.has("codeContent")) {
                    System.out.println("==========250804 响应中缺少codeContent字段");
                    throw new RuntimeException("二维码生成失败：响应格式不正确");
                }

                String codeContent = data.get("codeContent").asText();
                String ck = data.has("ck") ? data.get("ck").asText() : "";
                String t = data.has("t") ? data.get("t").asText() : "";
                String resultCode = data.has("resultCode") ? data.get("resultCode").asText() : "";

                System.out.println("==========250804 解析二维码数据成功: codeContent=" + codeContent + ", ck=" + ck);

                // 创建会话
                String sessionId = UUID.randomUUID().toString();
                AliCloudV2Session session = new AliCloudV2Session();
                session.setCsrfToken(tokens.get("csrf_token"));
                session.setUmidToken(tokens.get("umid_token"));
                session.setCodeContent(codeContent);
                session.setCk(ck);
                session.setT(t);
                session.setResultCode(resultCode);
                session.setCreatedAt(System.currentTimeMillis());

                aliV2Sessions.put(sessionId, session);
                System.out.println("==========250804 创建会话成功: sessionId=" + sessionId);
                System.out.println("==========250804 会话创建时间: " + session.getCreatedAt());
                System.out.println("==========250804 当前会话总数: " + aliV2Sessions.size());

                // 生成二维码图片
                String base64QRCode = generateQRCodeImage(codeContent);
                System.out.println("==========250804 生成二维码图片成功");

                Map<String, String> result = new HashMap<>();
                result.put("qr_code", base64QRCode);
                result.put("session_id", sessionId);

                return ResponseEntity.ok(result);
            }

            System.out.println("==========250804 二维码生成失败: 响应格式不正确");
            throw new RuntimeException("生成二维码失败");
        } catch (Exception e) {
            System.out.println("==========250804 生成二维码异常: " + e.getMessage());
            e.printStackTrace();
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    // 检查阿里云盘V2扫码状态
    @GetMapping("/alicloud_v2/check_status")
    public ResponseEntity<Map<String, String>> checkAliCloudV2Status(@RequestParam String session_id) throws IOException {
        try {
            System.out.println("==========250804 检查扫码状态: sessionId=" + session_id);
            System.out.println("==========250804 当前时间: " + System.currentTimeMillis());
            System.out.println("==========250804 当前会话总数: " + aliV2Sessions.size());

            AliCloudV2Session session = aliV2Sessions.get(session_id);
            if (session == null) {
                System.out.println("==========250804 会话不存在: sessionId=" + session_id);
                System.out.println("==========250804 现有会话列表: " + aliV2Sessions.keySet());
                Map<String, String> result = new HashMap<>();
                result.put("status", "expired");
                return ResponseEntity.ok(result);
            }

            // 检查会话是否过期（10分钟）
            long currentTime = System.currentTimeMillis();
            long sessionAge = currentTime - session.getCreatedAt();
            System.out.println("==========250804 会话年龄: " + sessionAge + "ms (" + (sessionAge/1000) + "秒)");

            if (sessionAge > 600000) {
                System.out.println("==========250804 会话已过期: sessionId=" + session_id + ", 年龄=" + sessionAge + "ms");
                aliV2Sessions.remove(session_id);
                Map<String, String> result = new HashMap<>();
                result.put("status", "expired");
                return ResponseEntity.ok(result);
            }

            // 检查扫码状态 - 按参考项目逻辑使用POST请求
            String checkUrl = "https://passport.alipan.com/newlogin/qrcode/query.do";
            Map<String, String> params = new HashMap<>();
            params.put("appName", "aliyun_drive");
            params.put("fromSite", "52");
            params.put("ck", session.getCk());

            // 调试信息
            System.out.println("==========250804 会话中的t值: '" + session.getT() + "'");
            System.out.println("==========250804 会话中的ck值: '" + session.getCk() + "'");

            // t参数是必需的，不能为空
            if (session.getT() != null && !session.getT().isEmpty()) {
                params.put("t", session.getT());
                System.out.println("==========250804 添加t参数: " + session.getT());
            } else {
                System.out.println("==========250804 警告: t参数为空或null");
            }

            System.out.println("==========250804 POST参数: " + params.toString());
            System.out.println("==========250804 发送状态检查请求(POST): " + checkUrl);
            // 使用POST请求检查状态，添加必要的请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            headers.put("X-Requested-With", "XMLHttpRequest");
            headers.put("Referer", "https://passport.alipan.com/");
            // 注意：isJson=false，因为阿里云盘API期望form-encoded格式，不是JSON
            String response = sendPostRequest(checkUrl, params, headers, false);
            System.out.println("==========250804 状态检查响应: " + response);

            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response);

            Map<String, String> result = new HashMap<>();

            if (jsonNode.has("content") && jsonNode.get("content").has("data")) {
                JsonNode data = jsonNode.get("content").get("data");
                String qrCodeStatus = data.get("qrCodeStatus").asText();
                System.out.println("==========250804 二维码状态: " + qrCodeStatus);

                // 状态映射 (参考项目逻辑)
                // NEW -> WAITING, SCANED -> SCANED, CONFIRMED -> CONFIRMED, EXPIRED -> EXPIRED

                if ("CONFIRMED".equals(qrCodeStatus)) {
                    // 扫码确认，获取refresh_token
                    String bizExt = data.get("bizExt").asText();
                    System.out.println("==========250804 获取到bizExt: " + bizExt);
                    String refreshToken = getRefreshTokenFromBizExt(bizExt, session);

                    if (refreshToken != null) {
                        System.out.println("==========250804 获取refresh_token成功: " + refreshToken);
                        // 保存refresh_token到会话中
                        session.setRefreshToken(refreshToken);
                        result.put("status", "success");
                        result.put("refresh_token", refreshToken);
                    } else {
                        System.out.println("==========250804 获取refresh_token失败");
                        result.put("status", "error");
                        result.put("message", "获取token失败");
                    }
                } else if ("SCANED".equals(qrCodeStatus)) {
                    result.put("status", "scanned");
                } else if ("EXPIRED".equals(qrCodeStatus)) {
                    aliV2Sessions.remove(session_id);
                    result.put("status", "expired");
                } else if ("NEW".equals(qrCodeStatus)) {
                    // NEW状态对应WAITING
                    result.put("status", "waiting");
                } else {
                    result.put("status", "waiting");
                }
            } else {
                result.put("status", "waiting");
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            System.out.println("==========250804 检查状态异常: " + e.getMessage());
            e.printStackTrace();
            Map<String, String> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    // 获取登录页面的token
    private Map<String, String> getAliLoginPageTokens() throws IOException {
        // 构建完整的登录页面URL，包含必要的参数
        String baseUrl = "https://passport.alipan.com/mini_login.htm";
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?lang=zh_cn");
        urlBuilder.append("&appName=aliyun_drive");
        urlBuilder.append("&appEntrance=web_default");
        urlBuilder.append("&styleType=auto");
        urlBuilder.append("&bizParams=");
        urlBuilder.append("&notLoadSsoView=false");
        urlBuilder.append("&notKeepLogin=false");
        urlBuilder.append("&isMobile=false");
        urlBuilder.append("&ad__pass__q__rememberLogin=true");
        urlBuilder.append("&ad__pass__q__rememberLoginDefaultValue=true");
        urlBuilder.append("&ad__pass__q__forgotPassword=true");
        urlBuilder.append("&ad__pass__q__licenseMargin=true");
        urlBuilder.append("&ad__pass__q__loginType=normal");
        urlBuilder.append("&hidePhoneCode=true");
        urlBuilder.append("&rnd=").append(System.currentTimeMillis());

        String loginPageUrl = urlBuilder.toString();
        System.out.println("==========250804 获取登录页面tokens: " + loginPageUrl);

        String html;
        try {
            html = sendGetRequest(loginPageUrl);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Request interrupted", e);
        }

        System.out.println("==========250804 登录页面HTML长度: " + html.length());
        System.out.println("==========250804 登录页面HTML内容: " + html);
        Map<String, String> tokens = new HashMap<>();

        // 提取csrf_token - 从JavaScript对象中提取
        String csrfToken = null;
        String[] csrfPatterns = {
            "\"_csrf_token\"\\s*:\\s*\"([^\"]+)\"",  // JSON格式
            "_csrf_token[\"']?\\s*[:=]\\s*[\"']([^\"']+)[\"']",  // 原有格式
            "name=[\"']_csrf_token[\"']\\s+value=[\"']([^\"']+)[\"']",
            "<input[^>]*name=[\"']_csrf_token[\"'][^>]*value=[\"']([^\"']+)[\"']"
        };

        for (String patternStr : csrfPatterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(html);
            if (matcher.find()) {
                csrfToken = matcher.group(1);
                System.out.println("==========250804 找到csrf_token: " + csrfToken + " (使用模式: " + patternStr + ")");
                break;
            }
        }

        if (csrfToken != null) {
            tokens.put("csrf_token", csrfToken);
        } else {
            System.out.println("==========250804 未找到csrf_token");
            // 设置默认值避免null
            tokens.put("csrf_token", "");
        }

        // 提取umidToken - 从JavaScript对象中提取
        String umidToken = null;
        String[] umidPatterns = {
            "\"umidToken\"\\s*:\\s*\"([^\"]+)\"",  // JSON格式
            "umidToken[\"']?\\s*[:=]\\s*[\"']([^\"']+)[\"']",  // 原有格式
            "name=[\"']umidToken[\"']\\s+value=[\"']([^\"']+)[\"']",
            "<input[^>]*name=[\"']umidToken[\"'][^>]*value=[\"']([^\"']+)[\"']"
        };

        for (String patternStr : umidPatterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(html);
            if (matcher.find()) {
                umidToken = matcher.group(1);
                System.out.println("==========250804 找到umid_token: " + umidToken + " (使用模式: " + patternStr + ")");
                break;
            }
        }

        if (umidToken != null) {
            tokens.put("umid_token", umidToken);
        } else {
            System.out.println("==========250804 未找到umid_token");
            // 设置默认值避免null
            tokens.put("umid_token", "");
        }

        return tokens;
    }

    // 带参数的GET请求
    private String sendGetRequestWithParams(String url, Map<String, String> params) throws IOException {
        StringBuilder urlBuilder = new StringBuilder(url);
        if (!params.isEmpty()) {
            urlBuilder.append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                // 添加空值检查
                if (key == null || value == null) {
                    System.out.println("==========250804 警告: 参数中有null值 - key: " + key + ", value: " + value);
                    continue; // 跳过null值参数
                }

                urlBuilder.append(URLEncoder.encode(key, "UTF-8"))
                         .append("=")
                         .append(URLEncoder.encode(value, "UTF-8"))
                         .append("&");
            }
            urlBuilder.setLength(urlBuilder.length() - 1); // 移除最后的&
        }

        try {
            return sendGetRequest(urlBuilder.toString());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Request interrupted", e);
        }
    }

    // 生成二维码图片
    private String generateQRCodeImage(String content) throws Exception {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300);
        BufferedImage qrImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(qrImage, "png", baos);
        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }

    // 从bizExt获取refresh_token
    private String getRefreshTokenFromBizExt(String bizExt, AliCloudV2Session session) {
        try {
            System.out.println("==========250804 处理bizExt: " + bizExt);

            // 方法1: bizExt直接包含token信息
            if (bizExt != null && bizExt.length() > 100) {
                // 尝试直接解析为JSON
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode bizData = mapper.readTree(bizExt);

                    if (bizData.has("refresh_token")) {
                        String token = bizData.get("refresh_token").asText();
                        System.out.println("==========250804 从JSON中找到refresh_token: " + token);
                        return token;
                    }

                    if (bizData.has("access_token")) {
                        String token = bizData.get("access_token").asText();
                        System.out.println("==========250804 从JSON中找到access_token: " + token);
                        return token;
                    }
                } catch (Exception ignored) {
                    System.out.println("==========250804 bizExt不是JSON格式，尝试其他方式");
                }

                // 尝试URL解码
                try {
                    String decoded = URLDecoder.decode(bizExt, "UTF-8");
                    System.out.println("==========250804 URL解码后: " + decoded);
                    if (decoded.contains("refresh_token=")) {
                        String[] parts = decoded.split("refresh_token=");
                        if (parts.length > 1) {
                            String token = parts[1].split("&")[0];
                            System.out.println("==========250804 从URL参数中找到refresh_token: " + token);
                            return token;
                        }
                    }
                } catch (Exception ignored) {
                    System.out.println("==========250804 URL解码失败");
                }
            }

            // 方法2: 使用bizExt作为参数调用token获取API
            System.out.println("==========250804 尝试使用bizExt调用API获取token");
            return getTokenWithBizExt(bizExt, session);

        } catch (Exception e) {
            System.out.println("==========250804 解析bizExt失败: " + bizExt + ", 错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    private String getTokenWithBizExt(String bizExt, AliCloudV2Session session) {
        try {
            // 根据阿里云盘的实际API，使用bizExt获取最终token
            String tokenUrl = "https://passport.alipan.com/newlogin/login.do";

            Map<String, String> params = new HashMap<>();
            params.put("appName", "aliyun_drive");
            params.put("fromSite", "52");
            params.put("appEntrance", "web_default");
            params.put("_csrf_token", session.getCsrfToken());
            params.put("umidToken", session.getUmidToken());
            params.put("bizExt", bizExt);
            params.put("codeContent", session.getCodeContent());
            params.put("ck", session.getCk());
            params.put("t", session.getT());
            params.put("resultCode", session.getResultCode());

            System.out.println("==========250804 发送token获取请求: " + tokenUrl);
            String response = sendPostRequest(tokenUrl, params, null, false);
            System.out.println("==========250804 token获取响应: " + response);

            ObjectMapper mapper = new ObjectMapper();
            JsonNode responseData = mapper.readTree(response);

            // 解析响应获取token
            if (responseData.has("content") && responseData.get("content").has("data")) {
                JsonNode data = responseData.get("content").get("data");

                if (data.has("refresh_token")) {
                    String token = data.get("refresh_token").asText();
                    System.out.println("==========250804 从响应中找到refresh_token: " + token);
                    return token;
                }

                if (data.has("token")) {
                    String token = data.get("token").asText();
                    System.out.println("==========250804 从响应中找到token: " + token);
                    return token;
                }

                // 可能在其他字段中
                if (data.has("loginResult")) {
                    JsonNode loginResult = data.get("loginResult");
                    if (loginResult.has("refresh_token")) {
                        String token = loginResult.get("refresh_token").asText();
                        System.out.println("==========250804 从loginResult中找到refresh_token: " + token);
                        return token;
                    }
                }
            }

            System.out.println("==========250804 响应中未找到token");
            return null;
        } catch (Exception e) {
            System.out.println("==========250804 使用bizExt获取token失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    // 获取用户信息接口
    @GetMapping("/alicloud_v2/get_user_info")
    public ResponseEntity<Map<String, Object>> getAliCloudV2UserInfo(@RequestParam String session_id) throws IOException {
        try {
            System.out.println("==========250804 获取用户信息: sessionId=" + session_id);

            AliCloudV2Session session = aliV2Sessions.get(session_id);
            if (session == null) {
                System.out.println("==========250804 会话不存在: sessionId=" + session_id);
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("error", "会话不存在或已过期");
                return ResponseEntity.ok(result);
            }

            // 检查是否有refresh_token（表示已登录成功）
            if (session.getRefreshToken() == null) {
                System.out.println("==========250804 用户尚未登录成功");
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("error", "用户尚未登录成功，请先完成扫码登录");
                return ResponseEntity.ok(result);
            }

            // 模拟用户信息（实际项目中可以调用阿里云盘API获取真实用户信息）
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("user_id", "alicloud_user_" + System.currentTimeMillis());
            userInfo.put("nick_name", "阿里云盘用户");
            userInfo.put("avatar", "");

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("user_info", userInfo);
            result.put("refresh_token", session.getRefreshToken());

            // 清理会话
            aliV2Sessions.remove(session_id);
            System.out.println("==========250804 用户信息获取成功，会话已清理");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            System.out.println("==========250804 获取用户信息异常: " + e.getMessage());
            e.printStackTrace();
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    // 会话数据类
    private static class AliCloudV2Session {
        private String csrfToken;
        private String umidToken;
        private String codeContent;
        private String ck;
        private String t;
        private String resultCode;
        private String refreshToken;
        private long createdAt;

        // getters and setters
        public String getCsrfToken() { return csrfToken; }
        public void setCsrfToken(String csrfToken) { this.csrfToken = csrfToken; }

        public String getUmidToken() { return umidToken; }
        public void setUmidToken(String umidToken) { this.umidToken = umidToken; }

        public String getCodeContent() { return codeContent; }
        public void setCodeContent(String codeContent) { this.codeContent = codeContent; }

        public String getCk() { return ck; }
        public void setCk(String ck) { this.ck = ck; }

        public String getT() { return t; }
        public void setT(String t) { this.t = t; }

        public String getResultCode() { return resultCode; }
        public void setResultCode(String resultCode) { this.resultCode = resultCode; }

        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }

        public long getCreatedAt() { return createdAt; }
        public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }
    }
}
