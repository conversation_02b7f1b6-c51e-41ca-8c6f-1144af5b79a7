package cn.har01d.alist_tvbox.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Map;
import java.util.HashMap;
import java.util.UUID;
import java.util.Random;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Set;
import java.util.LinkedHashSet;
import java.util.Base64;

@Service
public class AlipanTokenService {
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private static final String API_URL = "http://api.extscreen.com/aliyundrive/v3/token";
    private static final String TIMESTAMP_URL = "http://api.extscreen.com/timestamp";
    private static final String TOKEN = "6733b42e28cdba32";

    public AlipanTokenService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    public Map<String, Object> refreshToken(String refreshToken) throws Exception {
        // Get timestamp
        ResponseEntity<Map> timestampResponse = restTemplate.getForEntity(TIMESTAMP_URL, Map.class);
        String timestamp = String.valueOf(((Map)((Map)timestampResponse.getBody()).get("data")).get("timestamp"));

        // Generate unique identifiers
        String uniqueId = UUID.randomUUID().toString().replace("-", "");
        String wifiMac = String.valueOf(100000000000L + new Random().nextInt(899999999));

        // Prepare headers and parameters
        HttpHeaders headers = new HttpHeaders();
        headers.set("token", TOKEN);
        headers.set("User-Agent", "Mozilla/5.0 (Linux; U; Android 9; zh-cn; SM-S908E Build/TP1A.220624.014) AppleWebKit/533.1 (KHTML, like Gecko) Mobile Safari/533.1");
        headers.set("Host", "api.extscreen.com");

        Map<String, String> params = getParams(timestamp, uniqueId, wifiMac);
        params.forEach(headers::set);

        // Prepare request body
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("refresh_token", refreshToken);

        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<Map> response = restTemplate.exchange(API_URL, HttpMethod.POST, requestEntity, Map.class);

        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            Map<String, String> data = (Map<String, String>) response.getBody().get("data");
            String ciphertext = data.get("ciphertext");
            String iv = data.get("iv");

            String decryptedData = decrypt(ciphertext, iv, timestamp, uniqueId, wifiMac);
            Map<String, Object> decryptedMap = objectMapper.readValue(decryptedData, Map.class);
            
            // 构造标准的响应格式
            Map<String, Object> standardResponse = new HashMap<>();
            standardResponse.put("code", "");
            standardResponse.put("message", "");
            standardResponse.put("token_type", "Bearer");
            standardResponse.put("access_token", decryptedMap.get("access_token"));
            standardResponse.put("refresh_token", decryptedMap.get("refresh_token"));
            standardResponse.put("expires_in", 7200);
            
            return standardResponse;
        }

        throw new RuntimeException("Failed to refresh token");
    }

    private Map<String, String> getParams(String timestamp, String uniqueId, String wifiMac) {
        Map<String, String> params = new HashMap<>();
        params.put("akv", "2.8.1496");
        params.put("apv", "1.3.8");
        params.put("b", "samsung");
        params.put("d", uniqueId);
        params.put("m", "SM-S908E");
        params.put("mac", "");
        params.put("n", "SM-S908E");
        params.put("t", timestamp);
        params.put("wifiMac", wifiMac);
        return params;
    }

    private String generateKey(String timestamp, String uniqueId, String wifiMac) throws Exception {
        Map<String, String> params = getParams(timestamp, uniqueId, wifiMac);
        List<String> sortedKeys = new ArrayList<>(params.keySet());
        Collections.sort(sortedKeys);

        StringBuilder concatenatedParams = new StringBuilder();
        for (String key : sortedKeys) {
            if (!key.equals("t")) {
                concatenatedParams.append(params.get(key));
            }
        }

        String hashedKey = h(concatenatedParams.toString(), timestamp);
        return getMD5(hashedKey);
    }

    private String h(String input, String timestamp) {
        char[] chars = input.toCharArray();
        Set<Character> uniqueChars = new LinkedHashSet<>();
        for (char c : chars) {
            uniqueChars.add(c);
        }

        int numericModifier = Integer.parseInt(timestamp.substring(7));
        StringBuilder result = new StringBuilder();
        
        for (char c : uniqueChars) {
            int transformed = Math.abs(c - (numericModifier % 127) - 1);
            if (transformed < 33) {
                transformed += 33;
            }
            result.append((char) transformed);
        }
        
        return result.toString();
    }

    private String getMD5(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder();
        for (byte b : messageDigest) {
            String hex = Integer.toHexString(0xFF & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    private String decrypt(String ciphertext, String iv, String timestamp, String uniqueId, String wifiMac) throws Exception {
        String key = generateKey(timestamp, uniqueId, wifiMac);
        
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        byte[] ivBytes = hexStringToByteArray(iv);
        
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
        
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(ciphertext));
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    private byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }
}
