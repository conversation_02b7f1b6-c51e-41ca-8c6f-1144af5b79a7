package cn.har01d.alist_tvbox.service;

import cn.har01d.alist_tvbox.dto.FileDto;
import cn.har01d.alist_tvbox.dto.FileItem;
import cn.har01d.alist_tvbox.entity.ConfigFile;
import cn.har01d.alist_tvbox.entity.ConfigFileRepository;
import cn.har01d.alist_tvbox.exception.BadRequestException;
import cn.har01d.alist_tvbox.exception.NotFoundException;
import com.fasterxml.jackson.databind.ObjectMapper; // ===1021 sync 466
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service

public class ConfigFileService {
    private final ConfigFileRepository repository;
    private final ObjectMapper objectMapper; // ===1021 sync 466
    private List<FileItem> labels = new ArrayList<>();

    // public ConfigFileService(ConfigFileRepository repository) {
    public ConfigFileService(ConfigFileRepository repository, ObjectMapper objectMapper) { // ===1021 sync 466
        this.repository = repository;
        this.objectMapper = objectMapper; // ===1021 sync 466
    }

    @PostConstruct
    public void setup() {
        if (repository.count() == 0) {
            readFiles();
        }
        loadLabels();
    }

    public List<FileItem> getLabels() {
        return labels;
    }

    private void loadLabels() {
        try {
            Path path = Path.of("/data/label.txt");
            if (Files.exists(path)) {
                loadLabels(Files.readAllLines(path));
            }
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    private void loadLabels(List<String> lines) {
        try {
            labels = lines.stream()
                    .map(e -> e.split("#")[0])
                    .filter(StringUtils::isNotBlank)
                    .map(e -> e.split(":"))
                    .filter(e -> e.length == 2)
                    .map(parts -> new FileItem(parts[0].trim(), parts[1].trim(), 0))
                    .toList();
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    public void writeFiles() {
        for (ConfigFile file : repository.findAll()) {
            try {
                writeFileContent(file);
            } catch (Exception e) {
                log.warn("write file " + file.getPath(), e);
            }
        }
    }

    private void readFiles() {
        readFile("/data/tv.txt");
        readFile("/data/proxy.txt");

        if (Files.exists(Paths.get("/data/iptv.m3u"))) {
            readFile("/www/tvbox/iptv.m3u");
        }

        if (Files.exists(Paths.get("/data/my.json"))) {
            readFile("/www/tvbox/my.json");
        }

        //readFile("/opt/alist/data/config.json");
        //readFile("/etc/nginx/http.d/default.conf");
    }

    private void readFile(String filepath) {
        try {
            Path path = Paths.get(filepath);
            if (Files.exists(path)) {
                String content = Files.readString(path);
                ConfigFile file = new ConfigFile();
                file.setDir(path.getParent().toString());
                file.setName(path.getFileName().toString());
                file.setPath(filepath);
                file.setContent(content);
                repository.save(file);
                log.info("load file: {}", path);
            }
        } catch (Exception e) {
            log.warn("read file " + filepath, e);
        }
    }
    public List<ConfigFile> list() {
        return repository.findAll();
    }

    public ConfigFile create(FileDto dto) throws IOException {
        validate(dto);
        dto.setId(null);
        // dto.setPath(new File(dto.getDir(), dto.getName()).getAbsolutePath());
        if (repository.existsByPath(dto.getPath())) {
            throw new BadRequestException("文件已经存在");
        }

        ConfigFile file = new ConfigFile(dto);
        repository.save(file);
        writeFileContent(file);
        return file;
    }

    private void validate(FileDto dto) {
        if (StringUtils.isBlank(dto.getDir())) {
            throw new BadRequestException("目录不能为空");
        }
        if (StringUtils.isBlank(dto.getName())) {
            throw new BadRequestException("文件名不能为空");
        }
        dto.setPath(new File(dto.getDir(), dto.getName()).getAbsolutePath()); //===1021 sync 466
        if (dto.getName().endsWith(".json")) {
            if (StringUtils.isNotBlank(dto.getContent())) {
                try {
                    var node = objectMapper.readTree(dto.getContent());
                    dto.setContent(objectMapper.writeValueAsString(node));
                } catch (IOException e) {
                    throw new BadRequestException("JSON格式错误", e);
                }
            } else if ("null".equals(dto.getContent())) {
                dto.setContent("");
            }
        }
    }

    // private void writeFileContent(ConfigFile configFile) throws IOException { //===1021 sync 508
    public void writeFileContent(ConfigFile configFile) throws IOException {
        log.info("write file: {}", configFile.getPath());
        Files.createDirectories(Paths.get(configFile.getDir()));
        Path path = Paths.get(configFile.getDir(), configFile.getName());
        Files.writeString(path, configFile.getContent());
        if (path.toString().equals("/data/label.txt")) {
            loadLabels(Arrays.asList(configFile.getContent().split("\n")));
        }
        
        // 检查是否是diy_mount.txt文件，如果是则执行挂载处理脚本
        if (path.toString().equals("/data/diy_mount.txt")) {
            processDiyMount();
        }
    }

    private void processDiyMount() {
        try {
            // 检查Docker socket是否存在
            File dockerSocket = new File("/var/run/docker.sock");
            if (!dockerSocket.exists()) {
                log.warn("Docker socket不存在，无法处理自定义挂载配置");
                return;
            }
            
            log.info("检测到diy_mount.txt文件变更，开始处理自定义挂载配置");
            ProcessBuilder builder = new ProcessBuilder();
            builder.command("bash", "-c", "/check_diy_mount.sh");
            builder.inheritIO();
            Process process = builder.start();
            
            // 异步等待进程完成
            CompletableFuture.runAsync(() -> {
                try {
                    int code = process.waitFor();
                    if (code == 0) {
                        log.info("自定义挂载配置处理完成");
                    } else {
                        log.warn("自定义挂载配置处理失败: {}", code);
                    }
                } catch (InterruptedException e) {
                    log.error("自定义挂载配置处理被中断", e);
                    Thread.currentThread().interrupt();
                }
            });
        } catch (Exception e) {
            log.error("执行自定义挂载配置处理时出错", e);
        }
    }

    public ConfigFile update(Integer id, FileDto dto) throws IOException {
        validate(dto);
        ConfigFile configFile = repository.findById(id).orElseThrow(NotFoundException::new);
        try {
            Path path = Paths.get(configFile.getDir(), configFile.getName());
            Files.delete(path);
        } catch (Exception e) {
            log.warn("", e);
        }

        dto.setId(id);
        // dto.setPath(new File(dto.getDir(), dto.getName()).getAbsolutePath()); //===1021 sync 466

        ConfigFile other = repository.findByPath(dto.getPath());
        if (other != null && !id.equals(other.getId())) {
            throw new BadRequestException("文件已经存在");
        }

        ConfigFile file = new ConfigFile(dto);
        repository.save(file);
        writeFileContent(file);
        return file;
    }

    public void delete(Integer id) throws IOException {
        ConfigFile configFile = repository.findById(id).orElse(null);
        if (configFile == null) {
            return;
        }

        repository.deleteById(id);
        Path path = Paths.get(configFile.getDir(), configFile.getName());
        Files.delete(path);
    }
}
