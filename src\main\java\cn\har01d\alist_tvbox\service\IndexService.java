package cn.har01d.alist_tvbox.service;

import cn.har01d.alist_tvbox.config.AppProperties;
import cn.har01d.alist_tvbox.domain.TaskResult;
import cn.har01d.alist_tvbox.domain.TaskStatus;
import cn.har01d.alist_tvbox.dto.AliFileList;
import cn.har01d.alist_tvbox.dto.FileItem;
import cn.har01d.alist_tvbox.dto.IndexRequest;
import cn.har01d.alist_tvbox.dto.IndexResponse;
import cn.har01d.alist_tvbox.entity.IndexTemplate;
import cn.har01d.alist_tvbox.entity.IndexTemplateRepository;
import cn.har01d.alist_tvbox.entity.Meta;
import cn.har01d.alist_tvbox.entity.MetaRepository;
import cn.har01d.alist_tvbox.entity.Setting;
import cn.har01d.alist_tvbox.entity.SettingRepository;
import cn.har01d.alist_tvbox.entity.Site;
import cn.har01d.alist_tvbox.entity.Task;
import cn.har01d.alist_tvbox.exception.BadRequestException;
import cn.har01d.alist_tvbox.model.FsDetail;
import cn.har01d.alist_tvbox.model.FsInfo;
import cn.har01d.alist_tvbox.model.FsResponse;
import cn.har01d.alist_tvbox.model.ShareInfo;
import cn.har01d.alist_tvbox.tvbox.IndexContext;
import cn.har01d.alist_tvbox.util.Constants;
import cn.har01d.alist_tvbox.util.TextUtils;
import cn.har01d.alist_tvbox.util.Utils;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
// import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.bind.annotation.PostMapping;
    
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import static cn.har01d.alist_tvbox.util.Constants.APP_VERSION;
import static cn.har01d.alist_tvbox.util.Constants.DOCKER_VERSION;
import static cn.har01d.alist_tvbox.util.Constants.INDEX_VERSION;
import static cn.har01d.alist_tvbox.util.Constants.USER_AGENT;

@Slf4j
@Service
public class IndexService {
    private static final Pattern SEASON1 = Pattern.compile("Season ?\\d{1,2}.*");
    private static final Pattern SEASON2 = Pattern.compile("SE\\d{1,2}.*");
    private static final Pattern SEASON3 = Pattern.compile("^[Ss](\\d{1,2})$");
    private static final Pattern SEASON4 = Pattern.compile("第.{1,3}季.*");
    private static final Pattern EPISODE = Pattern.compile("S\\d+E\\d+");
    private static final Pattern EPISODE1 = Pattern.compile("全\\d+集");

    private final AListService aListService;
    private final SiteService siteService;
    private final TaskService taskService;
    private final AListLocalService aListLocalService;
    private final TmdbService tmdbService;
    private final AppProperties appProperties;
    private final SettingRepository settingRepository;
    private final IndexTemplateRepository indexTemplateRepository;
    private final MetaRepository metaRepository;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final Environment environment;
    private final ExecutorService executor = Executors.newSingleThreadExecutor();

    // 定义为常量
    private static final String BASE_URL = "http://127.0.0.1:5234/d/AI老G常用分享/";
    
    private static final String[] FILES_TO_DOWNLOAD = {
        "小雅A-list启动4文件/version.txt",
        "小雅A-list启动4文件/index.zip",
        "小雅A-list启动4文件/update.zip",
        "小雅A-list启动4文件/tvbox.zip",
        "小雅A-list启动4文件/115share_list.txt"
    };

    private static final String DATA_DIR = "/data";
    
    /**
     * 日志条目，包含消息内容和时间戳
     */
    private static class LogEntry {
        private final String message;
        private final String timestamp;
        
        public LogEntry(String message) {
            this.message = message;
            this.timestamp = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(java.time.LocalDateTime.now());
        }
        
        @Override
        public String toString() {
            return timestamp + "  " + message;
        }
    }
    
    // 用于收集日志摘要的列表
    private List<LogEntry> updateSummaryLogs;
    
    /**
     * 添加一条更新摘要日志
     */
    private void addUpdateSummary(String message) {
        if (updateSummaryLogs != null && message.contains("==========")) {
            // 移除前缀"=========="，只保留实际消息内容
            String cleanMessage = message.replace("==========", "").trim();
            updateSummaryLogs.add(new LogEntry(cleanMessage));
        }
    }
    
    /**
     * 输出更新摘要日志
     */
    private void outputSummary() {
        if (updateSummaryLogs != null && !updateSummaryLogs.isEmpty()) {
            // 构建完整的摘要内容
            StringBuilder content = new StringBuilder();
            
            // 添加标题行，确保它单独成行
            content.append("==========小雅启动文件更新摘要==========\n");
            
            // 添加每条日志条目
            for (LogEntry entry : updateSummaryLogs) {
                content.append(entry.toString()).append("\n");
            }
            
            // 添加结束时间戳
            String timestamp = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(java.time.LocalDateTime.now());
            content.append("==========本次更新：").append(timestamp).append("==========");
            
            // 使用log.info输出完整内容
            log.info("\n{}", content.toString());
            
            // 清空列表，避免内存泄漏
            updateSummaryLogs.clear();
        }
    }

    public IndexService(AListService aListService,
                        SiteService siteService,
                        TaskService taskService,
                        AListLocalService aListLocalService,
                        TmdbService tmdbService,
                        AppProperties appProperties,
                        SettingRepository settingRepository,
                        IndexTemplateRepository indexTemplateRepository,
                        MetaRepository metaRepository,
                        RestTemplateBuilder builder,
                        ObjectMapper objectMapper,
                        Environment environment) {
        this.aListService = aListService;
        this.siteService = siteService;
        this.taskService = taskService;
        this.aListLocalService = aListLocalService;
        this.tmdbService = tmdbService;
        this.appProperties = appProperties;
        this.settingRepository = settingRepository;
        this.indexTemplateRepository = indexTemplateRepository;
        this.metaRepository = metaRepository;
        this.restTemplate = builder
                .defaultHeader(HttpHeaders.ACCEPT, Constants.ACCEPT)
                .defaultHeader(HttpHeaders.USER_AGENT, Constants.USER_AGENT1)
                .build();
        this.objectMapper = objectMapper;
        this.environment = environment;
        updateIndexFile();
    }

    @PostConstruct
    public void setup() {
        try {
            Path path = Paths.get("/data/index/version.txt");
            if (Files.exists(path)) {
                List<String> lines = Files.readAllLines(path);
                if (!lines.isEmpty()) {
                    settingRepository.save(new Setting(INDEX_VERSION, lines.get(0).trim()));
                }
            }
        } catch (Exception e) {
            log.warn("", e);
        }

        try {
            Path path = Paths.get("/docker.version");
            if (Files.exists(path)) {
                List<String> lines = Files.readAllLines(path);
                if (!lines.isEmpty()) {
                    settingRepository.save(new Setting(DOCKER_VERSION, lines.get(0).trim()));
                }
            }
        } catch (Exception e) {
            log.warn("", e);
        }

        try {
            Path path = Paths.get("data/app_version");
            if (Files.exists(path)) {
                List<String> lines = Files.readAllLines(path);
                if (!lines.isEmpty()) {
                    settingRepository.save(new Setting(APP_VERSION, lines.get(0).trim()));
                }
            }
        } catch (Exception e) {
            log.warn("", e);
        }

        // 同步自动更新设置到 AList 数据库
        try {
            settingRepository.findById("auto_quark_update")
                .ifPresentOrElse(
                    setting -> syncSettingToAList("auto_quark_update", setting.getValue()),
                    () -> {
                        // 如果设置不存在，创建默认值
                        Setting setting = new Setting("auto_quark_update", "false");
                        settingRepository.save(setting);
                        syncSettingToAList("auto_quark_update", "false");
                    }
                );

            // 同步时间范围设置
            settingRepository.findById("quark_update_start_time")
                .ifPresentOrElse(
                    setting -> syncSettingToAList("quark_update_start_time", setting.getValue()),
                    () -> {
                        Setting setting = new Setting("quark_update_start_time", "00:00");
                        settingRepository.save(setting);
                        syncSettingToAList("quark_update_start_time", "00:00");
                    }
                );

            settingRepository.findById("quark_update_end_time")
                .ifPresentOrElse(
                    setting -> syncSettingToAList("quark_update_end_time", setting.getValue()),
                    () -> {
                        Setting setting = new Setting("quark_update_end_time", "23:59");
                        settingRepository.save(setting);
                        syncSettingToAList("quark_update_end_time", "23:59");
                    }
                );
        } catch (Exception e) {
            log.warn("Failed to sync settings to AList DB", e);
        }
    }

    private void syncSettingToAList(String key, String value) {
        try {
            String type = "auto_quark_update".equals(key) ? "bool" : "text";
            String sql = String.format(
                "INSERT OR REPLACE INTO x_setting_items (key, value, type, flag) VALUES ('%s', '%s', '%s', 1)",
                key, value, type
            );
            Utils.executeUpdate(sql);
            log.info("Synced setting to AList DB: {} = {}", key, value);
        } catch (Exception e) {
            log.error("Failed to sync setting to AList DB: {} = {}", key, value, e);
        }
    }

    @Scheduled(cron = "0 0 22 * * ?")
    public void update() {
        getRemoteVersion();
    }

    public String getRemoteVersion() {
        if (!environment.matchesProfiles("xiaoya")) {
            return "";
        }

        try {
            String remote = getVersion();
            String local = settingRepository.findById(INDEX_VERSION).map(Setting::getValue).orElse("").trim();
            log.debug("xiaoya index file local: {} remote: {}", local, remote);
            if (remote != null && !local.equals(remote)) {
                executor.execute(() -> updateXiaoyaIndexFile(remote));
            }
            return remote == null ? "" : remote;
        } catch (Exception e) {
            log.debug("", e);
        }
        return "";
    }

    private String getVersion() {
        String remote;
        try {
            remote = restTemplate.getForObject("http://docker.xiaoya.pro/version.txt", String.class);
        } catch (ResourceAccessException e) {
            remote = restTemplate.getForObject("http://har01d.org/version.txt", String.class);
        }
        return Utils.trim(remote);
    }

    public void updateXiaoyaIndexFile(String remote) {
        try {
            log.info("download xiaoya index file");
            ProcessBuilder builder = new ProcessBuilder();
            builder.command("bash", "-c", "/index.sh", remote);
            builder.inheritIO();
            builder.directory(new File("/tmp"));
            Process process = builder.start();
            int code = process.waitFor();
            if (code == 0) {
                log.info("xiaoya index file updated");
                settingRepository.save(new Setting(INDEX_VERSION, remote));                
            } else {
                log.warn("download xiaoya index file failed: {}", code);
            }
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    public void updateIndexFile() {
        for (Site site : siteService.list()) {
            if (site.isSearchable() && StringUtils.isNotBlank(site.getIndexFile())) {
                try {
                    downloadIndexFile(site, true);
                } catch (Exception e) {
                    log.warn("", e);
                }
            }
        }
    }

    public void updateIndexFile(Integer siteId) throws IOException {
        Site site = siteService.getById(siteId);
        downloadIndexFile(site, true);
    }

    public String downloadIndexFile(Site site) throws IOException {
        return downloadIndexFile(site, false);
    }

    public String downloadIndexFile(Site site, boolean update) throws IOException {
        String url = site.getIndexFile();
        if (!url.startsWith("http")) {
            return url;
        }

        String name = getIndexFileName(url);
        String filename = name;
        if (name.endsWith(".zip")) {
            filename = name.substring(0, name.length() - 4) + ".txt";
        }

        File file = new File(".cache/" + site.getId() + "/" + filename);
        if (!update && file.exists()) {
            return file.getAbsolutePath();
        }

        if (name.endsWith(".zip")) {
            if (unchanged(site, url, name)) {
                return file.getAbsolutePath();
            }

            log.info("download index file from {}", url);
            downloadZipFile(site, url, name);
        } else {
            log.info("download index file from {}", url);
            FileUtils.copyURLToFile(new URL(url), file);
        }

        return file.getAbsolutePath();
    }

    private static boolean unchanged(Site site, String url, String name) {
        String localTime = getLocalTime(site, name.substring(0, name.length() - 4) + ".info");
        String infoUrl = url.substring(0, url.length() - 4) + ".info";
        String remoteTime = getRemoteTime(site, infoUrl);
        return localTime.equals(remoteTime);
    }

    private static String getLocalTime(Site site, String filename) {
        File info = new File(".cache/" + site.getId() + "/" + filename);
        if (info.exists()) {
            try {
                return FileUtils.readFileToString(info, StandardCharsets.UTF_8);
            } catch (Exception e) {
                // ignore
            }
        }
        return Instant.now().toString();
    }

    private static String getRemoteTime(Site site, String url) {
        try {
            File file = Files.createTempFile(String.valueOf(site.getId()), ".info").toFile();
            FileUtils.copyURLToFile(new URL(url), file);
            return FileUtils.readFileToString(file, StandardCharsets.UTF_8);
        } catch (Exception e) {
            // ignore
        }
        return "";
    }

    private static void downloadZipFile(Site site, String url, String name) throws IOException {
        File zipFile = new File(".cache/" + site.getId() + "/" + name);
        FileUtils.copyURLToFile(new URL(url), zipFile);
        unzip(zipFile);
        Files.delete(zipFile.toPath());
    }

    public static void unzip(File file) throws IOException {
        Path destFolderPath = Paths.get(file.getParent());

        try (ZipFile zipFile = new ZipFile(file, ZipFile.OPEN_READ, StandardCharsets.UTF_8)) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                Path entryPath = destFolderPath.resolve(entry.getName());
                if (entryPath.normalize().startsWith(destFolderPath.normalize())) {
                    if (entry.isDirectory()) {
                        Files.createDirectories(entryPath);
                    } else {
                        Files.createDirectories(entryPath.getParent());
                        try (InputStream in = zipFile.getInputStream(entry);
                             OutputStream out = Files.newOutputStream(entryPath.toFile().toPath())) {
                            IOUtils.copy(in, out);
                        }
                    }
                }
            }
        }
    }

    private String getIndexFileName(String url) {
        int index = url.lastIndexOf('/');
        String name = "index.txt";
        if (index > -1) {
            name = url.substring(index + 1);
        }
        if (name.isEmpty()) {
            return "index.txt";
        }
        return name;
    }

    @Scheduled(cron = "0 0 10,12,14,16,18-23 * * ?")
    public void autoIndex() {
        if (aListLocalService.getAListStatus() != 2) {
            return;
        }
        String hour = String.valueOf(LocalTime.now().getHour());
        List<IndexTemplate> list = indexTemplateRepository.findByScheduledTrue();
        log.debug("auto index: {}", list.size());
        for (IndexTemplate template : list) {
            if (template.getScheduleTime() != null && template.getScheduleTime().contains(hour)) {
                try {
                    log.info("auto index for template: {}", template.getId());
                    IndexRequest indexRequest = objectMapper.readValue(template.getData(), IndexRequest.class);
                    indexRequest.setScrape(template.isScrape());
                    index(indexRequest);
                } catch (Exception e) {
                    log.error("start index failed: {}", template.getId(), e);
                }
            }
        }
    }

    public IndexResponse index(IndexRequest indexRequest) {
        indexRequest.setPaths(indexRequest.getPaths().stream().filter(StringUtils::isNotBlank).toList());
        if (indexRequest.getPaths().isEmpty()) {
            throw new BadRequestException("路径不能为空");
        }
        cn.har01d.alist_tvbox.entity.Site site = siteService.getById(indexRequest.getSiteId());
        Task task = taskService.addIndexTask(site, indexRequest.getIndexName());

        executor.submit(() -> {
            try {
                index(indexRequest, site, task);
            } catch (Exception e) {
                log.warn("index failed", e);
                taskService.failTask(task.getId(), e.getMessage());
            }
        });

        return new IndexResponse(task.getId());
    }

    @Async
    public void index(IndexRequest indexRequest, cn.har01d.alist_tvbox.entity.Site site, Task task) throws IOException {
        StopWatch stopWatch = new StopWatch("index");
        File dir = new File("/data/index/" + indexRequest.getSiteId());
        Files.createDirectories(dir.toPath());
        File file = new File(dir, indexRequest.getIndexName() + ".txt");
        File info = new File(dir, indexRequest.getIndexName() + ".info");

        indexRequest.setPaths(indexRequest.getPaths().stream().filter(e -> !e.isBlank()).toList());
        List<String> paths = indexRequest.getPaths().stream().map(e -> e.split(":")[0]).collect(Collectors.toList());
        List<String> excludes = paths.stream().filter(e -> e.startsWith("-")).map(e -> e.substring(1)).toList();
        List<String> reset = paths.stream().filter(e -> e.startsWith(">")).map(e -> e.substring(1)).toList();
        paths.removeIf(e -> excludes.contains("-" + e));
        paths.removeIf(e -> reset.contains(">" + e));
        if (indexRequest.isIncremental()) {
            removeLines(file, paths, reset);
        }

        String summary;
        try (FileWriter writer = new FileWriter(file, indexRequest.isIncremental());
             FileWriter writer2 = new FileWriter(info)) {
            Instant time = Instant.now();
            taskService.startTask(task.getId());
            String detail = getTaskDetails(paths) + "\n\n索引文件:\n" + file.getAbsolutePath();
            taskService.updateTaskData(task.getId(), detail);
            IndexContext context = new IndexContext(indexRequest, site, writer, task.getId());
            context.getExcludes().addAll(excludes);
            context.getExcludes().addAll(loadExcluded(file));
            int total = 0;
            for (String path : indexRequest.getPaths()) {
                if (isCancelled(context)) {
                    break;
                }
                if (path.startsWith(">") || path.startsWith("-")) {
                    continue;
                }
                context.getTime().clear();
                path = customize(context, indexRequest, path);
                stopWatch.start("index " + path);
                var shareInfo = aListService.getShareInfo(site, path);
                if (shareInfo != null) {
                    index(context, shareInfo, shareInfo.getFileId(), path, 0);
                } else {
                    index(context, path, 0);
                }
                handleUpdateTime(path, context.getTime());
                stopWatch.stop();
                log.info("{} {}", path, context.stats.indexed - total);
                total = context.stats.indexed;
            }
            writer2.write(time.toString());
            log.info("index stats: {}", context.stats);
            summary = context.stats.toString();
        }

        if (indexRequest.isCompress()) {
            File zipFIle = new File(dir, indexRequest.getIndexName() + ".zip");
            zipFile(file, info, zipFIle);
        }
        taskService.completeTask(task.getId());
        taskService.updateTaskSummary(task.getId(), summary);

        if (indexRequest.isScrape()) {
            tmdbService.scrape(site.getId(), indexRequest.getIndexName(), false);
        }

        log.info("index done, total time : {} {}", Duration.ofNanos(stopWatch.getTotalTimeNanos()), stopWatch.prettyPrint());
        log.info("index file: {}", file.getAbsolutePath());
    }

    private void handleUpdateTime(String path, Map<String, String> times) {
        log.debug("handle update time for {}", path);
        try {
            var list = metaRepository.findByPathStartsWith(path, PageRequest.of(0, 1000)).getContent();
            List<Meta> updated = new ArrayList<>();
            for (var meta : list) {
                String text = times.get(meta.getPath());
                if (text != null) {
                    Instant time = Instant.parse(text);
                    log.debug("{} {} {}", meta.getPath(), meta.getTime(), time);
                    if (time != null && (meta.getTime() == null || time.isAfter(meta.getTime()))) {
                        meta.setTime(time);
                        updated.add(meta);
                    }
                }
            }

            if (!updated.isEmpty()) {
                log.info("update time for {} path {}", updated.size(), path);
                metaRepository.saveAll(updated);
            }
        } catch (Exception e) {
            log.warn("handleUpdateTime error", e);
        }
    }

    private static String customize(IndexContext context, IndexRequest indexRequest, String path) {
        String[] parts = path.split(":");
        path = parts[0];
        boolean includeFiles = parts.length > 1 && "file".equals(parts[1]);
        context.setIncludeFiles(indexRequest.isIncludeFiles() || includeFiles);
        int maxDepth = parts.length > 2 ? Integer.parseInt(parts[2]) : indexRequest.getMaxDepth();
        context.setMaxDepth(maxDepth);
        return path;
    }

    private String getTaskDetails(List<String> paths) {
        int n = paths.size();
        if (n < 7) {
            return "索引路径:\n" + String.join("\n", paths);
        }
        return "索引路径:\n" + String.join("\n", paths.subList(0, 3)) +
                "\n...\n" + String.join("\n", paths.subList(n - 3, n));
    }

    private boolean isCancelled(IndexContext context) {
        Task task = taskService.getById(context.getTaskId());
        return task.getStatus() == TaskStatus.COMPLETED && task.getResult() == TaskResult.CANCELLED;
    }

    private void removeLines(File file, List<String> prefix, List<String> reset) {
        if (!file.exists()) {
            return;
        }

        prefix.addAll(reset);
        try {
            List<String> lines = Files.readAllLines(file.toPath())
                    .stream()
                    .filter(path -> prefix.stream().noneMatch(path::startsWith))
                    .toList();

            try (FileWriter writer = new FileWriter(file)) {
                IOUtils.writeLines(lines, null, writer);
            }
        } catch (Exception e) {
            log.warn("", e);
        }
    }

    private List<String> loadExcluded(File file) throws IOException {
        return Files.readAllLines(file.toPath())
                .stream()
                .filter(path -> path.startsWith("-"))
                .map(path -> "^" + path.substring(1) + "$")
                .toList();
    }

    private void zipFile(File file, File info, File output) throws IOException {
        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(output.toPath()))) {
            addZipEntry(zipOut, file);
            addZipEntry(zipOut, info);
        }
    }

    private void addZipEntry(ZipOutputStream zipOut, File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            ZipEntry zipEntry = new ZipEntry(file.getName());
            zipOut.putNextEntry(zipEntry);

            byte[] bytes = new byte[4096];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zipOut.write(bytes, 0, length);
            }
        }
    }

    private AliFileList listFiles(IndexContext context, ShareInfo shareInfo, String parentId, String path, String marker) {
        Exception exception = null;
        for (int i = 0; i < 20; i++) {
            String deviceID = UUID.randomUUID().toString().replace("-", "");
            HttpHeaders headers = new HttpHeaders();
            headers.put("X-Canary", List.of("client=web,app=share,version=v2.3.1"));
            headers.put("X-Device-Id", List.of(deviceID));
            headers.put("X-Share-Token", List.of(shareInfo.getShareToken()));
            headers.put("Referer", List.of("https://www.alipan.com/"));
            headers.put("User-Agent", List.of(USER_AGENT));
            Map<String, Object> body = new HashMap<>();
            body.put("share_id", shareInfo.getShareId());
            body.put("limit", 200);
            body.put("order_by", "name");
            body.put("order_direction", "ASC");
            body.put("parent_file_id", parentId);
            body.put("marker", marker);
            HttpEntity<Object> entity = new HttpEntity<>(body, headers);

            try {
                ResponseEntity<AliFileList> response = restTemplate.exchange("https://api.aliyundrive.com/adrive/v2/file/list_by_share", HttpMethod.POST, entity, AliFileList.class);
                log.debug("listFiles {} {}", path, response.getBody());
                return response.getBody();
            } catch (HttpClientErrorException.TooManyRequests e) {
                exception = e;
                log.warn("Too many requests: {} {}", i + 1, path);
            } catch (HttpClientErrorException.Unauthorized e) {
                if (e.getMessage().contains("ShareLinkToken is invalid")) {
                    shareInfo.setShareToken(aListService.getShareInfo(context.getSite(), path).getShareToken());
                } else {
                    throw e;
                }
            }

            try {
                Thread.sleep(context.getIndexRequest().getSleep());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        log.error("list files failed {}", path, exception);
        return null;
    }

    private void index(IndexContext context, ShareInfo shareInfo, String parentId, String path, int depth) throws IOException {
        log.debug("path: {}  depth: {}  context: {}", path, depth, context);
        if ((context.getMaxDepth() > 0 && depth == context.getMaxDepth()) || isCancelled(context)) {
            log.debug("exit {}", depth);
            return;
        }

        if (!log.isDebugEnabled()) {
            log.info("index {} : {}", context.getSiteName(), path);
        }

        List<String> files = new ArrayList<>();
        List<String> folders = new ArrayList<>();
        boolean hasFile = false;
        String marker = "";
        do {
            var fsResponse = listFiles(context, shareInfo, parentId, path, marker);
            if (fsResponse == null) {
                log.warn("response null: {} {}", path, context.stats);
                context.stats.errors++;
                Task task = taskService.getById(context.getTaskId());
                String data = task.getData();
                if (!data.contains("失效路径：")) {
                    data += "\n\n失效路径：\n";
                }
                data += path + "\n";
                taskService.updateTaskData(task.getId(), data);
                return;
            }

            for (var fsInfo : fsResponse.getItems()) {
                try {
                    if ("folder".equals(fsInfo.getType())) { // folder
                        String newPath = fixPath(path + "/" + fsInfo.getName());
                        log.debug("new path: {}", newPath);
                        if (exclude(context.getExcludes(), newPath)) {
                            log.warn("exclude folder {}", newPath);
                            context.stats.excluded++;
                            continue;
                        }

                        context.getTime().put(newPath, fsInfo.getUpdatedAt());
                        if (context.getMaxDepth() == depth + 1 && !context.isIncludeFiles()) {
                            folders.add(fsInfo.getName());
                        } else {
                            if (isSeason(fsInfo.getName()) || isSpecial(fsInfo.getName())) {
                                hasFile = true;
                                continue;
                            }

//                            if (context.getIndexRequest().getSleep() > 0) {
//                                log.debug("sleep {}", context.getIndexRequest().getSleep());
//                                Thread.sleep(context.getIndexRequest().getSleep());
//                            }
                            Thread.sleep(1000);

                            if (isCancelled(context)) {
                                break;
                            }

                            try {
                                index(context, shareInfo, fsInfo.getFileId(), newPath, depth + 1);
                            } catch (Exception e) {
                                context.stats.errors++;
                                log.warn("index failed: {}", newPath, e);
                            }
                        }
                    } else if (isMediaFormat(fsInfo.getName())) { // file
                        hasFile = true;
                        if (context.isIncludeFiles() || isMovie(path)) {
                            String newPath = fixPath(path + "/" + fsInfo.getName());
                            if (exclude(context.getExcludes(), newPath)) {
                                log.warn("exclude file {}", newPath);
                                context.stats.excluded++;
                                continue;
                            }

                            context.stats.files++;
                            log.debug("{}, add file: {}", path, fsInfo.getName());
                            files.add(fsInfo.getName());
                            context.getTime().put(newPath, fsInfo.getUpdatedAt());
                        }
                    } else {
                        log.debug("ignore file: {}", fsInfo.getName());
                    }
                } catch (Exception e) {
                    log.warn("index error", e);
                }
            }

            marker = fsResponse.getNext();
        } while (StringUtils.isNotEmpty(marker));

        if (hasFile) {
            context.write(path);
        }

        for (String name : folders) {
            String newPath = fixPath(path + "/" + name);
            context.write(newPath);
        }

        if (!shouldSkipFiles(files)) {
            for (String name : files) {
                String newPath = fixPath(path + "/" + name);
                context.write(newPath);
            }
        }

        taskService.updateTaskSummary(context.getTaskId(), context.stats.toString());
    }

    private boolean shouldSkipFiles(List<String> files) {
        if (files.size() <= 1) {
            return true;
        }

        double threshold = files.size() * 0.9;
        long count = files.stream().filter(e -> EPISODE.matcher(e).find()).count();
        if (count >= threshold) {
            return true;
        }

        String prefix = Utils.getCommonPrefix(files);
        String suffix = Utils.getCommonSuffix(files);
        count = files.stream().filter(e -> TextUtils.isNumber(e.replace(prefix, "").replace(suffix, ""))).count();
        return count >= threshold;
    }

    private void index(IndexContext context, String path, int depth) throws IOException {
        log.debug("path: {}  depth: {}  context: {}", path, depth, context);
        if ((context.getMaxDepth() > 0 && depth == context.getMaxDepth()) || isCancelled(context)) {
            log.debug("exit {}", depth);
            return;
        }

        if (!log.isDebugEnabled()) {
            log.info("index {} : {}", context.getSiteName(), path);
        }

        FsResponse fsResponse = aListService.listFiles(context.getSite(), path, 1, 5000);
        if (fsResponse == null) {
            log.warn("response null: {} {}", path, context.stats);
            context.stats.errors++;
            if (depth == 0) {
                Task task = taskService.getById(context.getTaskId());
                String data = task.getData();
                if (!data.contains("失效路径：")) {
                    data += "\n\n失效路径：\n";
                }
                data += path + "\n";
                taskService.updateTaskData(task.getId(), data);
            }
            return;
        }
        if (context.isExcludeExternal() && fsResponse.getProvider().contains("AList")) {
            log.warn("exclude external {}", path);
            return;
        }

        log.debug("{} get {} files", fsResponse.getProvider(), fsResponse.getFiles().size());
        List<String> files = new ArrayList<>();
        boolean hasFile = false;
        for (FsInfo fsInfo : fsResponse.getFiles()) {
            try {
                if (fsInfo.getType() == 1) { // folder
                    String newPath = fixPath(path + "/" + fsInfo.getName());
                    log.debug("new path: {}", newPath);
                    if (exclude(context.getExcludes(), newPath)) {
                        log.warn("exclude folder {}", newPath);
                        context.stats.excluded++;
                        continue;
                    }

                    context.getTime().put(newPath, fsInfo.getModified());
                    if (context.getMaxDepth() == depth + 1 && !context.isIncludeFiles()) {
                        files.add(fsInfo.getName());
                    } else {
                        if (isSeason(fsInfo.getName()) || isSpecial(fsInfo.getName())) {
                            hasFile = true;
                            continue;
                        }

                        if (context.getIndexRequest().getSleep() > 0) {
                            log.debug("sleep {}", context.getIndexRequest().getSleep());
                            Thread.sleep(context.getIndexRequest().getSleep());
                        }

                        if (isCancelled(context)) {
                            break;
                        }

                        try {
                            index(context, newPath, depth + 1);
                        } catch (Exception e) {
                            context.stats.errors++;
                            log.warn("index failed: {}", newPath, e);
                        }
                    }
                } else if (isMediaFormat(fsInfo.getName())) { // file
                    hasFile = true;
                    if (context.isIncludeFiles()) {
                        String newPath = fixPath(path + "/" + fsInfo.getName());
                        if (exclude(context.getExcludes(), newPath)) {
                            log.warn("exclude file {}", newPath);
                            context.stats.excluded++;
                            continue;
                        }

                        context.stats.files++;
                        log.debug("{}, add file: {}", path, fsInfo.getName());
                        files.add(fsInfo.getName());
                        context.getTime().put(newPath, fsInfo.getModified());
                    }
                } else {
                    log.debug("ignore file: {}", fsInfo.getName());
                }
            } catch (Exception e) {
                log.warn("index error", e);
            }
        }

        if (hasFile) {
            context.write(path);
        }

        for (String name : files) {
            String newPath = fixPath(path + "/" + name);
            context.write(newPath);
        }

        taskService.updateTaskSummary(context.getTaskId(), context.stats.toString());
    }

    private boolean isMovie(String path) {
        if (SEASON1.matcher(path).find()
                || SEASON2.matcher(path).find()
                || SEASON4.matcher(path).find()
                || EPISODE1.matcher(path).find()
        ) {
            return false;
        }
        path = path.replace("+电影", "");
        path = path.replace("+大电影", "");
        path = path.replace("+剧场版", "");
        path = path.replace("含剧场版", "");
        path = path.replace("【动漫.动画电影】", "");
        return path.contains("电影") || path.contains("剧场版");
    }

    private static boolean isSeason(String name) {
        return SEASON1.matcher(name).matches()
                || SEASON2.matcher(name).matches()
                || SEASON3.matcher(name).matches()
                || SEASON4.matcher(name).matches()
                || EPISODE1.matcher(name).find()
                ;
    }

    private static boolean isSpecial(String name) {
        return name.equalsIgnoreCase("sp")
                || name.equalsIgnoreCase("Specials")
                || name.equalsIgnoreCase("extra")
                || name.equalsIgnoreCase("ova")
                || name.equalsIgnoreCase("sc")
                || name.equalsIgnoreCase("tc")
                || name.equalsIgnoreCase("pv")
                || name.equalsIgnoreCase("MV")
                || name.equalsIgnoreCase("1080P")
                || name.equalsIgnoreCase("4K")
                || name.equalsIgnoreCase("4K.HEVC")
                || name.equals("4K高码")
                || name.equals("4K 高码")
                || name.equals("4K HDR")
                || name.equals("4K HDR 内封简繁")
                || name.equals("4K HDR高码[内封字幕]")
                || name.equals("1080P高码[内封字幕]")
                || name.equals("1080P官中压制")
                || name.equals("1080P官中")
                || name.equals("杜比视界版")
                || name.equals("SPs")
                || name.equals("简体")
                || name.equals("繁体")
                || name.equals("字幕")
                || name.equals("中文字幕")
                || name.equals("日文字幕")
                || name.equals("正片")
                || name.equals("花絮")
                || name.equals("彩蛋＋番外")
                || name.equals("彩蛋")
                || name.equals("番外")
                || name.equals("特辑")
                || name.equals("短片")
                || name.equals("国语")
                || name.equals("粤语")
                || name.equals("其它");
    }

    private boolean exclude(Set<String> rules, String path) {
        for (String rule : rules) {
            if (StringUtils.isBlank(rule)) {
                continue;
            }
            if (rule.startsWith("/") && path.startsWith(rule)) {
                return true;
            }
            if (rule.startsWith("~") && path.matches(rule.substring(1))) {
                return true;
            }
            if (rule.startsWith("^") && rule.endsWith("$") && path.equals(rule.substring(1, rule.length() - 1))) {
                return true;
            }
            if (rule.startsWith("^") && path.startsWith(rule.substring(1))) {
                return true;
            }
            if (rule.endsWith("$") && path.endsWith(rule.substring(0, rule.length() - 1))) {
                return true;
            }
            if (path.contains(rule)) {
                return true;
            }
        }
        return false;
    }

    private boolean isMediaFormat(String name) {
        int index = name.lastIndexOf('.');
        if (index > 0) {
            String suffix = name.substring(index + 1);
            return appProperties.getFormats().contains(suffix);
        }
        return false;
    }

    private String fixPath(String path) {
        return path.replaceAll("/+", "/").replace("\n", "%20");
    }

    public List<FileItem> listIndexFiles(int id) {
        try {
            Path path = Paths.get("/data/index/" + id);
            return Files.list(path)
                    .filter(p -> p.getFileName().toString().endsWith(".txt"))
                    .sorted()
                    .map(p -> new FileItem(p.getFileName().toString().replace(".txt", ""), p.toString(), 0))
                    .toList();
        } catch (Exception e) {
            log.warn("list index files " + id, e);
        }
        return List.of();
    }

    private String decodeUrl(String url) {
        try {
            return URLDecoder.decode(url, StandardCharsets.UTF_8);
        } catch (Exception e) {
            return url;
        }
    }

    @Scheduled(cron = "0 0,30 * * * *")  // 每小时的0分和30分执行
    public void checkAndUpdateFiles() {
        // 检查是否启用了自动更新
        boolean autoUpdate = settingRepository.findById("auto_quark_update")
                .map(s -> "true".equals(s.getValue()))
                .orElse(false);
                
        if (!autoUpdate) {
            return;
        }
        
        // 检查是否在时间范围内
        String startTime = settingRepository.findById("quark_update_start_time")
                .map(Setting::getValue)
                .orElse("00:00");
        String endTime = settingRepository.findById("quark_update_end_time")
                .map(Setting::getValue)
                .orElse("23:59");
        
        LocalTime now = LocalTime.now();
        LocalTime start = LocalTime.parse(startTime);
        LocalTime end = endTime.equals("24:00") ? LocalTime.parse("23:59:59") : LocalTime.parse(endTime);
        
        if (now.isBefore(start) || now.isAfter(end)) {
            return;
        }
        
        // 直接执行更新
        updateQuarkFiles();
    }

    // 添加一个新方法，用于下载带有签名的文件
    private boolean downloadFileWithAuth(String url, String outputPath) {
        try {
            // 获取默认的AList站点（ID为1）
            Site localSite = siteService.getById(1);
            
            // 从URL中提取文件路径，保留"AI老G常用分享"前缀
            String filePath = "/AI老G常用分享/" + url.substring(BASE_URL.length());
            
            // 使用AListService获取文件详情（包含签名）
            FsDetail fileDetail = aListService.getFile(localSite, filePath);
            
            if (fileDetail != null) {
                // 构建带有签名的URL
                String signedUrl = fileDetail.getRawUrl();
                if (signedUrl == null && fileDetail.getSign() != null) {
                    // 如果rawUrl为空但有签名，手动构建URL
                    signedUrl = localSite.getUrl() + "/d" + filePath + "?sign=" + fileDetail.getSign();
                }
                
                if (signedUrl != null) {
                    // log.info("==========使用签名URL下载文件: {}", signedUrl);
                    // 使用带签名的URL下载文件
                    ProcessBuilder pb = new ProcessBuilder("wget", "-O", outputPath, signedUrl);
                    Process process = pb.start();
                    return process.waitFor() == 0;
                }
            }
            
            // log.error("==========无法获取文件URL或签名: {}", url);
            return false;
        } catch (Exception e) {
            // log.error("==========下载文件失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 使用curl命令从备用源下载index.zip文件
     */
    private boolean downloadIndexZipWithCurl(String outputPath) {
        try {
            // 确保输出目录存在
            File outputFile = new File(outputPath);
            Files.createDirectories(outputFile.getParentFile().toPath());
            
            // 构建curl命令，尝试从两个不同的URL下载
            String curlCommand = "curl -sSLf http://har01d.org/index.zip -o " + outputPath + " || curl -sSLf https://ailg.ggbond.org/index.zip -o " + outputPath;
            // log.debug("执行命令: {}", curlCommand);
            
            ProcessBuilder pb = new ProcessBuilder(
                "bash", 
                "-c", 
                curlCommand
            );
            
            // 启动进程并等待完成
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            // 检查命令是否成功执行
            if (exitCode == 0) {
                // 检查文件是否存在
                if (outputFile.exists()) {
                    // 验证文件是否为有效的zip文件
                    if (isValidZipFile(outputFile)) {
                        String successMsg = "==========成功从备用源下载index.zip文件";
                        addUpdateSummary(successMsg);
                        return true;
                    } else {
                        String errorMsg = "==========下载的index.zip文件不是有效的zip文件";
                        addUpdateSummary(errorMsg);
                        // 删除无效文件
                        try {
                            Files.deleteIfExists(outputFile.toPath());
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                        return false;
                    }
                } else {
                    String errorMsg = "==========index.zip文件下载失败";
                    addUpdateSummary(errorMsg);
                    return false;
                }
            } else {
                String errorMsg = "==========从备用源下载index.zip文件失败，退出代码：" + exitCode;
                addUpdateSummary(errorMsg);
                return false;
            }
        } catch (Exception e) {
            String errorMsg = "==========从备用源下载index.zip文件时出现异常：" + e.getMessage();
            addUpdateSummary(errorMsg);
            return false;
        }
    }
    
    /**
     * 验证文件是否为有效的zip文件
     */
    private boolean isValidZipFile(File file) {
        try (ZipFile zipFile = new ZipFile(file)) {
            // 如果能成功打开ZipFile对象，则文件是有效的zip文件
            return true;
        } catch (IOException e) {
            String errorMsg = "==========验证zip文件失败：" + e.getMessage();
            addUpdateSummary(errorMsg);
            return false;
        }
    }

    public void updateQuarkFiles() {
        // 初始化日志收集列表
        updateSummaryLogs = new ArrayList<>();
        
        try {
            // 检查 AList 状态
            if (aListLocalService.getAListStatus() != 2) {  // 2 表示运行状态
                return;
            }

            String message = "==========开始更新小雅启动文件";
            // log.info(message);
            addUpdateSummary(message);
            
            // 先检查版本
            String tempDir = "/tmp/quark_update";
            Files.createDirectories(Paths.get(tempDir));
            String versionUrl = BASE_URL + "小雅A-list启动4文件/version.txt";
            String tempVersionPath = tempDir + "/version.txt";
            
            // 检查115share_list.txt文件是否有更新
            String shareListUrl = BASE_URL + "小雅A-list启动4文件/115share_list.txt";
            String tempShareListPath = tempDir + "/115share_list.txt";
            String localShareListPath = "/data/115share_list.txt";
            
            try {
                // 使用带签名的方式下载115share_list.txt文件
                if (downloadFileWithAuth(shareListUrl, tempShareListPath)) {
                    // 检查文件内容是否有效
                    Path tempShareListFile = Paths.get(tempShareListPath);
                    
                    // 由于115share_list.txt是文本文件，可以安全地读取内容
                    String content = Files.readString(tempShareListFile);
                    
                    // 检查文件内容是否为错误响应
                    if (content.startsWith("{\"code\":") && content.contains("\"message\":")) {
                        String errorMsg = "==========115share_list.txt 下载内容无效，返回了错误响应: " + content;
                        addUpdateSummary(errorMsg);
                        Files.deleteIfExists(tempShareListFile);
                    } else {
                        Path localShareListFile = Paths.get(localShareListPath);
                        
                        boolean needUpdate = false;
                        if (Files.exists(localShareListFile)) {
                            // 读取本地文件内容
                            String localContent = Files.readString(localShareListFile);
                            
                            // 检查本地文件是否为错误响应
                            if (localContent.startsWith("{\"code\":") && localContent.contains("\"message\":")) {
                                String infoMsg = "==========本地115share_list.txt内容无效，需要更新";
                                addUpdateSummary(infoMsg);
                                needUpdate = true;
                            } else {
                                // 比较文件内容而不是大小
                                // log.info("==========比较115share_list.txt文件内容");
                                needUpdate = !content.equals(localContent);
                                // if (needUpdate) {
                                //     log.info("==========检测到115share_list.txt文件内容变化，需要更新");
                                // }
                            }
                        }
                        
                        // 如果需要更新，替换本地文件
                        if (needUpdate) {
                            // log.info("==========115share_list.txt 文件有更新，正在替换");
                            Files.createDirectories(localShareListFile.getParent());
                            Files.move(tempShareListFile, localShareListFile, StandardCopyOption.REPLACE_EXISTING);
                            String successMsg = "==========115share_list.txt 文件更新完成，重启生效！";
                            addUpdateSummary(successMsg);
                        } else {
                            String noUpdateMsg = "==========115share_list.tx无需更新";
                            addUpdateSummary(noUpdateMsg);
                            Files.deleteIfExists(tempShareListFile);
                        }
                    }
                } else {
                    String errorMsg = "==========下载115share_list.txt文件失败";
                    addUpdateSummary(errorMsg);
                }
            } catch (Exception e) {
                String errorMsg = "==========处理115share_list.txt文件时出错";
                addUpdateSummary(errorMsg);
            }
            
            // 下载远程版本文件，使用带签名的方式
            if (!downloadFileWithAuth(versionUrl, tempVersionPath)) {
                String errorMsg = "==========下载小雅启动文件版本文件失败";
                addUpdateSummary(errorMsg);
                // 在退出前输出摘要
                outputSummary();
                return;
            }
            
            // 读取远程版本号
            String remoteVersion = Files.readString(Paths.get(tempVersionPath)).trim();
            
            // 检查版本文件内容是否有效
            if (remoteVersion.startsWith("{\"code\":") && remoteVersion.contains("\"message\":")) {
                String errorMsg = "==========版本文件内容无效，返回了错误响应: " + remoteVersion;
                addUpdateSummary(errorMsg);
                Files.deleteIfExists(Paths.get(tempVersionPath));
                // 在退出前输出摘要
                outputSummary();
                return;
            }
            
            // 读取本地版本号
            String localVersion = "";
            Path localVersionPath = Paths.get("/www/data/version.txt");
            if (Files.exists(localVersionPath)) {
                localVersion = Files.readString(localVersionPath).trim();
            }
            
            // 版本相同，跳过更新
            if (localVersion.equals(remoteVersion)) {
                String infoMsg = "==========版本相同，无需更新: " + remoteVersion;
                addUpdateSummary(infoMsg);
                // 在退出前输出摘要
                outputSummary();
                return;
            }
            
            String updateMsg = "==========小雅启动文件检测到新版本，开始更新: " + localVersion + " -> " + remoteVersion;
            addUpdateSummary(updateMsg);
            
            // 确保目录存在
            Files.createDirectories(Paths.get("/www/data"));
            
            // 先移动版本文件
            Files.move(Paths.get(tempVersionPath), localVersionPath, StandardCopyOption.REPLACE_EXISTING);
            
            // 存储本次备份的文件路径，用于恢复
            Map<String, Path> backupFiles = new HashMap<>();
            List<String> downloadedFiles = new ArrayList<>();
            
            // 下载并更新其他文件
            boolean allSuccess = true;
            for (String filePath : FILES_TO_DOWNLOAD) {
                // 跳过版本文件
                if (filePath.endsWith("/version.txt")) {
                    continue;
                }
                
                // 跳过115share_list.txt文件，因为已经单独处理过了
                if (filePath.endsWith("/115share_list.txt")) {
                    continue;
                }
                
                // 从路径中提取文件名
                String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
                String url = BASE_URL + filePath;
                String outputPath = "/www/data/" + fileName;
                
                // 备份现有文件
                Path existingFile = Paths.get(outputPath);
                if (Files.exists(existingFile)) {
                    String backupTime = java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(java.time.LocalDateTime.now());
                    Path backupPath = Paths.get("/www/data/" + fileName + ".bak." + backupTime);
                    Files.createDirectories(backupPath.getParent());
                    Files.move(existingFile, backupPath);
                    backupFiles.put(fileName, backupPath);  // 记录备份文件路径
                }
                
                // 最多尝试两次下载
                boolean downloadSuccess = false;
                for (int attempt = 1; attempt <= 2 && !downloadSuccess; attempt++) {
                    try {
                        // 根据文件名选择不同的下载方法
                        if ("index.zip".equals(fileName)) {
                            // 使用curl从备用源下载index.zip文件
                            downloadSuccess = downloadIndexZipWithCurl(outputPath);
                        } else {
                            // 其他文件使用原有方法
                            downloadSuccess = downloadFileWithAuth(url, outputPath);
                        }
                        
                        if (downloadSuccess) {
                            downloadedFiles.add(outputPath);  // 记录下载的文件
                        } else if (attempt < 2) {
                            String retryMsg = "==========尝试重新下载文件 " + fileName;
                            addUpdateSummary(retryMsg);
                            Thread.sleep(3000);
                        }
                    } catch (Exception e) {
                        String errorMsg = "==========下载文件 " + fileName + " 时出错: " + e.getMessage();
                        addUpdateSummary(errorMsg);
                        if (attempt < 2) {
                            Thread.sleep(3000);
                        }
                    }
                }
                
                if (!downloadSuccess) {
                    String failMsg = "==========小雅启动文件下载失败，已尝试2次: " + fileName;
                    addUpdateSummary(failMsg);
                    allSuccess = false;
                    break;  // 任何文件失败就中断处理
                }
            }
            
            if (!allSuccess) {
                String recoverMsg = "==========小雅启动文件部分更新失败，开始恢复备份";
                addUpdateSummary(recoverMsg);
                
                // 删除本次下载的所有文件
                for (String downloadedFile : downloadedFiles) {
                    try {
                        Files.deleteIfExists(Paths.get(downloadedFile));
                    } catch (Exception e) {
                        String deleteErrorMsg = "==========删除下载文件失败: " + downloadedFile;
                        addUpdateSummary(deleteErrorMsg);
                    }
                }
                
                // 恢复备份的文件
                for (Map.Entry<String, Path> entry : backupFiles.entrySet()) {
                    try {
                        Files.move(entry.getValue(), Paths.get("/www/data/" + entry.getKey()), StandardCopyOption.REPLACE_EXISTING);
                    } catch (Exception e) {
                        String restoreErrorMsg = "==========恢复小雅启动文件备份文件失败: " + entry.getKey();
                        addUpdateSummary(restoreErrorMsg);
                    }
                }
                
                // 恢复版本文件
                if (Files.exists(localVersionPath)) {
                    try {
                        Files.write(localVersionPath, localVersion.getBytes());
                    } catch (Exception e) {
                        String versionErrorMsg = "==========恢复小雅启动文件版本文件失败";
                        addUpdateSummary(versionErrorMsg);
                    }
                }
            } else {
                // 如果所有文件更新成功，执行 index.sh
                String successMsg = "==========小雅启动文件更新成功，开始更新索引，版本: " + remoteVersion;
                addUpdateSummary(successMsg);
                updateXiaoyaIndexFile(remoteVersion);               
            }
            
            // 输出摘要日志
            outputSummary();
            
        } catch (Exception e) {
            String errorMsg = "==========小雅启动文件更新过程出错";
            addUpdateSummary(errorMsg);
            // 即使出现异常，也要输出摘要日志
            outputSummary();
        }
    }

    /**
     * 停止夸克网盘文件自动更新任务
     */
    // @PostMapping("/api/index/stop-quark-update")
    public void stopQuarkUpdate() {
        try {
            // 只需要更新设置，@Scheduled 任务会自动检查设置
            settingRepository.save(new Setting("auto_quark_update", "false"));
            
            // 同步到AList数据库
            syncSettingToAList("auto_quark_update", "false");
            
            log.info("夸克网盘文件自动更新任务已停止");
        } catch (Exception e) {
            log.error("停止夸克网盘文件自动更新任务失败", e);
            throw new RuntimeException("停止自动更新任务失败: " + e.getMessage());
        }
    }

    public void updateQuarkFileStatus(boolean enabled) {
        try {
            // 确保目录存在
            File dataDir = new File(DATA_DIR);
            
            if (!dataDir.exists()) {
                boolean created = dataDir.mkdirs();
                if (!created) {
                    throw new IOException("无法创建目录: " + DATA_DIR);
                }
            }
            
            File file = new File(DATA_DIR, "auto_quark_update.txt");

            try (FileWriter writer = new FileWriter(file)) {
                writer.write(String.valueOf(enabled));
            }            
        } catch (IOException e) {
            throw new RuntimeException("更新夸克文件状态失败: " + e.getMessage());
        }
    }
}

