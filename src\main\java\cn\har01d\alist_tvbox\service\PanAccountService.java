package cn.har01d.alist_tvbox.service;

import cn.har01d.alist_tvbox.config.AppProperties;
import cn.har01d.alist_tvbox.domain.DriverType;
import cn.har01d.alist_tvbox.entity.DriverAccount;
import cn.har01d.alist_tvbox.entity.DriverAccountRepository;
import cn.har01d.alist_tvbox.entity.PanAccountRepository;
import cn.har01d.alist_tvbox.entity.Setting;
import cn.har01d.alist_tvbox.entity.SettingRepository;
import cn.har01d.alist_tvbox.entity.Share;
import cn.har01d.alist_tvbox.entity.ShareRepository;
import cn.har01d.alist_tvbox.exception.BadRequestException;
import cn.har01d.alist_tvbox.exception.NotFoundException;
import cn.har01d.alist_tvbox.model.SettingResponse;
import cn.har01d.alist_tvbox.model.Response;
import cn.har01d.alist_tvbox.util.Utils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import java.util.Collections;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@Service
public class PanAccountService {
    public static final int IDX = 3900;
    private final PanAccountRepository panAccountRepository;
    private final DriverAccountRepository driverAccountRepository;
    private final SettingRepository settingRepository;
    private final ShareRepository shareRepository;
    private final AccountService accountService;
    private final AListLocalService aListLocalService;
    private final RestTemplate restTemplate;
    private final Map<String, QuarkUCTV> drivers = new HashMap<>();


    public PanAccountService(PanAccountRepository panAccountRepository, 
                             DriverAccountRepository driverAccountRepository,    
                             SettingRepository settingRepository, 
                             ShareRepository shareRepository, 
                             AccountService accountService, 
                             AListLocalService aListLocalService, 
                             AppProperties appProperties, 
                             RestTemplateBuilder builder) {
        this.panAccountRepository = panAccountRepository;
        this.driverAccountRepository = driverAccountRepository;
        this.settingRepository = settingRepository;
        this.shareRepository = shareRepository;
        this.accountService = accountService;
        this.aListLocalService = aListLocalService;
        this.restTemplate = builder.rootUri("http://localhost:" + (appProperties.isHostmode() ? "5234" : "5244")).build();
    }

    @PostConstruct
    public void init() {
        if (!settingRepository.existsByName("migrate_pan_account")) {
            migratePanAccounts();
        }
        if (!settingRepository.existsByName("migrate_driver_account")) {
            migrateDriverAccounts();
        }
        
        String deviceId = settingRepository.findById("quark_device_id").map(Setting::getValue).orElse(null);
        if (deviceId == null) {
            deviceId = QuarkUCTV.generateDeviceId();
            settingRepository.save(new Setting("quark_device_id", deviceId));
        }
        drivers.put("QUARK_TV", new QuarkUCTV(restTemplate, new QuarkUCTV.Conf("https://open-api-drive.quark.cn", "d3194e61504e493eb6222857bccfed94", "kw2dvtd7p4t3pjl2d9ed9yc8yej8kw2d", "1.5.6", "CP", "http://api.extscreen.com/quarkdrive", deviceId)));
        drivers.put("UC_TV", new QuarkUCTV(restTemplate, new QuarkUCTV.Conf("https://open-api-drive.uc.cn", "5acf882d27b74502b7040b0c65519aa7", "l3srvtd7p42l0d0x1u8d7yc8ye9kki4d", "1.6.5", "UCTVOFFICIALWEB", "http://api.extscreen.com/ucdrive", deviceId)));
    }

    private void migrateDriverAccounts() {
        List<DriverAccount> accounts = new ArrayList<>();
        for (var item : panAccountRepository.findAll()) {
            var account = new DriverAccount();
            account.setId(item.getId());
            account.setName(item.getName());
            account.setType(item.getType());
            account.setCookie(item.getCookie());
            account.setToken(item.getToken());
            account.setMaster(item.isMaster());
            account.setUseProxy(item.isUseProxy());
            account.setFolder(item.getFolder());
            accounts.add(account);
        }
        driverAccountRepository.saveAll(accounts);
        log.info("migrated {} accounts", accounts.size());
        settingRepository.save(new Setting("migrate_driver_account", "true"));
    }

    private void migratePanAccounts() {
        List<DriverAccount> accounts = new ArrayList<>();
        List<Share> shares = shareRepository.findAll();
        List<Share> deleted = new ArrayList<>();
        boolean master2 = true;
        boolean master3 = true;
        boolean master6 = true;
        for (Share share : shares) {
            if (share.getType() == 2 || share.getType() == 3 || share.getType() == 6) {
                DriverAccount account = new DriverAccount();
                if (share.getType() == 2) {
                    account.setType(DriverType.QUARK);
                    account.setMaster(master2);
                    master2 = false;
                } else if (share.getType() == 3) {
                    account.setType(DriverType.PAN115);
                    account.setMaster(master3);
                    master3 = false;
                } else if (share.getType() == 6) {
                    account.setType(DriverType.UC);
                    account.setMaster(master6);
                    master6 = false;
                }
                account.setName(getNameFromPath(share.getPath()));
                account.setFolder(share.getFolderId());
                account.setCookie(share.getCookie());
                accounts.add(account);
                deleted.add(share);
            }
        }
        driverAccountRepository.saveAll(accounts);
        shareRepository.deleteAll(deleted);
        log.info("migrated {} accounts", accounts.size());
        settingRepository.save(new Setting("migrate_pan_account", "true"));
    }

    private String getNameFromPath(String path) {
        return path.substring(path.lastIndexOf("/") + 1);
    }

    public void loadStorages() {
        List<DriverAccount> accounts = driverAccountRepository.findAll();
        for (DriverAccount account : accounts) {
            insertAList(account);
        }
    }

    private void insertAList(DriverAccount account) {
        String deviceId = settingRepository.findById("quark_device_id").map(Setting::getValue).orElse("");
        int id = account.getId() + IDX;
        if (account.getType() == DriverType.QUARK) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'Quark',30,'work','{\"cookie\":\"%s\",\"root_folder_id\":\"%s\",\"order_by\":\"name\",\"order_direction\":\"ASC\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'native_proxy','');";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getCookie(), account.getFolder()));
            log.info("insert Quark account {} : {}, result: {}", id, getMountPath(account), count);
            Utils.executeUpdate("INSERT INTO x_setting_items VALUES('quark_cookie','" + account.getCookie() + "','','text','',1,0);");
        } else if (account.getType() == DriverType.QUARK_TV) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'QuarkTV',30,'work','{\"refresh_token\":\"%s\",\"device_id\":\"%s\",\"root_folder_id\":\"%s\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','');";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getToken(), deviceId, account.getFolder()));
            log.info("insert QuarkTV account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.UC) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'UC',30,'work','{\"cookie\":\"%s\",\"root_folder_id\":\"%s\",\"order_by\":\"name\",\"order_direction\":\"ASC\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'native_proxy','');";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getCookie(), account.getFolder()));
            log.info("insert UC account {} : {}, result: {}", id, getMountPath(account), count);
            Utils.executeUpdate("INSERT INTO x_setting_items VALUES('uc_cookie','" + account.getCookie() + "','','text','',1,0);");
        } else if (account.getType() == DriverType.UC_TV) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'UCTV',30,'work','{\"refresh_token\":\"%s\",\"device_id\":\"%s\",\"root_folder_id\":\"%s\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','');";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getToken(), deviceId, account.getFolder()));
            log.info("insert UCTV account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.THUNDER) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'ThunderBrowser',30,'work','{\"username\":\"%s\",\"password\":\"%s\",\"safe_password\":\"%s\",\"root_folder_id\":\"%s\",\"remove_way\":\"delete\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','');";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getUsername(), account.getPassword(), account.getSafePassword(), account.getFolder()));
            log.info("insert ThunderBrowser account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.CLOUD189) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'189CloudPC',30,'work','{\"username\":\"%s\",\"password\":\"%s\",\"validate_code\":\"%s\",\"root_folder_id\":\"%s\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','');";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getUsername(), account.getPassword(), account.getToken(), account.getFolder()));
            log.info("insert 189CloudPC account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.PAN123) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'123Pan',30,'work','{\"username\":\"%s\",\"password\":\"%s\",\"root_folder_id\":\"%s\",\"platformType\":\"android\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','');";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getUsername(), account.getPassword(), account.getFolder()));
            log.info("insert 123Pan account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.PAN139) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'139Yun',30,'work','{\"authorization\":\"%s\",\"root_folder_id\":\"%s\",\"type\":\"personal_new\",\"cloud_id\":\"\",\"custom_upload_part_size\":0}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','');";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getToken(), account.getFolder()));
            log.info("insert 139Yun account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.PAN115) {
            String sql;
            if (account.isUseProxy()) {
                // ==========0922 c32
                // sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'115 Cloud',30,'work','{\"cookie\":\"%s\",\"qrcode_token\":\"%s\",\"root_folder_id\":\"%s\",\"page_size\":56}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',1,'native_proxy','');";
                sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'115 Cloud',30,'work','{\"cookie\":\"%s\",\"qrcode_token\":\"%s\",\"root_folder_id\":\"%s\",\"page_size\":300,\"limit_rate\":2}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',1,'native_proxy','');";
            } else {
                // ==========0922 c32
                // sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'115 Cloud',30,'work','{\"cookie\":\"%s\",\"qrcode_token\":\"%s\",\"root_folder_id\":\"%s\",\"page_size\":56}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','');";
                sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'115 Cloud',30,'work','{\"cookie\":\"%s\",\"qrcode_token\":\"%s\",\"root_folder_id\":\"%s\",\"page_size\":300,\"limit_rate\":2}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','');";
            }
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getCookie(), account.getToken(), account.getFolder()));
            log.info("insert 115 account {}: {}, result: {}", id, getMountPath(account), count);
            Utils.executeUpdate("INSERT INTO x_setting_items VALUES('115_cookie','" + account.getCookie() + "','','text','',1,0);");
        }
    }

    private void updateAList(DriverAccount account) {
        int id = account.getId() + IDX;
        if (account.getType() == DriverType.QUARK) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'Quark',30,'work','{\"cookie\":\"%s\",\"root_folder_id\":\"%s\",\"order_by\":\"name\",\"order_direction\":\"ASC\"}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',0,'native_proxy','',0);";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getCookie(), account.getFolder()));
            log.info("insert Quark account {} : {}, result: {}", id, getMountPath(account), count);
            Utils.executeUpdate("INSERT INTO x_setting_items VALUES('quark_cookie','" + account.getCookie() + "','','text','',1,0);");
        } else if (account.getType() == DriverType.QUARK_TV) {
            String deviceId = settingRepository.findById("quark_device_id").map(Setting::getValue).orElse("");
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'QuarkTV',30,'work','{\"refresh_token\":\"%s\",\"device_id\":\"%s\",\"root_folder_id\":\"%s\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','',0);";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getToken(), deviceId, account.getFolder()));
            log.info("insert QuarkTV account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.UC) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'UC',30,'work','{\"cookie\":\"%s\",\"root_folder_id\":\"%s\",\"order_by\":\"name\",\"order_direction\":\"ASC\"}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',0,'native_proxy','',0);";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getCookie(), account.getFolder()));
            log.info("insert UC account {} : {}, result: {}", id, getMountPath(account), count);
            Utils.executeUpdate("INSERT INTO x_setting_items VALUES('uc_cookie','" + account.getCookie() + "','','text','',1,0);");
        } else if (account.getType() == DriverType.UC_TV) {
            String deviceId = settingRepository.findById("quark_device_id").map(Setting::getValue).orElse("");
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'UCTV',30,'work','{\"refresh_token\":\"%s\",\"device_id\":\"%s\",\"root_folder_id\":\"%s\"}','','2023-06-15 12:00:00+00:00',0,'name','ASC','',0,'302_redirect','',0);";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getToken(), deviceId, account.getFolder()));
            log.info("insert UCTV account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.THUNDER) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'ThunderBrowser',30,'work','{\"username\":\"%s\",\"password\":\"%s\",\"safe_password\":\"%s\",\"root_folder_id\":\"%s\",\"remove_way\":\"delete\"}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',0,'302_redirect','',0);";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getUsername(), account.getPassword(), account.getSafePassword(), account.getFolder()));
            log.info("insert ThunderBrowser account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.CLOUD189) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'189CloudPC',30,'work','{\"username\":\"%s\",\"password\":\"%s\",\"validate_code\":\"%s\",\"root_folder_id\":\"%s\"}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',0,'302_redirect','',0);";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getUsername(), account.getPassword(), account.getToken(), account.getFolder()));
            log.info("insert 189CloudPC account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.PAN123) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'123Pan',30,'work','{\"username\":\"%s\",\"password\":\"%s\",\"root_folder_id\":\"%s\",\"platformType\":\"android\"}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',0,'302_redirect','',0);";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getUsername(), account.getPassword(), account.getFolder()));
            log.info("insert 123Pan account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.PAN139) {
            String sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'139Yun',30,'work','{\"authorization\":\"%s\",\"root_folder_id\":\"%s\",\"type\":\"personal_new\",\"cloud_id\":\"\",\"custom_upload_part_size\":0}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',0,'302_redirect','',0);";
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getToken(), account.getFolder()));
            log.info("insert 139Yun account {} : {}, result: {}", id, getMountPath(account), count);
        } else if (account.getType() == DriverType.PAN115) {
            String sql;
            if (account.isUseProxy()) {
                // ==========0922 c35
                // sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'115 Cloud',30,'work','{\"cookie\":\"%s\",\"qrcode_token\":\"%s\",\"root_folder_id\":\"%s\",\"page_size\":56}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',1,'native_proxy','',0);";
                sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'115 Cloud',30,'work','{\"cookie\":\"%s\",\"qrcode_token\":\"%s\",\"root_folder_id\":\"%s\",\"page_size\":300,\"limit_rate\":2,\"qrcode_source\":\"linux\"}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',1,'native_proxy','',0);";
            } else {
                // ==========0922 c34
                // sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'115 Cloud',30,'work','{\"cookie\":\"%s\",\"qrcode_token\":\"%s\",\"root_folder_id\":\"%s\",\"page_size\":56}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',0,'302_redirect','',0);";
                sql = "INSERT INTO x_storages VALUES(%d,'%s',0,'115 Cloud',30,'work','{\"cookie\":\"%s\",\"qrcode_token\":\"%s\",\"root_folder_id\":\"%s\",\"page_size\":300,\"limit_rate\":2,\"qrcode_source\":\"linux\"}','','2023-06-15 12:00:00+00:00',1,'name','ASC','',0,'302_redirect','',0);";
            }
            int count = Utils.executeUpdate(String.format(sql, id, getMountPath(account), account.getCookie(), account.getToken(), account.getFolder()));
            log.info("insert 115 account {}: {}, result: {}", id, getMountPath(account), count);
            Utils.executeUpdate("INSERT INTO x_setting_items VALUES('115_cookie','" + account.getCookie() + "','','text','',1,0);");
        }
    }

    private String getMountPath(DriverAccount account) {
        if (account.getName().startsWith("/")) {
            return account.getName();
        }
        if (account.getType() == DriverType.QUARK) {
            // ==========0922 c36
            return "/\uD83C\uDF52我的夸克网盘/" + account.getName();
        } else if (account.getType() == DriverType.UC) {
            // ==========0922 c37
            return "/\uD83C\uDF4D我的UC网盘/" + account.getName();
        } else if (account.getType() == DriverType.QUARK_TV) {
            return "/\uD83C\uDF46夸克网盘TV/" + account.getName();
        } else if (account.getType() == DriverType.UC_TV) {
            return "/\uD83C\uDF46UC网盘TV/" + account.getName();
        } else if (account.getType() == DriverType.PAN115) {
            // ==========0922 c38
            return "/\uD83E\uDD5D115网盘/" + account.getName();
        } else if (account.getType() == DriverType.THUNDER) {
            return "/\uD83C\uDF50我的迅雷云盘/" + account.getName();
        } else if (account.getType() == DriverType.CLOUD189) {
            return "/\uD83C\uDF45我的天翼云盘/" + account.getName();
        } else if (account.getType() == DriverType.PAN139) {
            return "/\uD83C\uDF4B我的移动云盘/" + account.getName();
        } else if (account.getType() == DriverType.PAN123) {
            return "/\uD83C\uDF4F我的123网盘/" + account.getName();
        }
        
        // ==========0922 c39
        return "/\uD83C\uDF48网盘" + account.getName();
        // cn.har01d.alist_tvbox.service.TvBoxService.addMyFavorite
    }

    public List<DriverAccount> list() {
        return driverAccountRepository.findAll();
    }

    public DriverAccount get(int id) {
        return driverAccountRepository.findById(id).orElseThrow(NotFoundException::new);
    }

    public DriverAccount create(DriverAccount account) {
        validate(account);
        
        if (driverAccountRepository.existsByNameAndType(account.getName(), account.getType())) {
            throw new BadRequestException("账号名称已经存在");
        }
        if (driverAccountRepository.count() == 0) {
            aListLocalService.startAListServer();
        }
        account.setId(null);

        if (driverAccountRepository.countByType(account.getType()) == 0) {
            account.setMaster(true);
        } else {
            updateMaster(account);
        }
        driverAccountRepository.save(account);

        updateStorage(account);

        return account;
    }

    // public PanAccount update(Integer id, PanAccount dto) {
    //     validate(dto);
    //     var account = get(id);
    //     var other = panAccountRepository.findByNameAndType(dto.getName(), dto.getType());
    //     if (other != null && !other.getId().equals(id)) {
    //         throw new BadRequestException("账号名称已经存在");
    //     }

    //     boolean changed = account.isMaster() != dto.isMaster()
    //             || account.isUseProxy() != dto.isUseProxy()
    //             || !account.getType().equals(dto.getType())
    //             || !account.getToken().equals(dto.getToken())
    //             || !account.getCookie().equals(dto.getCookie())
    //             || !account.getFolder().equals(dto.getFolder())
    //             || !account.getName().equals(dto.getName());

    //     account.setMaster(dto.isMaster());
    //     account.setUseProxy(dto.isUseProxy());
    //     account.setName(dto.getName());
    //     account.setType(dto.getType());
    //     account.setCookie(dto.getCookie());
    //     account.setToken(dto.getToken());
    //     account.setFolder(dto.getFolder());

    //     if (panAccountRepository.countByType(account.getType()) == 0) {
    //         account.setMaster(true);
    //     }

    //     if (changed && account.isMaster()) {
    //         updateMaster(account);
    //     }

    //     panAccountRepository.save(account);

    //     updateStorage(account);

    //     return account;
    // }

    // ===1221测试更新cookie免重启
    @Async
    public void updateStorageAsync(DriverAccount account, String driver) {
        // 只有115网盘需要更新cookie和reload
        if (account.getType() == DriverType.PAN115) {
            // 1. 更新数据库中所有相关存储的cookie
            // String sql = "UPDATE x_storages SET addition = json_set(addition, '$.cookie', ?) WHERE driver = ?";
            String sql = "UPDATE x_storages SET addition = json_set(addition, '$.cookie', '%s') WHERE driver = '%s'";
            int updated = Utils.executeUpdate(String.format(sql, account.getCookie(), driver));
            // 由于是异步，以面的日志没有意义，只会显示0条，实际更新成功
            log.info("更新了 {} 个{}类型的存储的cookie", updated, driver);


            reloadStorages(driver);
        }
    }

    private void reloadStorages(String driver) {
        // 2. 获取所有相关存储ID
        String idSql = "SELECT id FROM x_storages WHERE driver = '" + driver + "'";
        try {
            List<Integer> storageIds = new ArrayList<>();
            String result = Utils.executeQuery(idSql);
            for (String line : result.split("\n")) {
                try {
                    if (!line.trim().isEmpty()) {
                        storageIds.add(Integer.parseInt(line.trim()));
                    }
                } catch (NumberFormatException e) {
                    log.warn("解析存储ID失败: {}", line);
                }
            }
            
            // 3. 对每个存储执行reload
            for (Integer storageId : storageIds) {
                try {
                    aListLocalService.validateAListStatus();
                    HttpHeaders headers = new HttpHeaders();
                    headers.put("Authorization", Collections.singletonList(accountService.login()));
                    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(null, headers);
                    ResponseEntity<Response> response = restTemplate.exchange("/api/admin/storage/reload?id=" + storageId, HttpMethod.POST, entity, Response.class);
                    log.debug("重新加载存储: {} - {}", storageId, response.getBody());
                } catch (Exception e) {
                    log.error("重新加载存储失败: " + storageId, e);
                }
            }
        } catch (Exception e) {
            log.error("更新存储失败", e);
        }
    }
    
    public DriverAccount update(Integer id, DriverAccount dto) {
        validate(dto);
        
        var account = get(id);
        var other = driverAccountRepository.findByNameAndType(dto.getName(), dto.getType());
        if (other != null && !other.getId().equals(id)) {
            throw new BadRequestException("账号名称已经存在");
        }
        
        boolean cookieChanged = !account.getCookie().equals(dto.getCookie());
        boolean changed = account.isMaster() != dto.isMaster()
                || account.isUseProxy() != dto.isUseProxy()
                || !account.getType().equals(dto.getType())
                || !account.getToken().equals(dto.getToken())
                || !account.getCookie().equals(dto.getCookie())
                || !account.getFolder().equals(dto.getFolder())
                || !account.getName().equals(dto.getName());
    
        account.setMaster(dto.isMaster());
        account.setUseProxy(dto.isUseProxy());
        account.setName(dto.getName());
        account.setType(dto.getType());
        account.setCookie(dto.getCookie());
        account.setToken(dto.getToken());
        account.setUsername(dto.getUsername());
        account.setPassword(dto.getPassword());
        account.setSafePassword(dto.getSafePassword());
        account.setFolder(dto.getFolder());
    
        if (driverAccountRepository.countByType(account.getType()) == 0) {
            account.setMaster(true);
        }

        if (changed && account.isMaster()) {
            updateMaster(account);
        }
        // 先保存账号更新
        driverAccountRepository.save(account);
        updateStorage(account);
    
        if (changed && account.isMaster() && cookieChanged) {
            // 如果是主账号且发生了变化，更新所有相关分享存储
            String driver = switch (account.getType()) {
                case QUARK -> "QuarkShare";
                case PAN115 -> "115 Share";
                case UC -> "UCShare";
                case THUNDER -> "ThunderShare";
                case CLOUD189 -> "189CloudShare";
                default -> throw new IllegalArgumentException("Unsupported driver type: " + account.getType());
            };
            
            // 异步执行更新操作
            updateStorageAsync(account, driver);
        }
    
        return account;
    }

    public void delete(Integer id) {
        DriverAccount account = driverAccountRepository.findById(id).orElse(null);
        if (account != null) {
            if (account.isMaster() && account.getType() != DriverType.UC_TV && account.getType() != DriverType.QUARK_TV) {
                throw new BadRequestException("不能删除主账号");
            }
            driverAccountRepository.deleteById(id);
            String token = accountService.login();
            accountService.deleteStorage(IDX + account.getId(), token);
        }
    }

    private void validate(DriverAccount dto) {
        if (StringUtils.isBlank(dto.getName())) {
            throw new BadRequestException("名称不能为空");
        }
//        if (dto.getName().contains("/")) {
//            throw new BadRequestException("名称不能包含/");
//        }
        if (dto.getType() == null) {
            throw new BadRequestException("类型不能为空");
        }
        if (dto.getType() == DriverType.THUNDER || dto.getType() == DriverType.CLOUD189 || dto.getType() == DriverType.PAN123) {
            if (StringUtils.isBlank(dto.getUsername())) {
                throw new BadRequestException("用户名不能为空");
            }
            if (StringUtils.isBlank(dto.getPassword())) {
                throw new BadRequestException("密码不能为空");
            }
        } else if (dto.getType() == DriverType.PAN139) {
            if (StringUtils.isBlank(dto.getToken())) {
                throw new BadRequestException("Token不能为空");
            }
        } else if (StringUtils.isBlank(dto.getCookie()) && StringUtils.isBlank(dto.getToken())) {
            throw new BadRequestException("Cookie和Token不能同时为空");
        }
        if (StringUtils.isBlank(dto.getFolder())) {
            if (dto.getType() == DriverType.QUARK || dto.getType() == DriverType.UC || dto.getType() == DriverType.QUARK_TV || dto.getType() == DriverType.UC_TV || dto.getType() == DriverType.PAN115 || dto.getType() == DriverType.PAN123) {
                dto.setFolder("0");
            } else if (dto.getType() == DriverType.CLOUD189) {
                dto.setFolder("-11");
            }
        }
        if (dto.getCookie() != null) {
            dto.setCookie(dto.getCookie().trim());
        }
    }

    private void updateMaster(DriverAccount account) {
        if (account.isMaster()) {
            log.info("reset account master");
            List<DriverAccount> list = driverAccountRepository.findAll();
            list = list.stream().filter(e -> e.getType() == account.getType()).toList();
            for (DriverAccount a : list) {
                a.setMaster(false);
            }
            account.setMaster(true);
            String key = switch (account.getType()) {
                case QUARK -> "quark_cookie";
                case PAN115 -> "115_cookie";
                case UC -> "uc_cookie";
                case THUNDER -> "thunder_cookie";
                case CLOUD189 -> "189_cookie";
                default -> "";
            };
            if (key.isEmpty()) {
                return;
            }
            aListLocalService.updateSetting(key, account.getCookie(), "string");
            driverAccountRepository.saveAll(list);
        }
    }

    private void updateStorage(DriverAccount account) {
        int status = aListLocalService.getAListStatus();
        try {
            int id = IDX + account.getId();
            String token = status == 2 ? accountService.login() : "";
            if (status == 2) {
                accountService.deleteStorage(id, token);
            }
            updateAList(account);
            if (status == 2) {
                accountService.enableStorage(id, token);
            }
        } catch (Exception e) {
            throw new BadRequestException(e);
        }
    }

    public QuarkUCTV.LoginResponse getQrCode(String type) {
        QuarkUCTV driver = drivers.get(type);
        if (driver == null) {
            throw new BadRequestException("不支持的类型");
        }
        return driver.getLoginCode();
    }

    public String getRefreshToken(String type, String queryToken) {
        QuarkUCTV driver = drivers.get(type);
        if (driver == null) {
            throw new BadRequestException("不支持的类型");
        }
        String code = driver.getCode(queryToken);
        return driver.getRefreshToken(code);
    }

    @Scheduled(initialDelay = 1800_000, fixedDelay = 1800_000)
    public void syncCookies() {
        if (aListLocalService.getAListStatus() != 2) {
            return;
        }
        var cookie = aListLocalService.getSetting("quark_cookie");
        log.debug("quark_cookie={}", cookie);
        saveCookie(DriverType.QUARK, cookie);
        cookie = aListLocalService.getSetting("uc_cookie");
        log.debug("uc_cookie={}", cookie);
        saveCookie(DriverType.UC, cookie);
        cookie = aListLocalService.getSetting("115_cookie");
        log.debug("115_cookie={}", cookie);
        saveCookie(DriverType.PAN115, cookie);
    }

    private void saveCookie(DriverType type, SettingResponse response) {
        if (response.getCode() == 200) {
            driverAccountRepository.findByTypeAndMasterTrue(type).ifPresent(account -> {
                account.setCookie(response.getData().getValue());
                driverAccountRepository.save(account);
            });
        }
    }
}
