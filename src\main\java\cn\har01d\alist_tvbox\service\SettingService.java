package cn.har01d.alist_tvbox.service;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import cn.har01d.alist_tvbox.config.AppProperties;
import cn.har01d.alist_tvbox.entity.Setting;
import cn.har01d.alist_tvbox.entity.SettingRepository;
import cn.har01d.alist_tvbox.util.Utils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.file.Paths;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.TimeUnit;
import java.lang.StringBuilder;

@Slf4j
@Service
public class SettingService {
    private final JdbcTemplate jdbcTemplate;
    private final Environment environment;
    private final AppProperties appProperties;
    private final TmdbService tmdbService;
    private final AListLocalService aListLocalService;
    private final SettingRepository settingRepository;
    @Value("${backup.directory:/data/backup}")
    private String backupDirectory;

    public SettingService(JdbcTemplate jdbcTemplate, Environment environment, AppProperties appProperties, TmdbService tmdbService, AListLocalService aListLocalService, SettingRepository settingRepository) {
        this.jdbcTemplate = jdbcTemplate;
        this.environment = environment;
        this.appProperties = appProperties;
        this.tmdbService = tmdbService;
        this.aListLocalService = aListLocalService;
        this.settingRepository = settingRepository;
    }

    @PostConstruct
    public void setup() {
        settingRepository.save(new Setting("install_mode", environment.getProperty("INSTALL", "xiaoya")));
        appProperties.setMerge(settingRepository.findById("merge_site_source").map(Setting::getValue).orElse("").equals("true"));
        appProperties.setHeartbeat(settingRepository.findById("bilibili_heartbeat").map(Setting::getValue).orElse("").equals("true"));
        appProperties.setSupportDash(settingRepository.findById("bilibili_dash").map(Setting::getValue).orElse("").equals("true"));
        appProperties.setReplaceAliToken(settingRepository.findById("replace_ali_token").map(Setting::getValue).orElse("").equals("true"));
        appProperties.setEnableHttps(settingRepository.findById("enable_https").map(Setting::getValue).orElse("").equals("true"));
        appProperties.setMix(!settingRepository.findById("mix_site_source").map(Setting::getValue).orElse("").equals("false"));
        appProperties.setSearchable(!settingRepository.findById("bilibili_searchable").map(Setting::getValue).orElse("").equals("false"));
        settingRepository.findById("debug_log").ifPresent(this::setLogLevel);
        
        // ==========1122初始化 delete_code_115，使用 cleanPassword 的值
        String cleanPasswordValue = settingRepository.findById("cleanPassword").map(Setting::getValue).orElse("");
        settingRepository.save(new Setting("delete_code_115", cleanPasswordValue));
    }

    public FileSystemResource exportDatabase() throws IOException {
        File out = backupDatabase();
        if (out == null) {
            throw new IOException("备份数据库失败");
        }
        return new FileSystemResource(out);
    }

    @Scheduled(cron = "0 0 12 * * *")
    public File backupDatabase() {
        // ===1112增加备份的表：emby\alias\PAN_ACCOUNT
        try {
            jdbcTemplate.execute("SCRIPT TO '/tmp/script.sql' TABLE ACCOUNT, ALIST_ALIAS, CONFIG_FILE, EMBY, ID_GENERATOR, INDEX_TEMPLATE, NAVIGATION, PAN_ACCOUNT, PIK_PAK_ACCOUNT, SETTING, SHARE, SITE, SUBSCRIPTION, TASK, USERS, TMDB, TMDB_META");
            File out = new File("/data/backup/database-" + LocalDate.now() + ".zip");
            out.createNewFile();
            try (FileOutputStream fos = new FileOutputStream(out);
                 ZipOutputStream zipOut = new ZipOutputStream(fos)) {
                File fileToZip = new File("/tmp/script.sql");
                Utils.zipFile(fileToZip, fileToZip.getName(), zipOut);
            }
            cleanBackups();
            return out;
        } catch (Exception e) {
            log.warn("backup database failed", e);
        }
        return null;
    }

    private void cleanBackups() {
        LocalDate day = LocalDate.now().minusDays(7);
        for (File file : Utils.listFiles("/data/backup", "zip")) {
            if (file.getName().startsWith("database-")) {
                try {
                    String name = file.getName().replace("database-", "").replace(".zip", "");
                    LocalDate date = LocalDate.parse(name);
                    if (date.isBefore(day)) {
                        file.delete();
                    }
                } catch (Exception e) {
                    log.warn("clean backup failed", e);
                }
            }
        }
    }

    public Map<String, String> findAll() {
        Map<String, String> map = settingRepository.findAll()
                .stream()
                .filter(e -> e.getName() != null && e.getValue() != null)
                .collect(Collectors.toMap(Setting::getName, Setting::getValue));
        map.remove("api_key");
        map.remove("bilibili_cookie");
        return map;
    }

    public Setting get(String name) {
        return settingRepository.findById(name).orElse(null);
    }

    public Setting update(Setting setting) {
        if ("merge_site_source".equals(setting.getName())) {
            appProperties.setMerge("true".equals(setting.getValue()));
        }
        if ("bilibili_heartbeat".equals(setting.getName())) {
            appProperties.setHeartbeat("true".equals(setting.getValue()));
        }
        if ("bilibili_searchable".equals(setting.getName())) {
            appProperties.setSearchable("true".equals(setting.getValue()));
        }
        if ("bilibili_dash".equals(setting.getName())) {
            appProperties.setSupportDash("true".equals(setting.getValue()));
        }
        if ("mix_site_source".equals(setting.getName())) {
            appProperties.setMix("true".equals(setting.getValue()));
        }
        if ("replace_ali_token".equals(setting.getName())) {
            appProperties.setReplaceAliToken("true".equals(setting.getValue()));
        }
        if ("enable_https".equals(setting.getName())) {
            appProperties.setEnableHttps("true".equals(setting.getValue()));
        }
        if ("tmdb_api_key".equals(setting.getName())) {
            tmdbService.setApiKey(setting.getValue());
        }
        if ("debug_log".equals(setting.getName())) {
            setLogLevel(setting);
        }
        if ("delete_delay_time".equals(setting.getName())) {
            aListLocalService.updateSetting("delete_delay_time", setting.getValue(), "number");
        }
        if ("ali_to_115".equals(setting.getName())) {
            aListLocalService.updateSetting("ali_to_115", setting.getValue(), "bool");
        }
        // ===1027 增加自动更新开关
        if ("auto_update".equals(setting.getName())) {
            aListLocalService.updateSetting("auto_update", setting.getValue(), "bool");
        }
        // ==========删除115我的接收
        if ("delete115Folder".equals(setting.getName())) {
            aListLocalService.updateSetting("delete115Folder", setting.getValue(), "bool");
        }
        // ==========更新115清理安全密码
        if ("cleanPassword".equals(setting.getName())) {
            aListLocalService.updateSetting("cleanPassword", setting.getValue(), "string");
            aListLocalService.updateSetting("delete_code_115", setting.getValue(), "string"); //==========1020 同步一份原作者的删除码设置
        }
        // ==========0927 c1 更新壁纸源和时间
        if ("wallpaper_source".equals(setting.getName())) {
            aListLocalService.updateSetting("wallpaper_source", setting.getValue(), "string");
        }
        if ("auto_update_interval".equals(setting.getName())) {
            aListLocalService.updateSetting("auto_update_interval", setting.getValue(), "number");
        }
        return settingRepository.save(setting);
    }

    private void setLogLevel(Setting setting) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        ch.qos.logback.classic.Logger logger = loggerContext.getLogger("cn.har01d");
        if ("true".equals(setting.getValue())) {
            log.info("enable debug log");
            logger.setLevel(Level.DEBUG);
        } else {
            log.info("disable debug log");
            logger.setLevel(Level.INFO);
        }
    }

    // ===增加一键恢复
    public ResponseEntity<List<String>> getBackupFiles() {
        File dir = new File(backupDirectory);
        if (!dir.exists() || !dir.isDirectory()) {
            log.error("Backup directory does not exist or is not a directory: {}", backupDirectory);
            return ResponseEntity.badRequest().body(null);
        }

        List<String> backupFiles = Arrays.stream(dir.listFiles((d, name) -> name.endsWith(".zip")))
                .map(File::getName)
                .collect(Collectors.toList());

        log.info("Found backup files: {}", backupFiles);
        return ResponseEntity.ok(backupFiles);
    }

    public ResponseEntity<?> restoreDatabase(RestoreRequest request) {
        String filename = request.getFilename();
        File sourceFile = new File(backupDirectory, filename);
        File destFile = new File("/data/database.zip");

        if (!sourceFile.exists()) {
            log.error("Backup file does not exist: {}", sourceFile.getPath());
            return ResponseEntity.badRequest().body("备份文件不存在");
        }

        try {
            Files.copy(sourceFile.toPath(), destFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            log.info("Copied backup file to: {}", destFile.getPath());
        } catch (IOException e) {
            log.error("Failed to copy backup file", e);
            return ResponseEntity.status(500).body("文件复制失败");
        }

        File dockerSocket = new File("/var/run/docker.sock");
        if (dockerSocket.exists()) {
            String containerName = getDockerContainerName();
            if (containerName != null) {
                log.info("Found Docker container: {}", containerName);
                // 提示用户即将执行重启操作
                log.info("即将执行重启操作，几分钟后检查是否恢复成功");
                
                // 使用 curl 命令重启容器
                String restartCommand = String.format("curl --unix-socket /var/run/docker.sock -X POST http://localhost/containers/%s/restart", containerName);
                try {
                    Process process = Runtime.getRuntime().exec(restartCommand);
                    process.waitFor();
                    log.info("Executed restart command: {}", restartCommand);
                } catch (IOException | InterruptedException e) {
                    log.error("Failed to restart container", e);
                    return ResponseEntity.status(500).body("容器重启失败");
                }
                
                return ResponseEntity.ok("操作完成，几分钟后检查是否恢复成功");
            } else {
                log.error("No matching Docker container found");
                return ResponseEntity.status(500).body("未找到匹配的容器");
            }
        } else {
            log.warn("Docker socket not found, manual restart required");
            return ResponseEntity.ok("已准备好，请手动重启 g-box 容器");
        }
    }

    private String getDockerContainerName() {
        try {
            // 使用ProcessBuilder替代Runtime.exec，添加超时参数，限制只查询基于特定镜像的容器
            ProcessBuilder pb = new ProcessBuilder(
                "curl", "-s", "--max-time", "5",
                "--unix-socket", "/var/run/docker.sock",
                "http://localhost/containers/json?filters=%7B%22ancestor%22%3A%5B%22ailg%2Fg-box%3Ahostmode%22%5D%7D"
            );
            Process process = pb.start();
            
            // 设置进程超时
            boolean completed = process.waitFor(5, TimeUnit.SECONDS);
            if (!completed) {
                process.destroyForcibly();
                log.error("Docker API查询超时");
                return "g-box"; // 默认返回值
            }
            
            // 使用缓冲读取替代一次性读取
            StringBuilder result = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    result.append(line);
                }
            }
            
            // 解析JSON
            String jsonStr = result.toString();
            if (jsonStr.isEmpty()) {
                log.warn("Docker API返回空结果");
                return "g-box"; // 默认返回值
            }
            
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonStr);
            if (rootNode.size() > 0) {
                String containerName = rootNode.get(0).get("Names").get(0).asText().substring(1);
                log.info("找到Docker容器: {}", containerName);
                return containerName;
            } else {
                log.warn("未找到基于ailg/g-box:hostmode镜像的容器");
            }
        } catch (IOException | InterruptedException e) {
            log.error("获取Docker容器名称失败", e);
        }
        
        // 出错时返回默认值
        return "g-box";
    }

    public static class RestoreRequest {
        private String filename;

        public String getFilename() {
            return filename;
        }

        public void setFilename(String filename) {
            this.filename = filename;
        }
    }

    public ResponseEntity<?> restartGBox() {
        String containerName = getDockerContainerName();
        if (containerName == null) {
            return ResponseEntity.status(500).body("未找到匹配的容器");
        }
        
        // 检查Docker socket是否存在
        File dockerSocket = new File("/var/run/docker.sock");
        if (dockerSocket.exists()) {
            // 获取自动更新设置
            String autoUpdateValue = settingRepository.findById("auto_update").map(Setting::getValue).orElse("false");
            if ("true".equals(autoUpdateValue)) {
                try {
                    // 获取本地镜像哈希值
                    Process localShaProcess = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                        "curl -s --unix-socket /var/run/docker.sock \"http://localhost/images/ailg/g-box:hostmode/json\" | jq -r '.RepoDigests[0]' | cut -f2 -d:"});
                    String localSha = readProcessOutput(localShaProcess);
                    localShaProcess.waitFor();
                    
                    // 如果本地哈希为空或为"null"，尝试从文件读取
                    if (localSha == null || localSha.trim().isEmpty() || "null".equals(localSha.trim())) {
                        File shaFile = new File("/data/ailg_sha.txt");
                        if (shaFile.exists()) {
                            Process readFileProcess = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                                "grep \"ailg/g-box:hostmode\" \"/data/ailg_sha.txt\" | awk '{print $2}'"});
                            localSha = readProcessOutput(readFileProcess);
                            readFileProcess.waitFor();
                        }
                    }
                    
                    // 获取远程哈希值
                    String remoteSha = null;
                    try {
                        URL url = new URL("https://ailg.ggbond.org/ailg_sha_remote.txt");
                        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                        connection.setRequestMethod("GET");
                        connection.setConnectTimeout(5000);
                        connection.setReadTimeout(5000);
                        
                        if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                                String line;
                                while ((line = reader.readLine()) != null) {
                                    if (line.contains("ailg/g-box:hostmode")) {
                                        String[] parts = line.split("\\s+");
                                        if (parts.length >= 2) {
                                            remoteSha = parts[1].trim();
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取远程哈希值失败", e);
                    }
                    log.info("remoteSha: {}", remoteSha);
                    log.info("localSha: {}", localSha);
                    
                    // 比较哈希值并设置更新标志
                    boolean needUpdate = localSha != null && !localSha.trim().isEmpty() && 
                                        remoteSha != null && !remoteSha.trim().isEmpty() && 
                                        !localSha.trim().equals(remoteSha.trim());
                    
                    // 确保目录存在
                    File dataDir = new File("/data");
                    if (!dataDir.exists()) {
                        dataDir.mkdirs();
                    }

                    // 写入两个文件用于验证
                    try {
                        Files.writeString(Paths.get("/data/need_update.txt"), needUpdate ? "true" : "false");
                    } catch (IOException e) {
                        log.error("写入更新标志文件失败", e);
                    }
                    
                } catch (Exception e) {
                    log.error("检查更新失败", e);
                }
            }
        }
        
        try {
            // 执行重启命令
            Process process = Runtime.getRuntime().exec(new String[]{"/usr/bin/init", "restart", containerName});
            process.waitFor();
            return ResponseEntity.ok("G-Box 重启操作已提交");
        } catch (Exception e) {
            log.error("重启失败", e);
            return ResponseEntity.status(500).body("G-Box 重启失败: " + e.getMessage());
        }
    }

    // 辅助方法：读取进程输出
    private String readProcessOutput(Process process) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            return reader.lines().collect(Collectors.joining("\n")).trim();
        }
    }
}
