package cn.har01d.alist_tvbox.web;

import cn.har01d.alist_tvbox.dto.AListLogin;
import cn.har01d.alist_tvbox.dto.AccountDto;
import cn.har01d.alist_tvbox.dto.CheckinLog;
import cn.har01d.alist_tvbox.dto.CheckinResult;
import cn.har01d.alist_tvbox.entity.Account;
import cn.har01d.alist_tvbox.entity.AccountRepository;
import cn.har01d.alist_tvbox.service.AccountService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import cn.har01d.alist_tvbox.model.Response;

import java.time.Instant;
import java.util.List;

@RestController
public class AccountController {
    private final AccountRepository accountRepository;
    private final AccountService accountService;

    public AccountController(AccountRepository accountRepository, AccountService accountService) {
        this.accountRepository = accountRepository;
        this.accountService = accountService;
    }

    @GetMapping("/api/ali/accounts")
    public List<Account> list() {
        return accountRepository.findAll();
    }

    @PostMapping("/api/ali/accounts")
    public Account create(@RequestBody AccountDto account) {
        return accountService.create(account);
    }

    @PostMapping("/api/ali/accounts/{id}/checkin")
    public CheckinResult checkin(@PathVariable Integer id, @RequestParam(required = false) boolean force) {
        return accountService.checkin(id, force);
    }

    @GetMapping("/api/ali/accounts/{id}/checkin")
    public List<CheckinLog> getCheckinLogs(@PathVariable Integer id) {
        return accountService.getCheckinLogs(id);
    }

    // @PostMapping("/api/ali/accounts/{id}")
    // public Account update(@PathVariable Integer id, @RequestBody AccountDto account) {
    //     return accountService.update(id, account);
    // }

    // 1225===尝试修改免重启更新token
    @PostMapping("/api/ali/accounts/{id}")
    public ResponseEntity<Account> update(@PathVariable Integer id, @RequestBody AccountDto account) {
        Account updatedAccount = accountService.update(id, account);

        // 检查是否需要更新存储
        Boolean needUpdateStorage = (Boolean) RequestContextHolder.currentRequestAttributes()
                .getAttribute("need_update_storage", RequestAttributes.SCOPE_REQUEST);

        if (Boolean.TRUE.equals(needUpdateStorage)) {
            return ResponseEntity.ok()
                    .header("need_update_storage", "true")
                    .body(updatedAccount);
        }

        return ResponseEntity.ok(updatedAccount);
    }

    // 1225===尝试修改免重启更新token
    // @PostMapping("/api/ali/accounts/re_init")
    // public ResponseEntity<Void> reInitAliyunStorage() {
    //     accountService.updateAliyunStorageAsync();
    //     return ResponseEntity.ok().build();
    // }

    @PostMapping("/api/ali/accounts/re_init")
    public ResponseEntity<Response<Void>> reInitAliyunStorage() {
        Response<Void> result = accountService.updateAliyunStorage();
        return ResponseEntity.ok(result);
    }

    @DeleteMapping("/api/ali/accounts/{id}")
    public void delete(@PathVariable Integer id) {
        accountService.delete(id);
    }

    @GetMapping("/ali/token/{id}")
    public String getAliToken(@PathVariable String id) {
        return accountService.getAliRefreshToken(id);
    }

    @PostMapping("/api/alist/login")
    public AListLogin updateAListLogin(@RequestBody AListLogin login) {
        return accountService.updateAListLogin(login);
    }

    @GetMapping("/api/alist/login")
    public AListLogin getAListLoginInfo() {
        return accountService.getAListLoginInfo();
    }

    @PostMapping("/api/schedule")
    public Instant updateScheduleTime(@RequestBody Instant time) {
        return accountService.updateScheduleTime(time);
    }
}
