package cn.har01d.alist_tvbox.web;

import cn.har01d.alist_tvbox.dto.IndexRequest;
import cn.har01d.alist_tvbox.dto.IndexResponse;
import cn.har01d.alist_tvbox.dto.VersionDto;
import cn.har01d.alist_tvbox.service.IndexService;
import cn.har01d.alist_tvbox.util.Utils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/index")
public class IndexController {
    private final IndexService indexService;

    public IndexController(IndexService indexService) {
        this.indexService = indexService;
    }

    @GetMapping("/version")
    public VersionDto getRemoteVersion() {
        return new VersionDto(Utils.trim(indexService.getRemoteVersion()));
    }

    @ResponseStatus(HttpStatus.ACCEPTED)
    @PostMapping
    public IndexResponse index(@RequestBody IndexRequest indexRequest) throws IOException {
        return indexService.index(indexRequest);
    }

    @PostMapping("/update-quark-files")
    public ResponseEntity<?> updateQuarkFiles() {
        indexService.updateQuarkFiles();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/update-quark-file-status")
    public ResponseEntity<?> updateQuarkFileStatus(@RequestBody Map<String, Boolean> request) {
        try {
            boolean enabled = request.getOrDefault("enabled", false);
            indexService.updateQuarkFileStatus(enabled);
            return ResponseEntity.ok().body("更新成功");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("更新失败: " + e.getMessage());
        }
    }

    @PostMapping("/stop-quark-update")
    public ResponseEntity<?> stopQuarkUpdate() {
        indexService.stopQuarkUpdate();
        return ResponseEntity.ok().build();
    }
}
