package cn.har01d.alist_tvbox.web;

// import cn.har01d.alist_tvbox.service.BiliBiliService;
// import cn.har01d.alist_tvbox.service.ParseService;
// import cn.har01d.alist_tvbox.service.SubscriptionService;
// import cn.har01d.alist_tvbox.service.TvBoxService;
import cn.har01d.alist_tvbox.service.*; //==========1020 sync 451
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse; //==========1020 sync 451
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.intellij.lang.annotations.Pattern;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.Map;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.regex.Matcher;


@Slf4j
@RestController
@RequestMapping
public class PlayController {
    private final TvBoxService tvBoxService;
    private final BiliBiliService biliBiliService;
    private final SubscriptionService subscriptionService;
    private final ProxyService proxyService;

    // public PlayController(TvBoxService tvBoxService, BiliBiliService biliBiliService, ParseService parseService, SubscriptionService subscriptionService) {
    //==========1020 sync 451
    public PlayController(TvBoxService tvBoxService,
        BiliBiliService biliBiliService,
        SubscriptionService subscriptionService,
        ProxyService proxyService) {
        this.tvBoxService = tvBoxService;
        this.biliBiliService = biliBiliService;
        this.subscriptionService = subscriptionService;
        this.proxyService = proxyService;
    }
    @GetMapping("/proxy/{id}")
    public void proxy(@PathVariable String id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        proxyService.proxy(id, request, response);
    }

    @GetMapping("/play")
    public Object play(Integer site, String path, String id, String bvid, String type, boolean dash, HttpServletRequest request) throws IOException {
        return play("", site, path, id, bvid, type, dash, request);
    }

    @GetMapping("/play/{token}")
    public Object play(@PathVariable String token, Integer site, String path, String id, String bvid, String type, boolean dash, HttpServletRequest request) throws IOException {
        subscriptionService.checkToken(token);

        String client = request.getHeader("X-CLIENT");
        // com.mygithub0.tvbox0.osdX 影视仓
        // com.fongmi.android.tv    影视
        // com.github.tvbox.osc     q版
        // com.github.tvbox.osc.bh  宝盒
        // com.github.tvbox.osc.tk  takagen99
        // com.qingsong.yingmi      影迷
        // com.github.tvbox.osc.tk 电视端tvbox
        // open 猫影视抓到的客户端是这个名字，不知道准不准
        // com.qingniu.oky OK影视Pro
        log.debug("get play url - site: {}  path: {}  id: {}  bvid: {}  type: ", site, path, id, bvid, type);

        if (StringUtils.isNotBlank(bvid)) {
            return biliBiliService.getPlayUrl(bvid, dash, client);
        }

        if (StringUtils.isNotBlank(id)) {
            String[] parts = id.split("\\~\\~\\~");
            if (parts.length > 1) {
                site = Integer.parseInt(parts[0]);
                path = parts[1];
            } else {
                path = id;
            }
        }

        boolean getSub = true;
        Map<String, Object> result;
        if (path.contains("/")) {
            if (path.startsWith("/")) {
                result = tvBoxService.getPlayUrl(site, path, getSub, client);
            } else {
                int index = path.indexOf('/');
                id = path.substring(0, index);
                path = path.substring(index);
                result = tvBoxService.getPlayUrl(site, Integer.parseInt(id), path, getSub, client);
            }
        } else if (path.contains("-")) {
            String[] parts = path.split("-");
            id = parts[0];
            int index = Integer.parseInt(parts[1]);
            result = tvBoxService.getPlayUrl(site, Integer.parseInt(id), index, getSub, client);
        } else {
            result = tvBoxService.getPlayUrl(site, Integer.parseInt(path), getSub, client);
        }

//        String url = (String) result.get("url");
//        if (url.contains("/redirect")) {
//            result.put("url", parseService.parse(url));
//        }
        // 使用 getRemoteAddr 方法获取客户端的 IP 地址
        String clientIp = getRemoteAddr(request);
        log.debug("==========Client IP: {}", clientIp);

        // 判断请求是否来自局域网
        if (clientIp != null && !(clientIp.startsWith("192.168") || clientIp.startsWith("10.") || clientIp.startsWith("127.") || isInternal172IP(clientIp))) {
            try {
                Path filePath = Paths.get("/data/alisturl.txt");
                if (Files.exists(filePath) && Files.size(filePath) > 0) {
                    String externalUrl = new String(Files.readAllBytes(filePath)).trim();
                    if (!externalUrl.isEmpty()) {
                        String originalUrl = (String) result.get("url");
                        // log.debug("==========Original URL before modification: {}", originalUrl);
                        java.net.URL originalUrlObj = new java.net.URL(originalUrl);
                        String originalHost = originalUrlObj.getHost();
                        
                        if (originalHost.startsWith("192.168") || originalHost.startsWith("10.") || originalHost.startsWith("127.") || isInternal172IP(originalHost)) {
                            java.net.URL newUrl = new java.net.URL(externalUrl + originalUrlObj.getFile());
                            // log.debug("==========PlayController.java PlayController() newUrl: {}", newUrl);
                            result.put("url", newUrl.toString());
                        } 
                    } else {
                        log.warn("External URL file is empty");
                    }
                } else {
                    log.warn("External URL file does not exist or is empty");
                }
            } catch (IOException e) {
                log.error("Failed to read external URL from file", e);
            }
        }

        return result;
    }
    // 从 LogFilter 类中复制的 getRemoteAddr 方法
    private String getRemoteAddr(HttpServletRequest req) {
        if (!StringUtils.isEmpty(req.getHeader("X-Real-IP"))) {
            return req.getHeader("X-Real-IP");
        }
        if (!StringUtils.isEmpty(req.getHeader("X-FORWARDED-FOR"))) {
            return req.getHeader("X-FORWARDED-FOR");
        }
        return req.getRemoteAddr();
    }

    // 判断是否是局域网的 172.x.x.x IP 地址
    private boolean isInternal172IP(String ip) {
        @Pattern(value = "^172\\.(1[6-9]|2[0-9]|3[0-1])\\..*")
        Matcher matcher = java.util.regex.Pattern.compile("^172\\.(1[6-9]|2[0-9]|3[0-1])\\..*").matcher(ip);
        return matcher.matches();
    }
}
