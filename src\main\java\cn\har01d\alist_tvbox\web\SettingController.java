package cn.har01d.alist_tvbox.web;

import cn.har01d.alist_tvbox.entity.Setting;
import cn.har01d.alist_tvbox.service.SettingService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDate;
import java.util.Map;

import java.util.List;
import cn.har01d.alist_tvbox.service.SettingService.RestoreRequest;
import java.io.File;
import cn.har01d.alist_tvbox.util.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/settings")
public class SettingController {
    private final SettingService service;
    private static final Logger log = LoggerFactory.getLogger(SettingController.class);

    public SettingController(SettingService service) {
        this.service = service;
    }

    @GetMapping
    public Map<String, String> findAll() {
        return service.findAll();
    }

    @GetMapping("/{name}")
    public Setting get(@PathVariable String name) {
        return service.get(name);
    }

    @PostMapping
    public Setting update(@RequestBody Setting setting) {
        // 如果是自动更新设置,同步到 AList 数据库
        if ("auto_quark_update".equals(setting.getName())) {
            try {
                String sql = String.format(
                    "INSERT OR REPLACE INTO x_setting_items (key, value, type, flag) VALUES ('%s', '%s', 'bool', 1)",
                    setting.getName(),
                    setting.getValue()
                );
                Utils.executeUpdate(sql);
            } catch (Exception e) {
                log.error("Failed to sync setting to AList DB", e);
            }
        }
        return service.update(setting);
    }

    @GetMapping("/export")
    public FileSystemResource exportDatabase(HttpServletResponse response) throws IOException {
        response.addHeader("Content-Disposition", "attachment; filename=\"database-" + LocalDate.now() + ".zip\"");
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        return service.exportDatabase();
    }

    // ===增加一键还原
    @GetMapping("/backup-files")
    public ResponseEntity<List<String>> getBackupFiles() {
        return service.getBackupFiles();
    }

    @PostMapping("/restore")
    public ResponseEntity<?> restoreDatabase(@RequestBody RestoreRequest request) {
        return service.restoreDatabase(request);
    }

    @GetMapping("/check-docker-socket")
    public boolean checkDockerSocket() {
        File dockerSocket = new File("/var/run/docker.sock");
        return dockerSocket.exists();
    }

    @PostMapping("/restart-gbox")
    public ResponseEntity<?> restartGBox() {
        return service.restartGBox();
    }
}
