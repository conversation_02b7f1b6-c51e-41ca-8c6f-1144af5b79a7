package cn.har01d.alist_tvbox.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.logging.Logger;
import java.util.Random;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.har01d.alist_tvbox.service.SettingService;
import jakarta.annotation.PostConstruct;

import java.util.Collections;
import cn.har01d.alist_tvbox.entity.Setting;



@RestController
@RequestMapping("/api")
public class SpUrlController {

    private static final Logger logger = Logger.getLogger(SpUrlController.class.getName());
    private Timer autoUpdateTimer; // 在类中声明 autoUpdateTimer 变量

    @Autowired
    private SettingService settingService;

    // ==========0929 c1 增加自启动更新壁纸
    @Autowired
    @PostConstruct
    public void init() {
        try {
            String status = getStatusFromSunPanel();
            if ("open".equals(status)) {
                loadSettingsAndStartAutoUpdate();
            } else {
                logger.info("状态为 " + status + "，不执行自动更新任务");
            }
        } catch (IOException e) {
            logger.severe("读取状态时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private String getStatusFromSunPanel() throws IOException {
        String sunPanelFilePath = "/data/sun-panel.txt";
        if (Files.exists(Paths.get(sunPanelFilePath))) {
            return new String(Files.readAllBytes(Paths.get(sunPanelFilePath))).trim();
        }
        return "open"; // 默认值为 "open"
    }

    private void loadSettingsAndStartAutoUpdate() {
        try {
            // 从 SettingService 中加载设置
            Setting wallpaperSourceSetting = settingService.get("wallpaper_source");
            Setting autoUpdateIntervalSetting = settingService.get("auto_update_interval");

            String source = wallpaperSourceSetting != null ? wallpaperSourceSetting.getValue() : "bing"; // 默认值为 "bing"
            int interval = autoUpdateIntervalSetting != null ? Integer.parseInt(autoUpdateIntervalSetting.getValue()) : 0; // 默认值为0分钟，即禁用

            if (interval > 0) {
                autoUpdateTimer = new Timer();
                autoUpdateTimer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        saveImage(Collections.singletonMap("source", source));
                    }
                }, 0, interval * 60 * 1000); // 转换为毫秒
                logger.info("自动更新已设置，每 " + interval + " 分钟更新一次");
            } else {
                logger.info("自动更新已关闭");
            }
        } catch (Exception e) {
            logger.severe("初始化自动更新任务时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @GetMapping("/get-sp-url")
    public Map<String, String> getSpUrl() throws IOException {
        String sunPanelFilePath = "/data/sun-panel.txt";
        String alistUrlFilePath = "/data/alisturl.txt";
        String urlFilePath = "/data/sunpanelurl.txt";
        Map<String, String> response = new HashMap<>();

        String status = "open";
        String url = "";

        if (Files.exists(Paths.get(sunPanelFilePath))) {
            String sunPanelContent = new String(Files.readAllBytes(Paths.get(sunPanelFilePath))).trim();
            // ==========0928 c1 增加一个条件恢复小雅首页
            if ("close".equals(sunPanelContent) || "uninstall".equals(sunPanelContent)) {
                status = "close";
                if (Files.exists(Paths.get(alistUrlFilePath))) {
                    String alistUrlContent = new String(Files.readAllBytes(Paths.get(alistUrlFilePath))).trim();
                    url = alistUrlContent.split("\n")[0];
                }
            } else if ("open".equals(sunPanelContent) && Files.exists(Paths.get(urlFilePath))) {
                String urlContent = new String(Files.readAllBytes(Paths.get(urlFilePath))).trim();
                url = urlContent.split("\n")[0];
            }
        } else if (Files.exists(Paths.get(urlFilePath))) {
            String urlContent = new String(Files.readAllBytes(Paths.get(urlFilePath))).trim();
            url = urlContent.split("\n")[0];
        }

        response.put("status", status);
        response.put("url", url);

        return response;
    }

    @PostMapping("/save-image")
    public String saveImage(@RequestBody Map<String, String> request) {
        String source = request.getOrDefault("source", "bing");
        // logger.info("==========0926 c5 source: " + source);
        // logger.info("收到保存图片的请求");

        String imageUrl = getImageUrl(source);
        String uploadDir = "/data/conf/uploads/";
        String fileName = "bg.png";
        File uploadFile = new File(uploadDir + fileName);

        try (InputStream in = new URL(imageUrl).openStream();
            FileOutputStream out = new FileOutputStream(uploadFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            logger.info("文件成功保存到: " + uploadFile.getAbsolutePath());

            // 更新数据库中的 backgroundImageSrc 字段
            updateDatabaseBackgroundImageSrc("/uploads/bg.png");

            return "图片保存成功";
        } catch (IOException e) {
            // logger.severe("保存图片时出错: " + e.getMessage());
            e.printStackTrace();
            return "保存图片时出错";
        }
    }

    private void updateDatabaseBackgroundImageSrc(String newSrc) {
        String command = String.format("sqlite3 /data/conf/database/database.db \"UPDATE user_config SET panel_json = json_set(panel_json, '$.backgroundImageSrc', '%s') WHERE user_id = 1;\"", newSrc);
        try {
            Process process = Runtime.getRuntime().exec(new String[] { "sh", "-c", command });
            int exitCode = process.waitFor();
            // if (exitCode == 0) {
            //     logger.info("数据库中的 backgroundImageSrc 字段已更新为: " + newSrc);
            // } else {
            //     logger.severe("更新数据库时出错，退出代码: " + exitCode);
            // }
        } catch (IOException | InterruptedException e) {
            logger.severe("更新数据库时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @PostMapping("/set-auto-update")
    public Map<String, String> setAutoUpdate(@RequestBody Map<String, Object> request) {
        int interval = (int) request.getOrDefault("interval", 60);
        String source = (String) request.getOrDefault("source", "bing");

        // logger.info(String.format("==========0926 c6 Received parameters - interval: %d, source: %s", interval, source));
        
            Map<String, String> response = new HashMap<>();
        if (autoUpdateTimer != null) {
            autoUpdateTimer.cancel();
        }

        if (interval > 0) {
            autoUpdateTimer = new Timer();
            autoUpdateTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    saveImage(Collections.singletonMap("source", source)); // 使用前端传递的壁纸源
        }
            }, 0, interval * 60 * 1000); // 转换为毫秒
            String message = "自动更新已设置，每 " + interval + " 分钟更新一次";
            // logger.info(message);
            response.put("message", message);
        } else {
            String message = "自动更新已关闭";
            // logger.info(message);
            response.put("message", message);
        }
        return response;
    }

    private String getImageUrl(String source) {
        Random random = new Random();
        int randomNum = random.nextInt(8); // 随机获取0到7之间的整数

        switch (source) {
            case "bing":
                return "https://bing.biturl.top/?resolution=3840&format=image&index=" + randomNum + "&mkt=zh-CN";
            case "anime":
                return getWallhavenImageUrl();
            default:
                return "https://bing.biturl.top/?resolution=3840&format=image&index=" + randomNum + "&mkt=zh-CN";
        }
    }

    private String getWallhavenImageUrl() {
        try {
            String apiUrl = "https://wallhaven.cc/api/v1/search?resolutions=3840x2160&sorting=toplist&categories=010";
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(new URL(apiUrl));
            JsonNode data = root.path("data");
            if (data.isArray() && data.size() > 0) {
                Random random = new Random();
                int randomIndex = random.nextInt(data.size());
                JsonNode selectedImage = data.get(randomIndex);
                return selectedImage.path("path").asText();
            }
        } catch (IOException e) {
            // logger.severe("获取Wallhaven图片时出错: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    // ===1122增加卸载sp的api
    @PostMapping("/uninstall-sunpanel")
    public ResponseEntity<Map<String, String>> uninstallSunPanel() {
        try {
            Files.write(Paths.get("/data/sun-panel.txt"), "uninstall".getBytes());
            Map<String, String> response = new HashMap<>();
            response.put("message", "已卸载sun-panel，重启G-Box容器后生效");
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            logger.severe("卸载sun-panel时出错: " + e.getMessage());
            return ResponseEntity.status(500).body(Collections.singletonMap("error", "操作失败"));
        }
    }

    @PostMapping("/restore-sunpanel")
    public ResponseEntity<Map<String, String>> restoreSunPanel() {
        try {
            Files.write(Paths.get("/data/sun-panel.txt"), "open".getBytes());
            Map<String, String> response = new HashMap<>();
            response.put("message", "已恢复sun-panel，重启G-Box容器后生效");
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            logger.severe("恢复sun-panel时出错: " + e.getMessage());
            return ResponseEntity.status(500).body(Collections.singletonMap("error", "操作失败"));
        }
    }
}
