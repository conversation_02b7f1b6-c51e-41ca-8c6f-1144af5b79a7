package cn.har01d.alist_tvbox.web;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

@RestController
@RequestMapping("/api")
public class UrlUpdateController {

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/update-url")
    public void updateUrl(@RequestBody UrlUpdateRequest request) throws IOException {
        Files.write(Paths.get(request.getFile()), request.getUrl().getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
    }

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/run-script")
    public void runScript(@RequestBody ScriptRequest request) throws IOException {
        ProcessBuilder processBuilder = new ProcessBuilder("sh", request.getScriptPath());
        processBuilder.start();
    }

    public static class UrlUpdateRequest {
        private String url;
        private String file;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getFile() {
            return file;
        }

        public void setFile(String file) {
            this.file = file;
        }
    }

    public static class ScriptRequest {
        private String scriptPath;

        public String getScriptPath() {
            return scriptPath;
        }

        public void setScriptPath(String scriptPath) {
            this.scriptPath = scriptPath;
        }
    }
}
