[{"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchPgcResult$Video", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliQrCodeResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliV2Info$Subtitle", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.CookieData", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliV2Info$Owner", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliVideoInfo$Video", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.SubtitleDataResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyItem$ImageTags", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.tvbox.Site", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchInfo$SearchList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.ChannelArchives", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliChannelItem", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSeasonInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliListResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliTokenResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsListResponseV2", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.UserResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliCategory", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchPgcResult", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.ShareInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliChannelResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliRelatedResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AliFileList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliV2Info", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.CheckinResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.CheckinResult", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.SearchListResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliPlayResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.AliToken", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayInfo$PlayUrl", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.domain.TaskStatus", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliHotResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.OpenApiDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuRoomList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyMediaSources", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AliBatchResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.TmdbDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.UpInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliFavItemsResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliPlay$DUrl", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyInfo$SessionInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSeasonInfoList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.RewardResult", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.tg.SearchRequest", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.tg.Message", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyItem", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.IndexRequest", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliFavListResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsRequest", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuRoom", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaLiveRoom$LiveBitRateInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchResult$Video", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliHistoryResult$Cursor", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.tg.Chat", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliInfo$PageInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliPlay", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AliRequest", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliLoginResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliFeed", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliCategories", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliV2Info$SubtitleList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.FavItem$CntInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliInfo$Owner", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliHistoryResult$History", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.Filter", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.ShareInfoResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.MovieDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FilterValue", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.Segment", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.Media", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.Subtitle", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.NavigationList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.AListUser", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.domain.TaskResult", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliChannelListResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsResponseV2", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuLiveStream", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuLiveStream$CdnsWithName", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuRoomResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliChannel", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayInfo$QnDesc", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayInfo$Stream", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AliResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyItems", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsInfoV2", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliToken", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchInfoResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.tvbox.MovieList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuRoomsResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.VideoPreviewResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.VersionDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.MetaDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliHistoryResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcCategory", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.IdName", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.FavFolder", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliHot", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSeasonInfo$Stats", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcCategoryInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaLiveRoom$BitRateInfoList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliVideoInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsDetail", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.ChannelList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsListResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayInfo$Codec", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcCategoryResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AliBatchRequest", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.NavigationDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliPlay$Dash$Video", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliHistoryResult", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.IndexTemplateDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.Versions", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaLiveRoomInfoList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayInfo$UrlInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliFeedResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.QrCodeResult", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.Response", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyMediaSources$MediaSources", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliList$Page", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliVideoInfoResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.domain.Cache", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.FilterDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.DoubanDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.SearchResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaLiveRoomInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchInfo$Page", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.StorageInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.CatAudio", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.AliTokensResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchInfo$Video", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.LoginResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsDetailResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.DoubanData", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.ParseResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.SearchRequest", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.Data", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.FavItems", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliHistoryResult$Video", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcCategoryList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.VideoPreview", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliCategoryList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AccountDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.FavItem", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AListAliasDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaSearchResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliInfo$Stats", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliV2Info$Stats", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSeasonResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.SearchResult", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AListLogin", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliV2InfoResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.Sub", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchResult", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.VideoPreviewPlayInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.tvbox.CategoryList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliV2Info$PageInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.GenerateRequest", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.tvbox.MovieDetail", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.ChannelArchive", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliFollowingsResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyInfo$User", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.domain.SystemInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.SettingResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.TokenDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuCategoryList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcPlayInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaCategoryList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.LiveTranscodingSubtitle", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.LoginToken", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayInfo$PlayInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.SubtitleData", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcRoomsResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.SiteDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuCategory", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomsResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyMediaSources$MediaStreams", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.tvbox.IndexContext$Stats", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcRoomList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.QrCode", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuStreamResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuRoomInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.FileItem", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.Setting", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.TmdbCredits", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.RewardResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.TmdbList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.domain.DriverType", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaCategory", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.AliFileItem", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSeasonInfoList$Section", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.tvbox.IndexContext", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomPlayInfo$Format", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.Page", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.FileDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.SharesDto", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FileNameInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaLiveRoom", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.Resp", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.LoginRequest", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.HuyaLiveRoomInfoListResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliRoomList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.FavList", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.CcRoomInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.Dash", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.domain.TaskType", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.ShareInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliPlay$Dash$Audio", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.LiveTranscoding", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.model.FsInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.IndexResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuCategoryResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliSearchPgcResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliFollowings", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.BilibiliCategoriesResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.CheckinLog", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliPlay$Dash", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.emby.EmbyInfo", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.dto.bili.BiliBiliInfoResponse", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.tvbox.Category", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "cn.har01d.alist_tvbox.live.model.DouyuLiveStream$BitRate", "allDeclaredMethods": true, "allDeclaredFields": true}, {"methods": [{"parameterTypes": [], "name": "<init>"}], "name": "java.util.HashSet"}, {"methods": [{"parameterTypes": [], "name": "<init>"}], "name": "java.util.ArrayList"}, {"methods": [{"parameterTypes": [], "name": "<init>"}], "name": "java.util.HashMap"}, {"allDeclaredConstructors": true, "name": "com.github.benmanes.caffeine.cache.SSMS", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "com.github.benmanes.caffeine.cache.SSMSA", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "com.github.benmanes.caffeine.cache.SSSW", "allDeclaredMethods": true, "allDeclaredFields": true}, {"allDeclaredConstructors": true, "name": "com.github.benmanes.caffeine.cache.PSAMS", "allDeclaredMethods": true, "allDeclaredFields": true}]