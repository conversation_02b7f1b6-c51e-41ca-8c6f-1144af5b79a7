spring:
  datasource:
    url: jdbc:h2:file:/data/atv
    username: sa
    password: password
    driverClassName: org.h2.Driver
  sql:
    init:
      data-locations: file:/data/atv/data.sql
  banner:
    location: classpath:banner.txt
    charset: UTF-8

logging:
  file:
    name: log/app.log
  level:
    cn.har01d: info
app:
  xiaoya: true
  sites:
    - name: 丫仙女
      url: http://localhost
      version: 3
      searchable: true
      xiaoya: true
