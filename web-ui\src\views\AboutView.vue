<template>
  <div class="about">
    <div class="container">
      <h1>欢迎使用G-Box</h1>
      <p>
        G-Box的安装使用说明测试：<br>
        在哪个设备安装，就用SSH工具登陆它的命令控制台，执行一键脚本，在主菜单选择5，根据引导完成安装；<br>
        <div class="code-container">
          <code id="script1">bash -c "$(curl -sSLf https://ailg.ggbond.org/xy_install.sh)"</code>
          <span class="copy-icon" @click="copyToClipboard('script1')">📋</span>
          <span class="copy-notification" id="copy-notification1">已复制</span>
        </div>
        备用脚本：<br>
        <div class="code-container">
          <code id="script2">bash -c "$(curl -sSLf https://gbox.ggbond.org/xy_install.sh)"</code>
          <span class="copy-icon" @click="copyToClipboard('script2')">📋</span>
          <span class="copy-notification" id="copy-notification2">已复制</span>
        </div>
        <div class="code-container">
          <code id="script3">bash -c "$(curl -sSLf https://xy.ggbond.org/xy/xy_install.sh)"</code>
          <span class="copy-icon" @click="copyToClipboard('script3')">📋</span>
          <span class="copy-notification" id="copy-notification3">已复制</span>
        </div>
        <h2>内置Sun-Panel导航</h2>
        初始用户名：ailg666    密码：12345678<br>
        注：如果您不需要内置导航页功能，可以运行一键脚本，选O（字母），再选1，根据菜单引导进行卸载。<br>
        <h2>G-Box更新方法：</h2>
        可以在一键脚本的O（字母）选项中配置自动更新计划，也可以运行下面的命令马上更新（注：该更新方式不同于直接重装，可以保留用户数据和宿主机挂载的路径！）<br>
        <div class="code-container">
          <code id="script4">bash -c "$(curl -sSLf https://ailg.ggbond.org/xy_install.sh)" -s g-box</code>
          <span class="copy-icon" @click="copyToClipboard('script4')">📋</span>
          <span class="copy-notification" id="copy-notification4">已复制</span>
        </div>
      </p>
      
      <h2>G-Box详细视频教程</h2>
      <div class="card-container">
        <div class="card">
          <img src="https://img.youtube.com/vi/hYwCxJqChUw/0.jpg" alt="G-box 视频教程">
          <div class="card-content">
            <h3>G-Box保镖级教程</h3>
            <a href="https://youtu.be/hYwCxJqChUw?si=YZJsw_Aqt10VRNqS" target="_blank" class="button">Youtube观看</a>
            <a href="#" id="aliyun-link" target="_blank" class="button">阿里云盘观看</a>
            
          </div>
        </div>

        <div class="card">
          <img src="https://pic.imgdb.cn/item/66e0547ad9c307b7e968f395.png" alt="B站 视频教程">
          <div class="card-content">
            <h3>G-Box解决115风控（含速装Emby教程）</h3>
            <a href="https://b23.tv/ewhi6pF" target="_blank" class="button">B站观看</a>
          </div>
        </div>
      </div>

      <p>
        <strong style="font-size: larger;">问题/玩法/心得</strong>……可加TG群求助或交流：
        <a href="https://t.me/ailg666" target="_blank" class="button">加入TG群</a>
      </p>

      <div class="image-container">
        <p>如果您喜欢G-box，可以请老G喝杯咖啡，感谢您的支持！</p>
        <a href="https://smms.app/image/I2sicMHx31DKUdg" target="_blank">
          <img src="https://s2.loli.net/2024/08/08/I2sicMHx31DKUdg.jpg" alt="老G的赞赏码" />
        </a>
      </div>

      <p>致谢：本应用是基于alist-tvbox项目定制而来，非常感谢原作者haroldli大佬！</p>
      <p>
        alist-tvbox项目源代码：
        <a href="https://github.com/power721/alist-tvbox" target="_blank">https://github.com/power721/alist-tvbox</a>
      </p>
      <p>
        中文文档：<a href="https://har01d.cn/#/notes/alist-tvbox" target="_blank">https://har01d.cn/#/notes/alist-tvbox</a>
      </p>
      <p>
        <a href="https://github.com/power721/alist-tvbox/blob/master/doc/README_zh.md" target="_blank">https://github.com/power721/alist-tvbox/blob/master/doc/README_zh.md</a>
      </p>
      <p>
        AList：
        <a href="https://alist.nn.ci/zh/guide/" target="_blank">https://alist.nn.ci/</a>
      </p>
      <p>
        Docker：
        <a href="https://hub.docker.com/r/haroldli/xiaoya-tvbox" target="_blank">https://hub.docker.com/r/haroldli/xiaoya-tvbox</a>
      </p>
      <p>
        Telegram：
        <a href="https://t.me/alist_tvbox_group" target="_blank">https://t.me/alist_tvbox_group</a>
      </p>
      <p>
        使用方式：<br>
        <div class="code-container">
          <code id="script1">docker run -d -p 4567:4567 -p 5344:80 -e ALIST_PORT=5344 -v /etc/xiaoya:/data --restart=always --name=xiaoya-tvbox haroldli/xiaoya-tvbox</code>
          <span class="copy-icon" @click="copyToClipboard('script1')">📋</span>
          <span class="copy-notification" id="copy-notification1">已复制</span>
        </div>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
function copyToClipboard(elementId: string) {
  const element = document.getElementById(elementId);
  if (element) {
    const temp = document.createElement("textarea");
    document.body.appendChild(temp);
    temp.value = element.textContent || "";
    temp.select();
    document.execCommand("copy");
    document.body.removeChild(temp);
    showNotification(elementId);
  }
}

function showNotification(elementId: string) {
  const notification = document.getElementById(`copy-notification${elementId.slice(-1)}`);
  if (notification) {
    (notification as HTMLElement).style.display = 'inline';
    setTimeout(() => {
      (notification as HTMLElement).style.display = 'none';
    }, 1000);
  }
}

onMounted(() => {
    var host = window.location.hostname;
    var aliyunLink = document.getElementById("aliyun-link") as HTMLAnchorElement;
    if (aliyunLink) {
        if (/^(192\.168|10)\./.test(host)) {
            aliyunLink.href = "http://" + host + ":5678/ailg_jf/g-box保镖教程.mp4";
        } else {
            aliyunLink.href = "https://www.alipan.com/s/KEvj3Em71vU";
        }
        console.log(aliyunLink.href); // 打印链接
    } else {
        console.error("aliyunLink element not found");
    }
});

</script>

<style scoped>
.about {
  background: url('https://w.wallhaven.cc/full/ne/wallhaven-new8wk.jpg') no-repeat center center fixed;
  background-size: cover;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.container {
  max-width: 900px;
  margin: auto;
  background: #1e1e1e;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}
h1, h2 {
  text-align: center;
  color: #ffffff;
}
p {
  margin: 10px 0;
  color: #b0b0b0;
}
.code-container {
  position: relative;
  background: #333;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}
code {
  background: none;
  padding: 0;
  border-radius: 0;
  color: #e0e0e0;
}
.copy-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  color: #3498db;
}
.copy-icon:hover {
  color: #2980b9;
}
.copy-notification {
  position: absolute;
  top: -20px;
  right: 10px;
  background: #3498db;
  color: #fff;
  padding: 5px;
  border-radius: 4px;
  display: none;
}
a {
  color: #3498db;
  text-decoration: none;
}
a:hover {
  text-decoration: underline;
}
.button {
  display: inline-block;
  padding: 10px 20px;
  margin: 10px 0;
  background-color: #3498db;
  color: #fff;
  border-radius: 5px;
  text-decoration: none;
}
.button:hover {
  background-color: #2980b9;
}
.image-container {
  margin: 20px 0;
  text-align: center;
}
img {
  max-width: 200px;
  height: auto;
  border-radius: 8px;
}
.card-container {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
}
.card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  margin: 0 10px;
  background: #2c2c2c;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}
.card img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}
.card-content {
  margin-top: 10px;
  text-align: center;
}
.card-content h3 {
  margin: 10px 0;
}
</style>
