<template>
  <div class="list">
    <h1>阿里账号列表</h1>
    <el-row justify="end">
      <el-button @click="load">刷新</el-button>
      <el-button type="primary" @click="handleAdd">添加</el-button>
    </el-row>
    <div class="space"></div>

    <el-table :data="accounts" border style="width: 100%">
<!--      <el-table-column prop="id" label="ID" sortable width="70"/>-->
      <el-table-column prop="nickname" label="昵称" sortable width="180"/>
      <el-table-column prop="autoCheckin" label="自动签到" width="90">
        <template #default="scope">
          <el-icon v-if="scope.row.autoCheckin">
            <Check/>
          </el-icon>
          <el-icon v-else>
            <Close/>
          </el-icon>
        </template>
      </el-table-column>
      <el-table-column prop="checkinDays" label="签到次数" width="90">
        <template #default="scope">
          {{ scope.row.checkinDays }}
          <span class="divider"></span>
          <el-button link @click="loadTimeline(scope.row.id)">
            <el-icon><Calendar /></el-icon>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="checkinTime" label="上次签到时间">
        <template #default="scope">
          {{ formatTime(scope.row.checkinTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="showMyAli" label="加载我的云盘？" width="150">
        <template #default="scope">
          <el-icon v-if="scope.row.showMyAli">
            <Check/>
          </el-icon>
          <el-icon v-else>
            <Close/>
          </el-icon>
        </template>
      </el-table-column>
      <el-table-column prop="master" label="主账号？" width="120">
        <template #default="scope">
          <el-icon v-if="scope.row.master">
            <Check/>
          </el-icon>
          <el-icon v-else>
            <Close/>
          </el-icon>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="showDetails(scope.row)">详情</el-button>
          <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="formVisible" :title="dialogTitle" width="60%">
      <el-form :model="form">
        <el-form-item label="阿里refresh token" label-width="150" required>
          <el-input v-model="form.refreshToken" maxlength="128" placeholder="长度32位" autocomplete="off"/>
          <!-- <a href="https://alist.nn.ci/zh/guide/drivers/aliyundrive.html" target="_blank">获取阿里token</a> -->
          <!-- ===1106 修改阿里token获取 -->
          <a @click="openAliQRCodeModal" style="cursor: pointer; color: blue; text-decoration: underline;">获取阿里token</a>
          <a @click="openQuickTokenModal" style="cursor: pointer; color: green; text-decoration: underline; margin-left: 10px;">快速获取token</a>
          <a href="https://aliyuntoken.vercel.app/" class="hint" target="_blank">获取阿里token</a>
          <a @click="openAliCloudV2Modal" style="cursor: pointer; color: #409EFF; text-decoration: underline; margin-left: 10px;">扫码获取V2</a>
        </el-form-item>
        <el-form-item label="开放refresh token" label-width="140" required>
          <el-input v-model="form.openToken" type="textarea" rows="3" minlength="256" placeholder="长度280位"
                    autocomplete="off"/>
            <!-- AList:<a @click="openAListQRCodeModal" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a> -->
            <!-- xhofe:<a @click="openAListQRCodeModal1" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a> -->
            nn_ci:<a @click="openAListQRCodeModal" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a>
          <div class="hint">
            webdav:<a href="https://messense-aliyundrive-webdav-backendrefresh-token-ucs0wn.streamlit.app/" title="需要选择webdav的认证URL" target="_blank">获取开放token</a>
          </div>
          <!-- ===1104 修改tv token -->
          <div class="hint">
            TV Token(自认证):<a @click="openQRCodeModal" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a>
          </div>
          <div class="hint">
            xhofe:<a @click="openAListQRCodeModal1" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a>
          </div>
        </el-form-item>
        <el-form-item label="加载我的云盘" label-width="140" v-if="form.openToken">
          <el-switch
            v-model="form.showMyAli"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>
        <el-form-item label="主账号" label-width="140">
          <el-switch
            v-model="form.master"
            inline-prompt
            active-text="是"
            inactive-text="否"
          />
          <span class="hint">主账号用来观看分享。</span>
        </el-form-item>
        <el-form-item label="自动签到" label-width="140">
          <el-switch
            v-model="form.autoCheckin"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>
      </el-form>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">{{ updateAction ? '更新' : '添加' }}</el-button>
      </span>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible" title="删除阿里账号" width="30%">
      <p>是否删除阿里账号 - {{ form.id }}</p>
      <p>{{ form.nickname }}</p>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="danger" @click="deleteAccount">删除</el-button>
      </span>
      </template>
    </el-dialog>

    <el-dialog v-model="detailVisible" title="阿里账号详情" width="60%">
      <el-form :model="form" label-width="150px">
        <el-form-item v-if="form.accessToken" prop="accessToken" label="阿里access token">
          <el-input v-model="form.accessToken" maxlength="128" readonly/>
          <span class="hint">创建时间： {{ formatTime(iat[0]) }}</span>
          <span class="hint">更新时间： {{ formatTime(form.accessTokenTime) }}</span>
          <span class="hint">过期时间： {{ formatTime(exp[0]) }}</span>
        </el-form-item>
        <el-form-item v-if="form.accessTokenOpen" prop="accessTokenOpen" label="开放access token">
          <el-input v-model="form.accessTokenOpen" maxlength="128" readonly/>
          <span class="hint">创建时间： {{ formatTime(iat[1]) }}</span>
          <span class="hint">更新时间： {{ formatTime(form.accessTokenOpenTime) }}</span>
          <span class="hint">过期时间： {{ formatTime(exp[1]) }}</span>
        </el-form-item>
        <el-form-item prop="refreshToken" label="阿里refresh token" required>
          <el-input v-model="form.refreshToken" maxlength="128" placeholder="长度32位"/>
          <!-- <a href="https://alist.nn.ci/zh/guide/drivers/aliyundrive.html" target="_blank">获取阿里token</a> -->
          <!-- ===1106 修改阿里token获取 -->
          <a @click="openAliQRCodeModal" style="cursor: pointer; color: blue; text-decoration: underline;">获取阿里token</a>
          <a @click="openQuickTokenModal" style="cursor: pointer; color: green; text-decoration: underline; margin-left: 10px;">快速获取</a>
          <a href="https://aliyuntoken.vercel.app/" class="hint" target="_blank">获取阿里token</a>
          <span class="hint">更新时间： {{ formatTime(form.refreshTokenTime) }}</span>
        </el-form-item>
        <el-form-item prop="openToken" label="开放refresh token" required>
          <el-input v-model="form.openToken" type="textarea" rows="4" minlength="256" placeholder="长度280位"/>  
            <!-- xhofe:<a @click="openAListQRCodeModal1" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a> -->
            nn_ci:<a @click="openAListQRCodeModal" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a>
          <!-- ===1203修改认证地址检查 -->
          <div class="hint">
            webdav:<a href="https://messense-aliyundrive-webdav-backendrefresh-token-ucs0wn.streamlit.app/" 
              title="需要选择webdav的认证URL" 
              target="_blank" 
              @click="() => tokenSource = 'webdav'">获取开放token</a>
          </div>
          <!-- ===1104 修改tv token -->
          <div class="hint">
            TV Token(自认证):<a @click="openQRCodeModal" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a>
          </div>
          <!-- ===1203修改认证地址检查 -->
          <div class="hint">
            xhofe:<a @click="openAListQRCodeModal1" style="cursor: pointer; color: blue; text-decoration: underline;">获取开放token</a>
          </div>
          <span class="hint">创建时间： {{ formatTime(iat[2]) }}</span>
          <span class="hint">更新时间： {{ formatTime(form.openTokenTime) }}</span>
          <span class="hint">过期时间： {{ formatTime(exp[2]) }}</span>
        </el-form-item>
        <el-form-item label="加载我的云盘" v-if="form.openToken">
          <el-switch
            v-model="form.showMyAli"
            inline-prompt
            active-text="加载"
            inactive-text="关闭"
          />
        </el-form-item>
        <el-form-item label="主账号">
          <el-switch
            v-model="form.master"
            inline-prompt
            active-text="是"
            inactive-text="否"
          />
          <span class="hint">主账号用来观看分享。</span>
        </el-form-item>
        <el-form-item label="自动签到">
          <el-switch
            v-model="form.autoCheckin"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>
        <el-form-item label="上次签到时间" v-if="form.checkinTime">
          <el-input :model-value="formatTime(form.checkinTime)" readonly/>
          <span class="hint" v-if="form.checkinDays">{{ form.nickname }} 本月签到{{ form.checkinDays }}次</span>
        </el-form-item>
        <el-checkbox v-model="forceCheckin" label="强制签到"/>
      </el-form>
      <template #footer>
          <span class="dialog-footer">
            <el-button @click="detailVisible = false">取消</el-button>
            <el-button type="success" @click="checkin">签到</el-button>
            <el-button type="primary" @click="handleConfirm">更新</el-button>
          </span>
      </template>
    </el-dialog>

    <el-dialog v-model="alistVisible" title="更新成功" width="40%">
      <p>需要重启AList服务后才能生效</p>
      <p>是否重启AList服务？</p>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="alistVisible = false">取消</el-button>
        <el-button type="danger" @click="restartAList">重启</el-button>
      </span>
      </template>
    </el-dialog>

    <el-dialog v-model="timelineVisible" title="签到日志" width="60%">
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="index"
          :type="activity.status!='end'?'primary':''"
          :hollow="activity.status!='verification'"
          :timestamp="activity.date"
        >
          {{ activity.name }}
        </el-timeline-item>
      </el-timeline>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="timelineVisible = false">关闭</el-button>
      </span>
      </template>
    </el-dialog>

    <!-- ===1130修改opentoken的认证地址弹窗交互 -->
    <!-- el-dialog v-model="restartGBoxVisible" title="认证地址更新成功" width="40%">
      <p>opentoken更新成功，并已修改为对应的认证地址</p>
      <p>需要重启G-Box容器后才能生效，是否立即重启？</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="restartGBoxVisible = false">稍后重启</el-button>
          <el-button type="primary" @click="handleRestartGBox">立即重启</el-button>
        </span>
      </template>
    </el-dialog -->

    <div class="divider"></div>

    <PikPakView></PikPakView>

    <!-- ==========0922 c26 -->
    <div class="divider"></div>

    <pan-account-view></pan-account-view>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref, h} from 'vue'
import {Check, Close} from '@element-plus/icons-vue'
import axios from "axios"
import {ElMessage} from "element-plus";
import {store} from "@/services/store";
import router from "@/router";
import PikPakView from '@/views/PikPakView.vue'
// ==========0922 c27
import PanAccountView from "@/views/PanAccountView.vue";
// ===修改open token认证地址的检查
import { ElMessageBox } from 'element-plus'


const iat = ref([0])
const exp = ref([0])
const activities = ref<any[]>([])
const forceCheckin = ref(false)
const updateAction = ref(false)
const dialogTitle = ref('')
const accounts = ref([])
const formVisible = ref(false)
const dialogVisible = ref(false)
const detailVisible = ref(false)
const alistVisible = ref(false)
const timelineVisible = ref(false)
// ===1130修改opentoken的认证地址弹窗交互
// const restartGBoxVisible = ref(false)
// const showRestartGBox = ref(false)
// ===1202 增加open-token认证地址标识
const tokenSource = ref('') 
const aliV2SessionId = ref('')
const aliV2PollTimer = ref<number | null>(null)

const checkDockerSocket = async () => {
  try {
    const { data } = await axios.get('/api/settings/check-docker-socket')
    return data
  } catch (error) {
    return false
  }
}

const handleRestartGBox = async () => {
  try {
    ElMessage.success('G-Box 重启操作已执行，配置目录中auto_update.log可查看日志！')
    await axios.post('/api/settings/restart-gbox')
  } catch (error) {
    console.error('Error restarting G-Box:', error)
    ElMessage.error('G-Box 重启失败，请检查日志或手动重启')
  }
}

const sid = ref('')

const form = ref({
  id: 0,
  nickname: '',
  refreshToken: '',
  openToken: '',
  accessToken: '',
  accessTokenOpen: '',
  autoCheckin: true,
  showMyAli: false,
  master: false,
  refreshTokenTime: '',
  openTokenTime: '',
  accessTokenTime: '',
  accessTokenOpenTime: '',
  checkinTime: '',
  checkinDays: 1,
})

const formatTime = (value: string | number) => {
  return new Date(value).toLocaleString('zh-cn')
}

const showDetails = (data: any) => {
  form.value = Object.assign({}, data)
  updateAction.value = true
  if (form.value.accessToken) {
    let details = JSON.parse(atob(form.value.accessToken.split('.')[1]))
    iat.value[0] = details.iat * 1000
    exp.value[0] = details.exp * 1000
  }
  if (form.value.accessTokenOpen) {
    let details = JSON.parse(atob(form.value.accessTokenOpen.split('.')[1]))
    iat.value[1] = details.iat * 1000
    exp.value[1] = details.exp * 1000
  }
  if (form.value.openToken) {
    let details = JSON.parse(atob(form.value.openToken.split('.')[1]))
    iat.value[2] = details.iat * 1000
    exp.value[2] = details.exp * 1000
  }
  detailVisible.value = true
}

const checkin = () => {
  axios.post('/api/ali/accounts/' + form.value.id + '/checkin?force=' + forceCheckin.value).then(({data}) => {
    form.value.checkinTime = data.checkinTime
    form.value.checkinDays = data.signInCount
    form.value.nickname = data.nickname
    forceCheckin.value = false
    ElMessage.success('签到成功, 本月累计' + data.signInCount + '天')
    load()
  })
}

const handleAdd = () => {
  dialogTitle.value = '添加阿里账号'
  updateAction.value = false
  form.value = {
    id: 0,
    nickname: '',
    refreshToken: '',
    openToken: '',
    accessToken: '',
    accessTokenOpen: '',
    autoCheckin: true,
    showMyAli: false,
    master: false,
    refreshTokenTime: '',
    openTokenTime: '',
    accessTokenTime: '',
    accessTokenOpenTime: '',
    checkinTime: '',
    checkinDays: 1,
  }
  formVisible.value = true
}

const handleDelete = (data: any) => {
  form.value = data
  dialogVisible.value = true
}

const deleteAccount = () => {
  dialogVisible.value = false
  axios.delete('/api/ali/accounts/' + form.value.id).then(() => {
    load()
  })
}

const handleCancel = () => {
  formVisible.value = false
}

const getCurrentHost = () => {
  const protocol = window.location.protocol
  const hostname = window.location.hostname
  const port = window.location.port
  return `${protocol}//${hostname}${port ? `:${port}` : ''}`
}

// const handleConfirm = async () => {
//   try {
//     const url = updateAction.value ? '/api/ali/accounts/' + form.value.id : '/api/ali/accounts'
//     axios.post(url, form.value).then(async (response) => {
//       detailVisible.value = false
//       if (accounts.value.length === 0) {
//         if (store.aListStatus) {
//           ElMessage.success('添加成功')
//         } else {
//           ElMessage.success('添加成功，AList服务重启中。')
//           setTimeout(() => router.push('/wait'), 3000)
//         }
//       } else {
//         if (response.headers['alist_restart_required']) {
//           ElMessage.success('更新成功，需要重启AList生效')
//           alistVisible.value = true
//         } else {
//           ElMessage.success('更新成功')
//         }
//       }
//       formVisible.value = false
//       load()
//     })

//     let expectedUrl = ''
//     let needUpdate = false
//     console.log('===当前token来源:', tokenSource.value)
//     const { data: currentUrl } = await axios.get('/api/settings/open_token_url')
//     console.log('===获取到当前认证URL:', currentUrl)
//     if (tokenSource.value) {
//       // 根据 token 来源设置正确的认证地址
//       switch (tokenSource.value) {
//         case 'xhofe':
//           expectedUrl = 'https://api.xhofe.top/alist/ali_open/token'
//           needUpdate = currentUrl.value !== expectedUrl
//           break
//         case 'webdav':
//           expectedUrl = 'https://aliyundrive-oauth.messense.me/oauth/access_token'
//           needUpdate = currentUrl.value !== expectedUrl
//           break
//         case 'nn_ci':
//           expectedUrl = 'https://api.nn.ci/alist/ali_open/token'
//           needUpdate = currentUrl.value !== expectedUrl
//           break
//         case 'tv_diy':
//           // expectedUrl = `${getCurrentHost()}/api/oauth/alipan/token`
//           expectedUrl = `http://127.0.0.1:4567/api/oauth/alipan/token`
//           needUpdate = currentUrl.value !== expectedUrl
//           break
//       }
//       console.log('===当前URL:', currentUrl.value, '期望URL:', expectedUrl, '需要更新:', needUpdate)
//     }

//     if (needUpdate) {
//       try {
//         await updateAuthUrl(expectedUrl)
//       } catch (error) {
//         console.error('更新认证URL失败:', error)
//         ElMessage.error('更新认证URL失败：' + (error as Error).message)
//       }
//     }
//   } catch (error) {
//     console.error('处理过程中出错:', error)
//     ElMessage.error('操作失败：' + (error as Error).message)
//   }
// }

// ===1225尝试免重启更新ali的token
const handleConfirm = async () => {
  try {
    let expectedUrl = ''
    let needUpdate = false
    // console.log('===当前token来源:', tokenSource.value)
    const { data: currentUrl } = await axios.get('/api/settings/open_token_url')
    // console.log('===获取到当前认证URL:', currentUrl)
    if (tokenSource.value) {
      // 根据 token 来源设置正确的认证地址
      switch (tokenSource.value) {
        case 'xhofe':
          expectedUrl = 'https://api.xhofe.top/alist/ali_open/token'
          needUpdate = currentUrl.value !== expectedUrl
          break
        case 'webdav':
          expectedUrl = 'https://aliyundrive-oauth.messense.me/oauth/access_token'
          needUpdate = currentUrl.value !== expectedUrl
          break
        case 'nn_ci':
          expectedUrl = 'https://api.nn.ci/alist/ali_open/token'
          needUpdate = currentUrl.value !== expectedUrl
          break
        case 'tv_diy':
          // expectedUrl = `${getCurrentHost()}/api/oauth/alipan/token`
          expectedUrl = `http://127.0.0.1:4567/api/oauth/alipan/token`
          needUpdate = currentUrl.value !== expectedUrl
          break
      }
      // console.log('===当前URL:', currentUrl.value, '期望URL:', expectedUrl, '需要更新:', needUpdate)
    }
    if (needUpdate) {
      try {
        await updateAuthUrl(expectedUrl)
      } catch (error) {
        console.error('更新认证URL失败:', error)
        ElMessage.error('更新认证URL失败：' + (error as Error).message)
      }
    }

    const url = updateAction.value ? '/api/ali/accounts/' + form.value.id : '/api/ali/accounts'
    axios.post(url, form.value).then(async (response) => {
      detailVisible.value = false
      if (accounts.value.length === 0) {
        ElMessage.success('添加成功')
      } else {
        // if (response.headers['need_update_storage'] === 'true' || needUpdate) {
        //   ElMessage.success('将为您更新阿里token或认证地址，即时生效，无需重启！')
        //   await axios.post('/api/ali/accounts/re_init')
        // } else {
        //   ElMessage.success('更新成功')
        // }
        if (response.headers['need_update_storage'] === 'true' || needUpdate) {
          try {
            const reinitResponse = await axios.post('/api/ali/accounts/re_init')
            console.log('重新初始化响应:', reinitResponse.data)
            
            const responseData = reinitResponse.data
            // 由于后端返回的一定是 Response 结构，可以直接判断 code
            if (responseData.code !== 200) {
              console.log('检测到错误响应:', responseData.message)
              // 风控相关的错误提示
              if (responseData.message.includes('Too Many Requests') || 
                  responseData.message.includes('被风控') || responseData.message.includes('invalid refresh_token') ||
                  responseData.message.includes('refresh token is empty')) {
                ElMessage.error({
                  message: 'open_token失效或风控，请更换认证地址重新获取open_token',
                  duration: 8000
                })
              } else if (responseData.message.includes('timeout')) {
                ElMessage.info({
                  message: '正在重载失效的小雅阿里资源……约2分钟完成，请稍候刷新5678页面检查是否成功！',
                  duration: 10000
                })
              } else {
                ElMessage.error({
                  message: responseData.message || '更新失败',
                  duration: 5000
                })
              }
              return
            }
            
            // 成功响应
            console.log('更新成功')
            ElMessage.success({
              message: '阿里token或认证地址已更新，已即时生效，无需重启！',
              duration: 8000
            })
          } catch (error: any) {
            // 处理网络错误或其他异常
            const errorMsg = error.response?.data?.message || error.message
            ElMessage.error({
              message: '更新失败：' + errorMsg,
              duration: 5000
            })
          }
        } else {
          ElMessage.success({
            message: '更新成功',
            duration: 3000
          })
        }
      }
      formVisible.value = false
      load()
    })
  } catch (error) {
    console.error('Error in handleConfirm:', error)
  }
}

const updateAuthUrl = async (url: string) => {
  try {
    // 获取当前的 clientId 和 clientSecret
    const { data: clientIdSetting } = await axios.get('/api/settings/open_api_client_id')
    const { data: clientSecretSetting } = await axios.get('/api/settings/open_api_client_secret')
    
    // 构建请求参数
    const dto = {
      url: url,
      clientId: clientIdSetting?.value || '',
      clientSecret: clientSecretSetting?.value || ''
    }
    await axios.post('/api/open-token-url', dto)
    
    // 直接给出执行成功的提示
    ElMessage.success('认证URL已更新')
  } catch (error) {
    ElMessage.error('更新认证URL失败：' + (error as Error).message)
    throw error
  }
}



// ===1202 增加open-token认证地址标识
// const updateAuthUrl = async (url: string) => {
//   try {
//     // 获取当前的 clientId 和 clientSecret
//     const { data: clientIdSetting } = await axios.get('/api/settings/open_api_client_id')
//     const { data: clientSecretSetting } = await axios.get('/api/settings/open_api_client_secret')
    
//     // 构建请求参数
//     const dto = {
//       url: url,
//       clientId: clientIdSetting?.value || '',
//       clientSecret: clientSecretSetting?.value || ''
//     }
//     await axios.post('/api/open-token-url', dto)
//     // const canRestart = await checkDockerSocket()
//     // if (canRestart) {
//       try {
//         await ElMessageBox.confirm(
//           '认证URL已更新，需要重启AList才能生效。是否立即重启？',
//           '提示',
//           {
//             confirmButtonText: '立即重启',
//             cancelButtonText: '稍后手动重启',
//             type: 'warning',
//           }
//         )
//         // 用户选择立即重启
//         ElMessage.success('重启指令已发送')
//         // await handleRestartGBox()
//         await restartAList()
//       } catch {
//         // 用户选择稍后手动重启
//         ElMessage.info('请记得稍后手动重启容器以使更改生效')
//       }
//     // } else {
//     //   ElMessage.success('认证URL已更新，请稍后手动重启容器以使更改生效')
//     // }
//   } catch (error) {
//     ElMessage.error('更新认证URL失败：' + (error as Error).message)
//     throw error
//   }
// }
const restartAList = () => {
  axios.post('/api/alist/restart').then(() => {
    alistVisible.value = false
    ElMessage.success('AList重启中')
    setTimeout(() => router.push('/wait'), 1000)
  })
}

const loadTimeline = (id: number) => {
  axios.get('/api/ali/accounts/' + id + '/checkin').then(({data}) => {
    activities.value = data
    timelineVisible.value = true
  })
}

const load = () => {
  axios.get('/api/ali/accounts').then(({data}) => {
    accounts.value = data
  })
}

// ===1104 修改tv token
const openQRCodeModal = () => {
  // ===1202 增加open-token认证地址标识
  tokenSource.value = 'tv_diy'
  // console.log('Opening QR Code Modal...')
  axios.get('/api/get_tv_token').then(response => {
    console.log('Received response from /api/get_tv_token:', response)
    const { qr_code, sid: responseSid } = response.data; // 确保从 response.data 中获取数据
    sid.value = responseSid;
    // console.log('qr_code is:', qr_code);
    // console.log('sid is:', sid);

    // 打开新窗口显示二维码
    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <div>
          <img src="data:image/png;base64,${qr_code}" alt="QR Code" />
          <p>请用手机阿里云盘APP扫码登录并授权确认</p>
        </div>
      `);
      pollQRCodeStatus(newWindow);
    } else {
      console.error("Failed to open new window.");
    }
  }).catch(error => {
    console.error('Error fetching TV token:', error);
    ElMessage.error('获取二维码失败，请重试');
  });
}

const pollQRCodeStatus = (newWindow: Window | null) => {
  const startTime = Date.now();
  const poll = () => {
    axios.get('/api/check_qr_status', { params: { sid: sid.value } }).then(response => {
      const { auth_code } = response.data
      if (auth_code) {
        axios.post('/api/get_tokens', { auth_code, sid: sid.value }, {
          headers: {
            'Content-Type': 'application/json'
          }
        }).then(response => {
          const { refresh_token, url_updated } = response.data;
          form.value.openToken = refresh_token;
          if (newWindow) {
              newWindow.close();
          }
          ElMessage.success('扫码成功，Token已获取，请点击更新按钮保存')
        }).catch(error => {
          console.error('Error fetching tokens:', error);
          if (newWindow) {
            newWindow.close();
          }
          ElMessage.error('获取令牌失败，请重试');
        });
      } else {
        if (Date.now() - startTime < 60000) { // 1分钟内继续轮询
          setTimeout(poll, 3000) // 每3秒检查一次
        } else {
          if (newWindow) {
            newWindow.close();
          }
          ElMessage.error('扫码超时，请重新获取二维码');
        }
      }
    }).catch(error => {
      console.error('Error polling QR code status:', error)
      if (newWindow) {
        newWindow.close();
      }
      ElMessage.error('获取二维码状态失败，请重试');
    })
  }
  setTimeout(poll, 3000)
}

// ===1106 修改阿里token获取
// const openAliQRCodeModal = () => {
//   // console.log('Opening Ali QR Code Modal...');
//   axios.get('/api/get_ali_qr_code').then(response => {
//     // console.log('Received response from /api/get_ali_qr_code:', response);
//     const { qr_code } = response.data;

//     // 打开新窗口显示二维码
//     const newWindow = window.open();
//     if (newWindow) {
//       newWindow.document.write(`
//         <div>
//           <img src="data:image/png;base64,${qr_code}" alt="QR Code" />
//           <p>请用手机阿里云盘APP扫码登录并授权确认</p>
//         </div>
//       `);
//       pollAliQRCodeStatus(newWindow);
//     } else {
//       console.error("Failed to open new window.");
//     }
//   }).catch(error => {
//     console.error('Error fetching Ali QR code:', error);
//     ElMessage.error('获取二维码失败，请重试');
//   });
// }

// const pollAliQRCodeStatus = (newWindow: Window | null) => {
//   const startTime = Date.now();
//   const poll = () => {
//     axios.get('/api/check_ali_qr_status').then(response => {
//       const { status, refresh_token } = response.data;
//       if (status === 'success') {
//         form.value.refreshToken = refresh_token;
//         if (newWindow) {
//           newWindow.close();
//         }
//         ElMessage.success('扫码成功，Token已获取');
//       // } else if (status === 'failure') {
//       //   // 不关闭窗口，继续轮询
//       //   if (Date.now() - startTime < 60000) { // 1分钟内继续轮询
//       //     setTimeout(poll, 3000); // 每3秒检查一次
//       //   } else {
//       //     if (newWindow) {
//       //       newWindow.close();
//       //     }
//       //     ElMessage.error('扫码失败，请重新获取二维码');
//       //   }
//       } else {
//         if (Date.now() - startTime < 60000) { // 1分钟内继续轮询
//           setTimeout(poll, 3000); // 每3秒检查一次
//         } else {
//           if (newWindow) {
//             newWindow.close();
//           }
//           ElMessage.error('扫码超时，请重新获取二维码');
//         }
//       }
//     }).catch(error => {
//       console.error('Error polling QR code status:', error);
//       if (newWindow) {
//         newWindow.close();
//       }
//       ElMessage.error('获取二维码状态失败，请重试');
//     });
//   };
//   setTimeout(poll, 3000);
// }

// 快速获取token的方法
const openQuickTokenModal = () => {
  const script = 'JSON.parse(localStorage.token).refresh_token';

  ElMessageBox.confirm(
    `<div style="text-align: left;">
      <h3>🚀 快速获取阿里云盘Token</h3>
      <p><strong>操作步骤：</strong></p>
      <ol>
        <li>点击下方按钮打开阿里云盘并登录</li>
        <li>按 <kbd>F12</kbd> 打开开发者工具</li>
        <li>点击 <kbd>Console</kbd> 标签</li>
        <li>粘贴以下代码并按回车：</li>
      </ol>
      <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0; position: relative;">
        <code style="font-family: monospace;">${script}</code>
        <button onclick="navigator.clipboard.writeText('${script}'); this.innerText='已复制!'; setTimeout(() => this.innerText='复制代码', 1000)"
                style="position: absolute; right: 5px; top: 5px; padding: 2px 8px; font-size: 12px; background: #409eff; color: white; border: none; border-radius: 3px; cursor: pointer;">
          复制代码
        </button>
      </div>
      <p>5. 复制显示的32位token，返回此页面粘贴</p>
      <p style="color: #67c23a; font-size: 12px;">✨ 比扫码快3倍，无需等待！</p>
    </div>`,
    '快速获取Token',
    {
      confirmButtonText: '🚀 打开阿里云盘',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true,
      type: 'info',
      customClass: 'quick-token-dialog'
    }
  ).then(() => {
    // 打开阿里云盘
    window.open('https://www.alipan.com', '_blank');

    // 延迟显示输入框，给用户时间操作
    setTimeout(() => {
      ElMessageBox.prompt(
        `<div style="text-align: left;">
          <p>📋 请将获取到的32位refresh_token粘贴到下方：</p>
          <p style="color: #909399; font-size: 12px;">格式示例：1d592bedc1a64edbb4c987de2b4738df</p>
        </div>`,
        '输入Token',
        {
          confirmButtonText: '✅ 确定',
          cancelButtonText: '取消',
          inputPattern: /^[a-f0-9]{32}$/,
          inputErrorMessage: '❌ Token格式不正确，应为32位字符串',
          dangerouslyUseHTMLString: true,
          inputPlaceholder: '粘贴您的32位refresh_token...'
        }
      ).then(({ value }) => {
        if (value) {
          form.value.refreshToken = value;
          ElMessage.success('🎉 Token已自动填入！');
        }
      }).catch(() => {
        ElMessage.info('已取消操作');
      });
    }, 3000);
  }).catch(() => {
    ElMessage.info('已取消操作');
  });
};

const openAliQRCodeModal = () => {
  axios.get('/api/get_ali_qr_code').then(response => {
    const { qr_code } = response.data;

    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <div>
          <img src="data:image/png;base64,${qr_code}" alt="QR Code" />
          <p>请用手机阿里云盘APP扫码登录并授权确认</p>
          <button id="confirmButton" style="background-color: blue; color: white; cursor: pointer;">
            已扫码请点击
          </button>
          <script>
            document.getElementById('confirmButton').onclick = function() {
              window.close(); // 关闭当前窗口
            };
          <\/script>
        </div>
      `);

      const poll = () => {
        axios.get('/api/check_ali_qr_status')
          .then(response => response.data)
          .then(data => {
            const { status, refresh_token } = data;
            if (status === 'success') {
              form.value.refreshToken = refresh_token;
              ElMessage.success('获取 token 成功');
              clearInterval(intervalId);
              if (newWindow) {
                clearTimeout(checkWindowInterval);
                newWindow.close();
              }
            } else {
              ElMessage.error('获取 token 失败，请重试');
            }
          })
          .catch(error => {
            console.error('Error fetching token:', error);
            ElMessage.error('请求失败，请重试');
          });
      };

      // 监听新窗口关闭事件
      let checkWindowInterval: number | undefined = undefined;
      const checkWindowClosed = () => {
        if (newWindow.closed) {
          clearInterval(intervalId);
          poll(); // 弹窗关闭时立即查询一次
        } else {
          checkWindowInterval = setTimeout(checkWindowClosed, 2000); // 每2秒检查一次
        }
      };

      checkWindowClosed();

      let pollCount = 0;
      const maxPollCount = 2;
      const intervalId = setInterval(() => {
        if (pollCount < maxPollCount) {
          poll();
          pollCount++;
        } else {
          clearInterval(intervalId);
          ElMessage.error('扫码超时，请重新获取二维码');
          if (newWindow) {
            newWindow.close(); // 关闭弹窗
          }
        }
      }, 20000); // 每20秒查询一次
    } else {
      console.error("Failed to open new window.");
    }
  }).catch(error => {
    console.error('Error fetching Ali QR code:', error);
    ElMessage.error('获取二维码失败，请重试');
  });
}

const openAListQRCodeModal = () => {
  tokenSource.value = 'nn_ci';
  axios.get('/api/get_ali_open_qrcode').then(response => {
    const { qr_code } = response.data;

    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <div>
          <img src="data:image/png;base64,${qr_code}" alt="QR Code" />
          <p>请用手机阿里云盘APP扫码登录并授权确认</p>
          <button id="confirmButton" style="background-color: blue; color: white; cursor: pointer;">
            已扫码请点击
          </button>
          <script>
            document.getElementById('confirmButton').onclick = function() {
              window.close(); // 关闭当前窗口
            };
          <\/script>
        </div>
      `);

      // 监听新窗口关闭事件
      const poll = () => {
        axios.get('/api/check_ali_open_qr_status')
          .then(response => response.data)
          .then(data => {
            const { status, refresh_token } = data;
            if (status === 'success') {
              form.value.openToken = refresh_token; // 更新 token
              ElMessage.success('获取 token 成功');
              clearInterval(intervalId);
              if (newWindow) {
                clearTimeout(checkWindowInterval);
                newWindow.close();
              }
            } else {
              ElMessage.error('获取 token 失败，请重试');
            }
          })
          .catch(error => {
            console.error('Error fetching token:', error);
            ElMessage.error('请求失败，请重试');
          });
      };

      // 监听新窗口关闭事件
      let checkWindowInterval: number | undefined = undefined;
      const checkWindowClosed = () => {
        if (newWindow.closed) {
          clearInterval(intervalId);
          poll(); // 弹窗关闭时立即查询一次
        } else {
          checkWindowInterval = setTimeout(checkWindowClosed, 2000); // 每2秒检查一次
        }
      };

      checkWindowClosed();

      let pollCount = 0;
      const maxPollCount = 2;
      const intervalId = setInterval(() => {
        if (pollCount < maxPollCount) {
          poll();
          pollCount++;
        } else {
          clearInterval(intervalId);
          ElMessage.error('扫码超时，请重新获取二维码');
          if (newWindow) {
            newWindow.close(); // 关闭弹窗
          }
        }
      }, 20000); // 每20秒查询一次
    } else {
      console.error("Failed to open new window.");
    }
  }).catch(error => {
    console.error('Error fetching AList QR code:', error);
    ElMessage.error('获取二维码失败，请重试');
  });
}

const openAListQRCodeModal1 = () => {
  tokenSource.value = 'xhofe';
  axios.get('/api/get_ali_open_qrcode1').then(response => {
    const { qr_code } = response.data;

    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <div>
          <img src="data:image/png;base64,${qr_code}" alt="QR Code" />
          <p>请用手机阿里云盘APP扫码登录并授权确认</p>
          <button id="confirmButton" style="background-color: blue; color: white; cursor: pointer;">
            已扫码请点击
          </button>
          <script>
            document.getElementById('confirmButton').onclick = function() {
              window.close(); // 关闭当前窗口
            };
          <\/script>
        </div>
      `);

      // 监听新窗口关闭事件
      const poll = () => {
        axios.get('/api/check_ali_open_qr_status1')
          .then(response => response.data)
          .then(data => {
            const { status, refresh_token } = data;
            if (status === 'success') {
              form.value.openToken = refresh_token; // 更新 token
              ElMessage.success('获取 token 成功');
              clearInterval(intervalId);
              if (newWindow) {
                clearTimeout(checkWindowInterval);
                newWindow.close();
              }
            } else {
              ElMessage.error('获取 token 失败，请重试');
            }
          })
          .catch(error => {
            console.error('Error fetching token:', error);
            ElMessage.error('请求失败，请重试');
          });
      };

      // 监听新窗口关闭事件
      let checkWindowInterval: number | undefined = undefined;
      const checkWindowClosed = () => {
        if (newWindow.closed) {
          clearInterval(intervalId);
          poll(); // 弹窗关闭时立即查询一次
        } else {
          checkWindowInterval = setTimeout(checkWindowClosed, 2000); // 每2秒检查一次
        }
      };

      checkWindowClosed();

      let pollCount = 0;
      const maxPollCount = 2;
      const intervalId = setInterval(() => {
        if (pollCount < maxPollCount) {
            poll();
            pollCount++;
        } else {
          clearInterval(intervalId);
          ElMessage.error('扫码超时，请重新获取二维码');
          if (newWindow) {
            clearTimeout(checkWindowInterval); // 清除监听弹窗关闭的定时器
            newWindow.close(); // 关闭弹窗
          }
        }
      }, 20000); // 每20秒查询一次
    } else {
      console.error("Failed to open new window.");
    }
  }).catch(error => {
    console.error('Error fetching AList QR code:', error);
    ElMessage.error('获取二维码失败，请重试');
  });
}

const openAliCloudV2Modal = () => {
  tokenSource.value = 'alicloud_v2'
  console.log('Opening AliCloud V2 QR Code Modal...')

  axios.get('/api/alicloud_v2/generate_qr').then(response => {
    console.log('Received response from /api/alicloud_v2/generate_qr:', response)

    const { qr_code, session_id } = response.data
    aliV2SessionId.value = session_id

    // 使用Element Plus的对话框显示二维码
    ElMessageBox({
      title: '阿里云盘扫码登录V2',
      message: h('div', { style: 'text-align: center; padding: 20px;' }, [
        h('img', {
          src: `data:image/png;base64,${qr_code}`,
          alt: 'QR Code',
          style: 'width: 300px; height: 300px; margin-bottom: 15px;'
        }),
        h('p', { style: 'margin: 10px 0; color: #333;' }, '请用手机阿里云盘APP扫码登录并授权确认'),
        h('p', { style: 'color: #666; font-size: 12px; margin: 5px 0;' }, '二维码有效期10分钟'),
        h('div', {
          id: 'qr-status-v2',
          style: 'margin-top: 10px; padding: 8px; border-radius: 4px; font-size: 14px;'
        }, '等待扫码...')
      ]),
      showCancelButton: true,
      confirmButtonText: '已完成',
      cancelButtonText: '取消',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      beforeClose: (action, instance, done) => {
        // 清理轮询
        if (aliV2PollTimer.value) {
          clearTimeout(aliV2PollTimer.value)
          aliV2PollTimer.value = null
        }
        done()
      }
    }).then(() => {
      // 用户点击确认按钮
    }).catch(() => {
      // 用户点击取消按钮
    })

    // 开始轮询状态
    pollAliCloudV2Status()
  }).catch(error => {
    console.error('Error fetching AliCloud V2 QR code:', error)
    ElMessage.error('获取二维码失败，请重试')
  })
}

const pollAliCloudV2Status = () => {
  const startTime = Date.now()

  const updateStatus = (message: string, type: 'waiting' | 'success' | 'error' | 'scanned' = 'waiting') => {
    const statusEl = document.getElementById('qr-status-v2')
    if (statusEl) {
      statusEl.textContent = message
      statusEl.style.backgroundColor = type === 'success' ? '#f0f9ff' :
                                      type === 'error' ? '#fef2f2' :
                                      type === 'scanned' ? '#f0fdf4' : '#f9fafb'
      statusEl.style.color = type === 'success' ? '#0369a1' :
                            type === 'error' ? '#dc2626' :
                            type === 'scanned' ? '#16a34a' : '#6b7280'
    }
  }

  const poll = () => {
    axios.get('/api/alicloud_v2/check_status', {
      params: { session_id: aliV2SessionId.value }
    }).then((response: any) => {
      const { status, refresh_token, message } = response.data

      if (status === 'success') {
        // 扫码成功，直接填入token
        form.value.refreshToken = refresh_token
        updateStatus('✅ 扫码成功！Token已获取', 'success')
        ElMessage.success('扫码成功，Token已获取，请点击更新按钮保存')
        // 清理轮询
        if (aliV2PollTimer.value) {
          clearTimeout(aliV2PollTimer.value)
          aliV2PollTimer.value = null
        }
      } else if (status === 'expired') {
        updateStatus('❌ 二维码已过期', 'error')
        ElMessage.error('二维码已过期，请重新获取')
        if (aliV2PollTimer.value) {
          clearTimeout(aliV2PollTimer.value)
          aliV2PollTimer.value = null
        }
      } else if (status === 'error') {
        updateStatus('❌ 获取Token失败', 'error')
        ElMessage.error('获取Token失败：' + (message || '未知错误'))
        if (aliV2PollTimer.value) {
          clearTimeout(aliV2PollTimer.value)
          aliV2PollTimer.value = null
        }
      } else if (status === 'scanned') {
        // 已扫码，等待确认，继续轮询
        updateStatus('📱 已扫码，请在手机上确认登录', 'scanned')
        if (Date.now() - startTime < 600000) { // 10分钟内继续轮询
          aliV2PollTimer.value = setTimeout(poll, 2000) // 每2秒检查一次
        } else {
          updateStatus('⏰ 扫码超时', 'error')
          ElMessage.error('扫码超时，请重新获取二维码')
        }
      } else {
        // waiting状态，继续轮询
        const elapsed = Math.floor((Date.now() - startTime) / 1000)
        updateStatus(`⏳ 等待扫码... (${elapsed}s)`, 'waiting')
        if (Date.now() - startTime < 600000) { // 10分钟内继续轮询
          aliV2PollTimer.value = setTimeout(poll, 3000) // 每3秒检查一次
        } else {
          updateStatus('⏰ 扫码超时', 'error')
          ElMessage.error('扫码超时，请重新获取二维码')
        }
      }
    }).catch((error: any) => {
      console.error('Error polling AliCloud V2 status:', error)
      updateStatus('❌ 网络错误', 'error')
      ElMessage.error('获取二维码状态失败，请重试')
      if (aliV2PollTimer.value) {
        clearTimeout(aliV2PollTimer.value)
        aliV2PollTimer.value = null
      }
    })
  }

  aliV2PollTimer.value = setTimeout(poll, 3000)
}

onMounted(async () => {
  load()
})
</script>

<style scoped>
.divider {
  margin: 30px 0;
}

.space {
  margin-bottom: 6px;
}

.json pre {
  height: 600px;
  overflow: scroll;
}

.el-dialog {
  display: block !important;
}

.hint {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}

.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}

/* 快速获取token按钮样式 */
a[style*="color: green"] {
  background: linear-gradient(45deg, #67c23a, #85ce61);
  color: white !important;
  padding: 4px 8px;
  border-radius: 4px;
  text-decoration: none !important;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
}

a[style*="color: green"]:hover {
  background: linear-gradient(45deg, #85ce61, #67c23a);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(103, 194, 58, 0.4);
}

/* 快速获取对话框样式 */
:deep(.quick-token-dialog) {
  .el-message-box__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
  }

  .el-message-box__title {
    color: white;
  }

  .el-message-box__content {
    padding: 20px;
  }

  kbd {
    background: #f1f1f1;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 2px 4px;
    font-family: monospace;
    font-size: 12px;
  }
}
</style>
