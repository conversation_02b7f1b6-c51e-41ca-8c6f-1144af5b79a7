<template>
  <div id="config">
    <el-row>
      <el-col :xs="23" :sm="23" :md="23" :lg="11" :xl="11">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>AList运行状态</span>
              <div>
                <el-button v-if="showRestartGBox" type="primary" @click="handleRestartGBox">重启G-Box</el-button>
                <el-button type="primary" v-if="store.aListStatus===0" @click="handleAList('start')">启动Alist</el-button>
                <!-- <el-button type="warning" v-if="store.aListStatus===2" @click="handleAList('restart')">重启</el-button>
                <el-button type="danger" v-if="store.aListStatus===2" @click="handleAList('stop')">停止</el-button> -->
                <!-- ===1108 sync 515 -->
                <el-button type="warning" v-if="store.aListStatus!==0" @click="handleAList('restart')">重启Alist</el-button>
                <el-button type="danger" v-if="store.aListStatus!==0" @click="handleAList('stop')">停止Alist</el-button>
              </div>
            </div>
          </template>
          <el-switch
            v-model="aListStarted"
            inline-prompt
            :disabled="true"
            :active-text="store.aListStatus===2?'运行中':'启动中'"
            inactive-text="停止中"
          />
          <span class="hint" v-if="aListStartTime">启动时间：{{ formatTime(aListStartTime) }}</span>
          <span class="hint warning" v-if="aListRestart">AList需要重启</span>
          <el-progress
            :percentage="percentage"
            :stroke-width="15"
            status="success"
            striped
            striped-flow
            :duration="duration"
            v-if="intervalId"
          />
        </el-card>

        <el-card class="box-card" v-if="store.aListStatus">
          <el-form :model="login" label-width="120px">
            <el-form-item prop="token" label="强制登录AList">
              <el-switch
                v-model="login.enabled"
                inline-prompt
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            <el-form-item prop="username" label="用户名">
              <el-input v-model="login.username"/>
            </el-form-item>
            <el-form-item prop="password" label="密码">
              <el-input v-model="login.password" type="password" show-password/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateLogin">保存</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="box-card">
          <el-form :model="form" label-width="120px">
            <el-form-item prop="enabledToken" label="安全订阅">
              <el-switch
                v-model="form.enabledToken"
                inline-prompt
                active-text="开启"
                inactive-text="关闭"
              />
              <span class="hint">建议外网开启，多个安全Token，逗号分割。</span>
            </el-form-item>
            <el-form-item prop="token" label="安全Token" v-if="form.enabledToken">
              <el-input v-model="form.token" type="password" show-password/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateToken">更新</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :xs="23" :sm="23" :md="23" :lg="11" :xl="11">
        <el-card class="box-card">
          <el-form label-width="120px">
            <el-form-item label="计划时间">
              <el-time-picker v-model="scheduleTime"/>
              <el-button type="primary" @click="updateScheduleTime">更新</el-button>
              <span class="hint">自动签到的时间</span>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="box-card" v-if="dockerVersion||appVersion">
          <template #header>
            <div class="card-header">
              <span>应用数据</span>
              <div>
                <el-button type="primary" plain @click="dialogVisible=true">高级设置</el-button>
              </div>
            </div>
          </template>
          <div v-if="appVersion">应用版本：{{ appVersion }}</div>
          <div v-if="appRemoteVersion&&appRemoteVersion>appVersion">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="tooltip"
              placement="top"
            >
              最新版本：{{ appRemoteVersion }}，已打开容器管理功能和重启自动更新开关的直接点 重启G-Box 按钮即可更新，或者手动运行命令更新！
            </el-tooltip>
            <div class="changelog" v-if="changelog">更新日志： {{ changelog }}</div>
          </div>
        </el-card>

        <el-card class="box-card" v-if="indexVersion">
          <template #header>
            <div class="card-header">索引数据</div>
          </template>
          <div>本地版本：{{ indexVersion }}</div>
          <div v-if="indexRemoteVersion&&indexRemoteVersion!=indexVersion">
            最新版本：{{ indexRemoteVersion }}，后台更新中。
          </div>
        </el-card>

        <el-card class="box-card" v-if="movieVersion">
          <template #header>
            <div class="card-header">豆瓣电影数据</div>
          </template>
          <el-form label-width="110px">
            <el-form-item label="海报墙混合模式">
              <el-switch
                v-model="mixSiteSource"
                inline-prompt
                active-text="开启"
                inactive-text="关闭"
                @change="updateMixed"
              />
            </el-form-item>
          </el-form>
          <div>本地版本：<a href="/#/meta">{{ movieVersion }}</a></div>
          <div v-if="movieRemoteVersion&&movieRemoteVersion>movieVersion">
            最新版本：{{
              movieRemoteVersion
            }}，后台更新中。
          </div>
        </el-card>
        <!-- ==========0922 c5 -->
        <el-card class="box-card" v-if="!store.xiaoya">
          <template #header>
            <div class="card-header">海报墙</div>
          </template>
          <el-form label-width="110px">
            <el-form-item label="海报墙混合模式">
              <el-switch
                v-model="mixSiteSource"
                inline-prompt
                active-text="开启"
                inactive-text="关闭"
                @change="updateMixed"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- ===1021 sync 488 -->
    <!-- <el-dialog v-model="dialogVisible" title="高级功能" width="50%"> -->
    <el-dialog id="adv" v-model="dialogVisible" title="高级设置" width="60%">
      <el-form label-width="180px">
        <el-form-item label="开放Token认证URL">
          <el-select v-model="openTokenUrl" class="m-2" placeholder="Select">
            <el-option-group
              v-for="group in options"
              :key="group.label"
              :label="group.label"
            >
              <el-option
                v-for="item in group.options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-option-group>
          </el-select>
          <el-input v-model="openTokenUrl"/>
        </el-form-item>
        <!-- ===1021 sync 488 -->
        <!-- <el-form-item label="APP ID">
          <el-input v-model="apiClientId" type="text"/> -->
        <el-form-item label="Client ID">
          <el-input v-model="apiClientId" type="text" placeholder="默认不填，开发者账号才填"/>
        </el-form-item>
        <el-form-item label="APP Secret" label-width="180px">
          <el-input v-model="apiClientSecret" type="password" show-password placeholder="默认不填，开发者账号才填" style="width: calc(100% - 100px);"/>
          <el-button type="primary" @click="updateOpenTokenUrl" style="margin-left: 10px; width: 80px;">更新</el-button>
        </el-form-item>
        <el-form-item label="TMDB API Key" label-width="180px">
          <el-input v-model="tmdbApiKey" type="password" show-password style="width: calc(100% - 100px);"/>
          <el-button type="primary" @click="updateTmdbApiKey" style="margin-left: 10px; width: 80px;">更新</el-button>
        </el-form-item>
        <el-form-item label="Cookie地址">
          <a :href="currentUrl + '/ali/token/' + aliSecret" target="_blank">
            阿里Token
          </a><span class="hint"></span>
          <a :href="currentUrl + '/quark/cookie/' + aliSecret" target="_blank">
            夸克 Cookie
          </a><span class="hint"></span>
          <a :href="currentUrl + '/uc/cookie/' + aliSecret" target="_blank">
            UC Cookie
          </a><span class="hint"></span>
          <a :href="currentUrl + '/115/cookie/' + aliSecret" target="_blank">
            115 Cookie
          </a><span class="hint"></span>
          <a :href="currentUrl + '/bili/cookie/' + aliSecret" target="_blank">
             B站 Cookie
          </a>
        </el-form-item>
        <el-form-item label="订阅替换阿里token地址">
          <el-switch
            v-model="replaceAliToken"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            @change="updateReplaceAliToken"
          />
        </el-form-item>
        <el-form-item label="订阅域名支持HTTPS">
          <el-switch
            v-model="enableHttps"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            @change="updateEnableHttps"
          />
        </el-form-item>
        <el-form-item label="开启调试日志">
          <el-switch
            v-model="debugLog"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            @change="updateDebugLog"
          />
        </el-form-item>
        <el-form-item label="开启AList调试模式">
          <el-switch
            v-model="aListDebug"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            @change="updateAListDebug"
          />
        </el-form-item>
        <!-- ==========115 modified 4 -->
        <el-form-item label="清理115[我的接收]目录">
          <el-switch
            v-model="delete115Folder"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            @change="updateDelete115Folder"
          />
        </el-form-item>
        <!-- ==========115 modified 5 -->
        <el-form-item label="115清理安全密码" label-width="140">
          <el-input v-model="cleanPassword" type="password" placeholder="只会删除填写该密码之后产生的转存文件，不会删除g-box和我的接收目录之前产生的转存文件！" show-password autocomplete="off" style="width: calc(100% - 100px);"/>
          <el-button type="primary" @click="updateCleanPassword" style="margin-left: 10px; width: 80px;">更新</el-button>
        </el-form-item>
        <el-form-item label="开启阿里快传115">
          <el-switch
            v-model="aliTo115"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            @change="updateAliTo115"
          />
          <span class="hint">帐号页面添加115网盘</span>
        </el-form-item>
        <!-- ===1027 添加自动更新按钮 -->
        <el-form-item label="开启重启自动更新">
          <el-switch
            v-model="autoUpdate"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            @change="updateAutoUpdate"
          />
          <span class="hint">安装G-Box时必须打开容器管理功能才会生效</span>
        </el-form-item>
        <!--        <el-form-item label="开启阿里延迟加载">-->
        <!--          <el-switch-->
        <!--            v-model="aliLazyLoad"-->
        <!--            inline-prompt-->
        <!--            active-text="开启"-->
        <!--            inactive-text="关闭"-->
        <!--            @change="updateAliLazyLoad"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <el-form-item label="AList管理密码" v-if="!store.xiaoya">
          <el-input v-model="atvPass" type="password" show-password/>
        </el-form-item>
        <!-- <el-form-item label="网盘文件删除延时">
          <el-input-number v-model="deleteDelayTime" min="0"></el-input-number>
          秒
          <span class="hint">0表示不删除</span>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="updateDeleteDelayTime">更新</el-button>
        </el-form-item> -->
        <!-- ===1027 更新按钮调整到同一行 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="网盘文件删除延时">
              <el-input-number v-model="deleteDelayTime" min="0"></el-input-number>
              秒
              <span class="hint" style="margin-right: 10px;">0表示不删除</span>
              <el-button type="primary" @click="updateDeleteDelayTime">更新</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- ==========新增的表单项 -->
        <el-form-item label="Emby外网访问地址" label-width="180px">
          <el-input v-model="embyUrl" type="text" placeholder="填写小雅emby的外网访问地址（下同），重启后不会显示，实际已生效！示例：https://emby.mydomain.com:10086" style="width: calc(100% - 100px);"/>
          <el-button type="primary" @click="updateEmbyUrl" style="margin-left: 10px; width: 80px;">更新</el-button>
        </el-form-item>
        <el-form-item label="Jellyfin外网访问地址" label-width="180px">
          <el-input v-model="jellyfinUrl" type="text" placeholder="示例：https://jellyfin.mydomain.com:10086" style="width: calc(100% - 100px);"/>
          <el-button type="primary" @click="updateJellyfinUrl" style="margin-left: 10px; width: 80px;">更新</el-button>
        </el-form-item>
        <el-form-item label="Alist外网访问地址" label-width="180px">
          <el-input v-model="alistUrl" type="text" placeholder="示例：http://mydomain.com:56780" style="width: calc(100% - 100px);"/>
          <el-button type="primary" @click="updateAlistUrl" style="margin-left: 10px; width: 80px;">更新</el-button>
        </el-form-item>
        <el-form-item label="Sun-Panel外网访问地址" label-width="180px">
          <el-input v-model="sunPanelUrl" type="text" placeholder="sun-panel导航的内网端口是3002，外网访问导航页需做好该端口转发或映射！示例：http://mydomain.com:3002" style="width: calc(100% - 100px);"/>
          <el-button type="primary" @click="updateSunPanelUrl" style="margin-left: 10px; width: 80px;">更新</el-button>
        </el-form-item>
        <el-form-item>
          <span>填写格式：http(s)://域名:端口 示范：https://emby.imok.com:5566 （注：末尾不要有斜杠）</span>
        </el-form-item>
        <!-- 现有的表单项 -->
        <!-- <el-form-item>
          <el-button @click="resetAListToken">重置AList认证Token</el-button>
        </el-form-item> -->
        <!-- ===1122增加卸载sp按钮 -->
        <el-form-item>
          <el-button @click="resetAListToken">重置AList认证Token</el-button>
          <el-button style="margin-left: 10px" @click="handleSunPanelToggle">
            {{ sunPanelUninstalled ? '恢复内置sun-panel' : '卸载内置sun-panel' }}
          </el-button>
        </el-form-item>
        <!-- <el-form-item>
          <el-button @click="exportDatabase">导出数据库</el-button>
        </el-form-item> -->
        <!-- ===1111 增加一键恢复 -->
        <el-form-item>
          <el-button @click="exportDatabase">导出数据库</el-button>
          <el-select v-model="selectedBackup" placeholder="选择要恢复的备份文件" style="margin-left: 10px;">
            <el-option
              v-for="file in backupFiles"
              :key="file"
              :label="file"
              :value="file"
            />
          </el-select>
          <el-button type="primary" @click="restoreDatabase" style="margin-left: 10px;">恢复数据库</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible=false">取消</el-button>
      </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, ref} from "vue";
import {ElMessage} from "element-plus";
import axios from "axios";
import {onUnmounted} from "@vue/runtime-core";
import {store} from "@/services/store";
// ===1203修改open token认证地址弹窗重启
import { ElMessageBox } from 'element-plus'
import router from "@/router";

let intervalId = 0
const currentUrl = window.location.origin
const percentage = ref<number>(0)
const duration = computed(() => Math.floor(percentage.value / 10))

// ===1111增加一键恢复
const selectedBackup = ref('');
const backupFiles = ref([]);

const fetchBackupFiles = () => {
  axios.get('/api/settings/backup-files')
  .then(response => {
    backupFiles.value = response.data;
  })
  .catch(error => {
    console.error('Error fetching backup files:', error);
  });
};

const restoreDatabase = () => {
  if (!selectedBackup.value) {
    ElMessage.error('请选择一个备份文件');
    return;
  }

  axios.post('/api/settings/restore', { filename: selectedBackup.value })
  .then(response => {
    if (response.data.success) {
      ElMessage.success('操作完成，几分钟后检查是否恢复成功');
    } else {
      ElMessage.error('恢复失败，请手动重启 g-box 容器');
    }
  })
  .catch(error => {
    console.error('Error restoring database:', error);
    ElMessage.error('恢复失败，请手动重启 g-box 容器');
  });
};

const increase = () => {
  percentage.value += 1
  if (percentage.value > 100) {
    percentage.value = 100
  }
}

// const options = [
//   {
//     label: 'AList',
//     options: [
//       {label: 'api.xhofe.top', value: 'https://api.xhofe.top/alist/ali_open/token'},
//       {label: 'api.nn.ci ✈', value: 'https://api.nn.ci/alist/ali_open/token'},
//     ]
//   },
//   {
//     label: 'webdav',
//     options: [
//       {label: 'aliyundrive-webdav', value: 'https://aliyundrive-oauth.messense.me/oauth/access_token'},
//     ]
//   },
//   {
//     label: '阿里',
//     options: [
//       {label: 'openapi.alipan.com', value: 'https://openapi.alipan.com/oauth/access_token'},
//       {label: '会员TV Token', value: 'https://www.voicehub.top/api/v1/oauth/alipan/token'},
//     ]
//   }
// ]

const getCurrentHost = () => {
  const protocol = window.location.protocol
  const hostname = window.location.hostname
  const port = window.location.port
  return `${protocol}//${hostname}${port ? `:${port}` : ''}`
}

const options = [
  {
    label: 'AList',
    options: [
      {label: 'api.xhofe.top', value: 'https://api.xhofe.top/alist/ali_open/token'},
      {label: 'api.nn.ci ✈', value: 'https://api.nn.ci/alist/ali_open/token'},
    ]
  },
  {
    label: 'webdav',
    options: [
      {label: 'aliyundrive-webdav', value: 'https://aliyundrive-oauth.messense.me/oauth/access_token'},
    ]
  },
  {
    label: '阿里',
    options: [
      {label: 'openapi.alipan.com', value: 'https://openapi.alipan.com/oauth/access_token'},
      // {label: '会员TV Token', value: 'https://www.voicehub.top/api/v1/oauth/alipan/token'},
    ]
  },
  {
    label: '自建tv认证',
    options: [
      // {label: '自建TV认证', value: `${getCurrentHost()}/api/oauth/alipan/token`},
      {label: '自建TV认证', value: `http://127.0.0.1:4567/api/oauth/alipan/token`},
    ]
  }
]
// const tooltip = ref('sudo bash -c "$(curl -fsSL https://d.har01d.cn/update_xiaoya.sh)"')
// ===1114修改更新悬停提示
const tooltip = ref('bash -c "$(curl -sSLf https://gbox.ggbond.org/xy_install.sh)" -s g-box')
const aListStarted = ref(false)
const aListRestart = ref(false)
const mixSiteSource = ref(false)
const replaceAliToken = ref(false)
const debugLog = ref(false)
const aListDebug = ref(false)
const aliTo115 = ref(false)
// ===1027 增加自动更新
const autoUpdate = ref(false)
const aliLazyLoad = ref(false)
const enableHttps = ref(false)
const autoCheckin = ref(false)
const dialogVisible = ref(false)
const changelog = ref('')
const appVersion = ref(0)
const appRemoteVersion = ref(0)
const dockerVersion = ref('')
const indexVersion = ref('')
const indexRemoteVersion = ref('')
const movieVersion = ref(0)
const movieRemoteVersion = ref(0)
const cachedMovieVersion = ref(0)
// ==========0922 c84
// const fileExpireHour = ref(6)
const deleteDelayTime = ref(900)
const aListStartTime = ref('')
const openTokenUrl = ref('')
const dockerAddress = ref('')
const aliSecret = ref('')
const tmdbApiKey = ref('')
const atvPass = ref('')
const apiClientId = ref('')
const apiClientSecret = ref('')
const scheduleTime = ref(new Date(2023, 6, 20, 8, 0))
const login = ref({
  username: '',
  password: '',
  enabled: false
})
const form = ref({
  token: '',
  enabledToken: false
})
// ==========115 modified 1
const delete115Folder = ref(false)
const cleanPassword = ref('');

const formatTime = (value: string | number) => {
  return new Date(value).toLocaleString('zh-cn')
}

// ==========115 modified 2
const updateDelete115Folder = () => {
  axios.post('/api/settings', {name: 'delete115Folder', value: delete115Folder.value}).then(() => {
    ElMessage.success('更新成功');
  }).catch(error => {
    ElMessage.error('更新失败');
    console.error(error);
  });
};
// ==========115 modified 6
const updateCleanPassword = () => {
  axios.post('/api/settings', { name: 'cleanPassword', value: cleanPassword.value }).then(() => {
    ElMessage.success('更新成功');
  }).catch(error => {
    ElMessage.error('更新失败');
    console.error(error);
  });
};
// ==========update_url 1
const embyUrl = ref('')
const jellyfinUrl = ref('')
const alistUrl = ref('')
const sunPanelUrl = ref('')

// ==========update_url 2
const updateEmbyUrl = () => {
  axios.post('/api/update-url', { url: embyUrl.value, file: '/data/embyurl.txt' })
    .then(() => axios.post('/api/run-script', { scriptPath: '/usr/bin/update_url.sh' }))
    .then(() => ElMessage.success('Emby外网访问地址更新成功'))
    .catch(() => ElMessage.error('更新失败'))
}

const updateJellyfinUrl = () => {
  axios.post('/api/update-url', { url: jellyfinUrl.value, file: '/data/jellyfinurl.txt' })
    .then(() => axios.post('/api/run-script', { scriptPath: '/usr/bin/update_url.sh' }))
    .then(() => ElMessage.success('Jellyfin外网访问地址更新成功'))
    .catch(() => ElMessage.error('更新失败'))
}

const updateAlistUrl = () => {
  axios.post('/api/update-url', { url: alistUrl.value, file: '/data/alisturl.txt' })
    .then(() => axios.post('/api/run-script', { scriptPath: '/usr/bin/update_url.sh' }))
    .then(() => ElMessage.success('Alist外网访问地址更新成功'))
    .catch(() => ElMessage.error('更新失败'))
}

const updateSunPanelUrl = () => {
  axios.post('/api/update-url', { url: sunPanelUrl.value, file: '/data/sunpanelurl.txt' })
    .then(() => axios.post('/api/run-script', { scriptPath: '/usr/bin/update_url.sh' }))
    .then(() => ElMessage.success('Sun-Panel外网访问地址更新成功'))
    .catch(() => ElMessage.error('更新失败'))
}

const updateToken = () => {
  if (form.value.enabledToken) {
    axios.post('/api/token', {token: form.value.token}).then(({data}) => {
      form.value.token = data
      ElMessage.success('成功开启安全订阅')
    })
  } else {
    axios.delete('/api/token').then(() => {
      form.value.token = ''
      ElMessage.info('成功关闭安全订阅')
    })
  }
}

const resetAListToken = () => {
  axios.post('/api/alist/reset_token', {}).then(() => {
    ElMessage.success('AList认证Token重置成功')
  })
}

// const updateOpenTokenUrl = () => {
//   axios.post('/api/open-token-url', {
//     url: openTokenUrl.value,
//     clientId: apiClientId.value,
//     clientSecret: apiClientSecret.value
//   }).then(() => {
//     ElMessage.success('更新成功，重启生效')
//   })
// }

// ===1203修改open token认证地址弹窗重启
const updateOpenTokenUrl = async () => {
  try {
    await axios.post('/api/open-token-url', {
      url: openTokenUrl.value,
      clientId: apiClientId.value,
      clientSecret: apiClientSecret.value
    })
    
    // ===1227修复更新认证地址免重启
    ElMessage.success('将为您更新认证URL，即时生效，无需重启！')
    await axios.post('/api/ali/accounts/re_init')

    // try {
    //   await ElMessageBox.confirm(
    //     '认证URL已更新，需要重启AList才能生效。是否立即重启？',
    //     '提示',
    //     {
    //       confirmButtonText: '立即重启',
    //       cancelButtonText: '稍后手动重启',
    //       type: 'warning',
    //     }
    //   )
    //   handleAList('restart')  // 使用已有的 handleAList 函数
    // } catch {
    //   ElMessage.info('请记得稍后手动重启AList服务以使更改生效')
    // }
  } catch (error) {
    ElMessage.error('更新失败：' + (error as Error).message)
  }
}

const updateTmdbApiKey = () => {
  axios.post('/api/settings', {name: 'tmdb_api_key', value: tmdbApiKey.value}).then(() => {
    ElMessage.success('更新成功')
  })
}

const updateDeleteDelayTime = () => {
  axios.post('/api/settings', {name: 'delete_delay_time', value: deleteDelayTime.value}).then(() => {
    ElMessage.success('更新成功')
  })
}

const updateMixed = () => {
  axios.post('/api/settings', {name: 'mix_site_source', value: mixSiteSource.value}).then(() => {
    ElMessage.success('更新成功')
  })
}

const updateReplaceAliToken = () => {
  axios.post('/api/settings', {name: 'replace_ali_token', value: replaceAliToken.value}).then(() => {
    ElMessage.success('更新成功')
  })
}

const updateEnableHttps = () => {
  axios.post('/api/settings', {name: 'enable_https', value: enableHttps.value}).then(() => {
    ElMessage.success('更新成功')
  })
}

const updateDebugLog = () => {
  axios.post('/api/settings', {name: 'debug_log', value: debugLog.value}).then(() => {
    ElMessage.success('更新成功')
  })
}

const updateAListDebug = () => {
  axios.post('/api/settings', {name: 'alist_debug', value: aListDebug.value}).then(() => {
    ElMessage.success('更新成功，重启生效')
  })
}

const updateAliTo115 = () => {
  axios.post('/api/settings', {name: 'ali_to_115', value: aliTo115.value}).then(() => {
    ElMessage.success('更新成功')
  })
}

// ===1027 增加重启自动更新
const updateAutoUpdate = () => {
  axios.post('/api/settings', {name: 'auto_update', value: autoUpdate.value}).then(() => {
    ElMessage.success('更新成功')
  })
}

const updateAliLazyLoad = () => {
  axios.post('/api/settings', {name: 'ali_lazy_load', value: aliLazyLoad.value}).then(() => {
    ElMessage.success('更新成功，重启生效')
  })
}

const updateLogin = () => {
  axios.post('/api/alist/login', login.value).then(({data}) => {
    ElMessage.success('保存成功')
    login.value = data
  })
}

const exportDatabase = () => {
  window.location.href = '/api/settings/export' + '?t=' + new Date().getTime() + '&X-ACCESS-TOKEN=' + localStorage.getItem("token");
}

const updateScheduleTime = () => {
  axios.post('/api/schedule', scheduleTime.value).then(() => {
    ElMessage.success('更新成功')
  })
}

const handleAList = (op: string) => {
  axios.post('/api/alist/' + op).then(() => {
    ElMessage.success('操作成功')
    setTimeout(() => getAListStatus(), 3000)
  })
}

const getAListStatus = () => {
  axios.get('/api/alist/status').then(({data}) => {
    increase()
    store.aListStatus = data
    aListStarted.value = data != 0
    if (data !== 1) {
      clearInterval(intervalId)
      intervalId = 0
    } else if (!intervalId) {
      percentage.value = 0
      intervalId = setInterval(getAListStatus, 1000)
    }
  })
}

// ===1117 添加g-box重启按钮
const showRestartGBox = ref(false);

const checkDockerSocket = async () => {
  try {
    const response = await axios.get('/api/settings/check-docker-socket');
    showRestartGBox.value = response.data;
  } catch (error) {
    console.error('Error checking Docker socket:', error);
  }
};

const handleRestartGBox = async () => {
  ElMessage.success('G-Box 重启操作已执行，配置目录中auto_update.log可查看日志！');
  try {
    await axios.post('/api/settings/restart-gbox');
  } catch (error) {
    console.error('Error restarting G-Box:', error);
    ElMessage.error('G-Box 重启失败，请检查日志或手动重启');
  }
};

// ===1122增加卸载sp按钮
const sunPanelUninstalled = ref(false)

const checkSunPanelStatus = async () => {
  try {
    const { data } = await axios.get('/api/get-sp-url')
    sunPanelUninstalled.value = data.status === 'close'
  } catch (error) {
    console.error('检查sun-panel状态失败:', error)
    ElMessage.error('检查sun-panel状态失败')
  }
}

const handleSunPanelToggle = async () => {
  try {
    const endpoint = sunPanelUninstalled.value ? '/api/restore-sunpanel' : '/api/uninstall-sunpanel'
    await axios.post(endpoint)
    sunPanelUninstalled.value = !sunPanelUninstalled.value
    ElMessage.success('操作成功，重启G-Box容器后生效')
  } catch (error) {
    console.error('切换sun-panel状态失败:', error)
    ElMessage.error('切换sun-panel状态失败')
  }
}

onMounted(() => {
  axios.get('/api/settings').then(({data}) => {
    form.value.token = data.token
    form.value.enabledToken = !!data.token
    scheduleTime.value = data.schedule_time || new Date(2023, 6, 20, 9, 0)
    aListStartTime.value = data.alist_start_time
    // ==========0922 c85
    // fileExpireHour.value = +data.file_expire_hour || 6
    // deleteDelayTime.value = +data.delete_delay_time || 900
    deleteDelayTime.value = +data.delete_delay_time
    movieVersion.value = data.movie_version
    indexVersion.value = data.index_version
    dockerVersion.value = data.docker_version
    appVersion.value = data.app_version
    openTokenUrl.value = data.open_token_url
    dockerAddress.value = data.docker_address
    aliSecret.value = data.ali_secret
    tmdbApiKey.value = data.tmdb_api_key
    autoCheckin.value = data.auto_checkin === 'true'
    aListRestart.value = data.alist_restart_required === 'true'
    replaceAliToken.value = data.replace_ali_token === 'true'
    enableHttps.value = data.enable_https === 'true'
    debugLog.value = data.debug_log === 'true'
    aListDebug.value = data.alist_debug === 'true'
    aliTo115.value = data.ali_to_115 === 'true'
    // ===1027 增加重启自动更新
    autoUpdate.value = data.auto_update === 'true'
    aliLazyLoad.value = data.ali_lazy_load === 'true'
    mixSiteSource.value = data.mix_site_source !== 'false'
    atvPass.value = data.atv_password
    apiClientId.value = data.open_api_client_id || ''
    apiClientSecret.value = data.open_api_client_secret || ''
    login.value.username = data.alist_username
    login.value.password = data.alist_password
    login.value.enabled = data.alist_login === 'true'
    // tooltip.value = 'sudo bash -c "$(curl -fsSL https://d.har01d.cn/update_' + data.install_mode + '.sh)"'
    tooltip.value = 'bash -c "$(curl -sSLf https://gbox.ggbond.org/xy_install.sh)" -s g-box'
    // ==========115 modified 3
    delete115Folder.value = data.delete115Folder === 'true' || false
    // ==========1020 重启保留115删除密码
    cleanPassword.value = data.cleanPassword || ''
    // ===1111 增加一键恢复
    fetchBackupFiles();
    // ===1122增加卸载sp按钮
    checkSunPanelStatus()
  })
  axios.get('/api/alist/status').then(({data}) => {
    store.aListStatus = data
    aListStarted.value = data != 0
    if (data === 1) {
      percentage.value = 0
      intervalId = setInterval(getAListStatus, 1000)
    }
  })
  axios.get('/api/versions').then(({data}) => {
    movieRemoteVersion.value = data.movie
    cachedMovieVersion.value = data.cachedMovie
    indexRemoteVersion.value = data.index
    appRemoteVersion.value = data.app
    changelog.value = data.changelog
  })
  checkDockerSocket(); // ===1117添加这行来检查 Docker socket
})

onUnmounted(() => {
  clearInterval(intervalId)
})
</script>

<style>
.main {
  max-width: 1080px;
}

.el-col {
  margin-left: 24px;
}

.bottom {
  margin-bottom: 0px;
}

.warning {
  color: #e6a23c;
}

.append {
  margin-left: 6px;
  margin-right: 6px;
}

.hint {
  margin-left: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  margin-bottom: 12px;
}

.changelog {
  color: #67c23a;
}

/* ===1021 sync 488 */
@media only screen and (max-width: 1500px) {
  #adv {
    width: 80%;
  }
}
@media only screen and (max-width: 960px) {
  #adv {
    width: 96%;
  }
}
</style>
