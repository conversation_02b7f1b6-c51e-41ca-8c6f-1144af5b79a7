<script setup lang="ts">
import { onMounted, ref } from 'vue';
import axios from 'axios';
import { store } from '@/services/store';

const url = ref('');
const height = ref(window.innerHeight);
const width = ref(window.innerWidth - 55);
const status = ref('open');
const showMenu = ref(false);
const selectedSource = ref('bing'); // 默认值为 'bing'
const autoUpdateInterval = ref<number>(0); // 修改类型为 number
const message = ref('');
const isLoading = ref(false);
const successMessage = ref('');

window.onresize = () => {
  height.value = window.innerHeight;
  width.value = window.innerWidth - 55;
};

const loadBaseUrl = () => {
  axios.get('/api/get-sp-url').then(({ data }) => {
    status.value = data.status;
    const hostname = window.location.hostname;
    const isInternalNetwork = /^192\.168|^10\./.test(hostname);

    if (status.value === 'open') {
      if (isInternalNetwork) {
        url.value = window.location.protocol + '//' + hostname + ':3002';
      } else {
        url.value = data.url || window.location.protocol + '//' + hostname + ':3002';
      }
      // console.log('==========load AList ' + url.value);
    } else if (status.value === 'close') {
      if (isInternalNetwork) {
        url.value = window.location.protocol + '//' + window.location.hostname + ':' + (store.hostmode ? 5678 : 5344);
      } else {
        url.value = data.url;
        if (!url.value) {
          axios.get('/api/sites/1').then(({ data }) => {
            url.value = data.url;
            const re = /http:\/\/localhost:(\d+)/.exec(data.url);
            if (re) {
              url.value = window.location.protocol + '//' + window.location.hostname + ':' + re[1];
              store.baseUrl = url.value;
              // console.log('==========load AList ' + url.value);
            } else if (data.url === 'http://localhost') {
              axios.get('/api/alist/port').then(({ data }) => {
                if (data) {
                  url.value = window.location.protocol + '//' + window.location.hostname + ':' + data;
                  store.baseUrl = url.value;
                  // console.log('==========load AList ' + url.value);
                }
              });
            } else {
              store.baseUrl = url.value;
              // console.log('==========load AList ' + url.value);
            }
          });
        }
      }
      height.value = window.innerHeight;
      width.value = window.innerWidth;
    }
  }).catch(error => {
    // console.error('==========API request failed:', error);
  });
};

const updateBackground = () => {
  isLoading.value = true;
  axios.post('/api/save-image', { source: selectedSource.value }).then(() => {
    isLoading.value = false;
    successMessage.value = '背景更新成功！';
    setTimeout(() => {
      successMessage.value = '';
      location.reload(); // 自动刷新页面
    }, 2000); // 2秒后自动刷新
  }).catch(error => {
    isLoading.value = false;
    console.error('背景更新请求失败:', error);
  });
};

const setAutoUpdate = () => {
  const interval = autoUpdateInterval.value;
  axios.post('/api/settings', { name: 'auto_update_interval', value: autoUpdateInterval.value }).then(() => {
    console.log('自动更新时间已保存');
  }).catch(error => {
    console.error('保存自动更新时间失败:', error);
  });

  axios.post('/api/set-auto-update', { interval, source: selectedSource.value }).then(({ data }) => {
    message.value = data.message;
    setTimeout(() => {
      message.value = '';
      // if (interval > 0) {
      //   location.reload(); // 自动刷新页面
      // }
    }, 2000); // 2秒后自动消失
  }).catch(error => {
    console.error('自动更新设置失败:', error);
  });
};

const saveUserSettings = () => {
  axios.post('/api/settings', { name: 'wallpaper_source', value: selectedSource.value }).then(() => {
    console.log('图片源设置已保存');
  }).catch(error => {
    console.error('保存用户设置失败:', error);
  });
};

const loadUserSettings = () => {
  axios.get('/api/settings/wallpaper_source').then(({ data }) => {
    selectedSource.value = data.value || 'bing';
    // console.log(`==========0926 c4 wallpaper_source: ${selectedSource.value}`);
  }).catch(error => {
    console.error('加载用户设置失败:', error);
  });

  axios.get('/api/settings/auto_update_interval').then(({ data }) => {
    autoUpdateInterval.value = Number(data.value) || 0; // 确保 autoUpdateInterval 是 number 类型
    console.log(`自动更新时间: ${autoUpdateInterval.value}`);
  }).catch(error => {
    console.error('加载自动更新时间失败:', error);
  });
};

onMounted(() => {
  loadBaseUrl();
  loadUserSettings();
});
</script>


<template>
  <div>
    <template v-if="status === 'open'">
      <iframe :src="url" :width="width" :height="height" style="border: none;"></iframe>
      <div class="button-container">
        <button @click="updateBackground" class="update-button">
          <span v-if="!isLoading">更新背景</span>
          <span v-else>加载中...</span>
        </button>
        <button @click="showMenu = !showMenu" class="menu-button">
          <i class="fas fa-cog"></i>
        </button>
      </div>
      <div v-if="showMenu" class="menu">
        <button @click="showMenu = false" class="close-button">×</button>
        <label>
          <input type="radio" value="bing" v-model="selectedSource" @change="saveUserSettings" /> 必应
        </label>
        <label>
          <input type="radio" value="anime" v-model="selectedSource" @change="saveUserSettings" /> 二次元
        </label>
        <div class="update-container">
          <label>更新时间</label>
          <el-input-number v-model="autoUpdateInterval" min="0"></el-input-number> 分钟
          <el-button type="primary" @click="setAutoUpdate">保存</el-button>
        </div>
        <div class="hint">填0表示关闭自动更新背景</div>
      </div>
      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>
      <div v-if="message" class="message">
        {{ message }}
      </div>
    </template>
    <template v-else-if="status === 'close'">
      <h1>G-Box - AList</h1>
      <div v-if="store.xiaoya">
        <el-text size="large">小雅集成版</el-text>
        <el-text v-if="store.hostmode" size="small">host网络模式</el-text>
        <a :href="url" class="hint" target="_blank">{{ url }}</a>
      </div>
      <div v-else>
        <el-text size="large">纯净版</el-text>
        <a :href="url" class="hint" target="_blank">{{ url }}</a>
      </div>
      <iframe v-if="store.aListStatus" :src="url" :width="width" :height="height"></iframe>
    </template>
  </div>
</template>

<style scoped>
.button-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.update-button {
  padding: 10px 20px;
  background-color: rgba(255, 255, 255, 0.3);
  color: #000;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-right: 3px; /* 调整按钮之间的水平距离 */
}

.menu-button {
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.3);
  color: #000;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(0); /* 修改这行代码 */
}

.menu {
  position: fixed;
  bottom: 60px;
  right: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  color: #000;
  width: 20%; /* 缩短宽度 */
}

.update-container {
  display: flex;
  align-items: center;
  gap: 5px
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

.success-message, .message {
  position: fixed;
  bottom: 100px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.hint {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}
</style>





