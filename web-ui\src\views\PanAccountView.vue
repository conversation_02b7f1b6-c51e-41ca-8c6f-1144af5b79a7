<template>
  <div class="list">
    <h1>网盘账号列表</h1>
    <el-row justify="end">
      <el-button @click="load">刷新</el-button>
      <el-button type="primary" @click="handleAdd">添加</el-button>
    </el-row>
    <div class="space"></div>

    <el-table :data="accounts" border style="width: 100%">
      <el-table-column prop="id" label="ID" sortable width="70">
        <template #default="scope">
          {{ scope.row.id + 3900}}
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" sortable width="150">
        <template #default="scope">
          <span v-if="scope.row.type=='QUARK'">夸克网盘</span>
          <span v-else-if="scope.row.type=='UC'">UC网盘</span>
          <span v-else-if="scope.row.type=='QUARK_TV'">夸克TV</span>
          <span v-else-if="scope.row.type=='UC_TV'">UC TV</span>
          <span v-else-if="scope.row.type=='PAN115'">115网盘</span>
          <span v-else-if="scope.row.type=='THUNDER'">迅雷云盘</span>
          <span v-else-if="scope.row.type=='CLOUD189'">天翼云盘</span>
          <span v-else-if="scope.row.type=='PAN139'">移动云盘</span>
          <span v-else-if="scope.row.type=='PAN123'">123网盘</span>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" sortable width="200"/>
      <el-table-column label="路径">
        <template #default="scope">
          {{ fullPath(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column prop="master" label="主账号？" width="120">
        <template #default="scope">
          <el-icon v-if="scope.row.master">
            <Check/>
          </el-icon>
          <el-icon v-else>
            <Close/>
          </el-icon>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="formVisible" :title="dialogTitle" width="60%">
      <el-form :model="form" label-width="140">
        <el-form-item label="名称" label-width="140" required>
          <el-input v-model="form.name" autocomplete="off"/>
        </el-form-item>
        <el-form-item label="类型" label-width="120" required>
          <el-radio-group v-model="form.type" class="ml-4">
            <el-radio label="QUARK" size="large">夸克网盘</el-radio>
            <el-radio label="UC" size="large">UC网盘</el-radio>
            <el-radio label="QUARK_TV" size="large">夸克TV</el-radio>
            <el-radio label="UC_TV" size="large">UC TV</el-radio>
            <el-radio label="PAN115" size="large">115网盘</el-radio>
            <el-radio label="THUNDER" size="large">迅雷云盘</el-radio>
            <el-radio label="CLOUD189" size="large">天翼云盘</el-radio>
            <el-radio label="PAN139" size="large">移动云盘</el-radio>
            <el-radio label="PAN123" size="large">123网盘</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="Cookie" required v-if="form.type=='QUARK'||form.type=='UC'||form.type=='PAN115'">
          <!-- ==========0922 c41 -->
          <el-input v-model="form.cookie" type="textarea" :rows="5" placeholder="Cookie或者Token必填一项"/>
        </el-form-item>
        <!-- ==========0922 c40 -->
        <el-form-item label="Token" v-if="form.type=='PAN139'" required>
          <el-input v-model="form.token" type="textarea" :rows="5"/>
        </el-form-item>
        <el-form-item v-if="form.type=='PAN115'" label="选择客户端" label-width="140">
          <el-select v-model="form.selectedClient" placeholder="选择客户端">
            <el-option v-for="client in clients" :key="client.value" :label="client.label" :value="client.value"></el-option>
          </el-select>
          <el-button type="primary" @click="generateQRCode">扫码获取115 cookie</el-button>
        </el-form-item>

        <!-- ===1108 增加夸克和UC扫码 -->
        <el-form-item v-if="form.type=='QUARK' || form.type=='UC'" label="扫码获取Cookie" label-width="140">
          <el-button type="primary" @click="generateQRCodeForQuarkOrUC">扫码获取Cookie</el-button>
        </el-form-item>


        <!-- ==========0922 c68 -->
        <!-- ==========0922 c81删了又改回来了 --> 
        <el-form-item label="Token" label-width="140" v-if="form.type=='PAN115'">
          <el-input v-model="form.token"/>
        </el-form-item>
        <el-form-item label="Token" v-if="form.type=='QUARK_TV'||form.type=='UC_TV'" required>
          <el-input v-model="form.token" type="textarea" :rows="3"/>
        </el-form-item>
        <el-form-item v-if="form.type=='QUARK_TV'||form.type=='UC_TV'" required>
          <el-button type="primary" @click="showQrCode">扫码获取</el-button>
        </el-form-item>
        <el-form-item label="用户名" v-if="form.type=='THUNDER'||form.type=='CLOUD189'||form.type=='PAN123'" required>
          <el-input v-model="form.username" :placeholder="form.type=='THUNDER'?'手机号要加 +86':''" />
        </el-form-item>
        <el-form-item label="密码" v-if="form.type=='THUNDER'||form.type=='CLOUD189'||form.type=='PAN123'" required>
          <el-input type="password" show-password v-model="form.password"/>
        </el-form-item>
        <el-form-item label="验证码" v-if="form.type=='THUNDER'||form.type=='CLOUD189'">
          <el-input v-model="form.token"/>
        </el-form-item>
        <el-form-item label="保险箱密码" v-if="form.type=='THUNDER'">
          <el-input type="password" show-password v-model="form.safePassword"/>
        </el-form-item>
        <el-form-item label="文件夹ID">
          <el-input v-model="form.folder"/>
        </el-form-item>
        <el-form-item v-if="form.type=='PAN115'" label="本地代理">
          <el-switch
            v-model="form.useProxy"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>
        <el-form-item label="主账号">
          <el-switch
            v-model="form.master"
            inline-prompt
            active-text="是"
            inactive-text="否"
          />
          <span class="hint">主账号用来观看分享。</span>
        </el-form-item>
        <el-form-item v-if="form.type=='QUARK' && form.master" label-width="140">
          <template #label>
            <div class="two-line-label">
              自动更新小雅Alist<br/>启动的4个文件
            </div>
          </template>
          <el-tooltip
            content="此功能通过夸克网盘自动更新小雅Alist启动所需的version.txt/index.zip/tvbox.zip/update.zip四文件，实现快速启动！"
            placement="top"
          >
            <el-switch
              v-model="autoQuarkUpdate"
              inline-prompt
              active-text="开启"
              inactive-text="关闭"
              @change="handleAutoUpdateChange"
            />
          </el-tooltip>
          
          <div v-if="autoQuarkUpdate" class="time-range">
            <span class="time-label">更新时段：</span>
            <el-time-select
              v-model="updateStartTime"
              start="00:00"
              step="01:00"
              end="23:59"
              placeholder="开始时间"
              format="HH:00"
              @change="handleTimeChange"
            />
            <span class="time-separator">至</span>
            <el-time-select
              v-model="updateEndTime"
              start="00:00"
              step="01:00"
              end="23:59"
              placeholder="结束时间"
              format="HH:00"
              @change="handleTimeChange"
            />
          </div>
        </el-form-item>
        <span v-if="form.name">完整路径： {{ fullPath(form) }}</span>
      </el-form>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">{{ updateAction ? '更新' : '添加' }}</el-button>
      </span>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible" title="删除网盘账号" width="30%">
      <p>是否删除网盘账号 - {{ form.id + 3900 }}</p>
      <p> {{ getTypeName(form.type) }} ： {{ form.name }}</p>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="danger" @click="deleteAccount">删除</el-button>
      </span>
      </template>
    </el-dialog>

    <el-dialog v-model="qrModel" title="扫码登陆" width="40%">
      <img alt="qr" :src="'data:image/jpeg;base64,' + qr.qr_data" />
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="qrModel=false">取消</el-button>
        <el-button @click="showQrCode">刷新二维码</el-button>
        <el-button type="primary" @click="getRefreshToken">我已扫码</el-button>
      </span>
      </template>
    </el-dialog>
  
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {Check, Close} from '@element-plus/icons-vue'
import axios from "axios"
import {ElMessage} from "element-plus";
// ===1203添加115cookie更新重启弹窗
import { ElMessageBox } from 'element-plus'
import router from "@/router"

interface PanAccount {
  id: number;
  type: string;
  name: string;
  cookie: string;
  token: string;
  username: '',
  password: '',
  safePassword: '',
  folder: string;
  useProxy: boolean;
  master: boolean;
  selectedClient?: string;
}

const updateAction = ref(false)
const dialogTitle = ref('')
const accounts = ref<PanAccount[]>([])
const formVisible = ref(false)
const dialogVisible = ref(false)
const qrModel = ref(false)
const form = ref<PanAccount>({
  id: 0,
  type: 'QUARK',
  name: '',
  cookie: '',
  token: '',
  username: '',
  password: '',
  safePassword: '',
  folder: '',
  useProxy: false,
  master: false,
  selectedClient: 'wechatmini' // ==========0922 c42,设置默认值为微信小程序
})
const qr = ref({
  qr_data: '',
  query_token: '',
})

// ==========0922 c43
const clients = ref([
  { value: 'web', label: '网页' },
  { value: 'ios', label: '115(i0S端)' },
  { value: 'tv', label: '115网盘(Android电视端)' },
  { value: 'qandroid', label: '115安卓管理端' },
  { value: 'wechatmini', label: '115生活(微信小程序)' },
  { value: 'alipaymini', label: '115生活(支付宝小程序)' }
]);

const autoQuarkUpdate = ref(false)
const updateStartTime = ref('00:00')
const updateEndTime = ref('23:59')

// 添加临时状态变量
const tempAutoUpdate = ref(false)
const tempStartTime = ref('00:00')
const tempEndTime = ref('23:59')

const handleAdd = () => {
  dialogTitle.value = '添加网盘账号'
  updateAction.value = false
  form.value = {
    id: 0,
    type: 'QUARK',
    name: '',
    cookie: '',
    token: '',
    username: '',
    password: '',
    safePassword: '',
    folder: '',
    useProxy: false,
    master: false,
    selectedClient: 'wechatmini' // ==========0922 c44,新增字段
  }
  formVisible.value = true
  
  // 同步临时状态为当前显示状态
  tempAutoUpdate.value = autoQuarkUpdate.value
  tempStartTime.value = updateStartTime.value
  tempEndTime.value = updateEndTime.value
}

const getTypeName = (type: string) => {
  if (type == 'QUARK') {
    return '夸克网盘'
  }
  if (type == 'UC') {
    return 'UC网盘'
  }
  if (type == 'QUARK_TV') {
    return '夸克TV网盘'
  }
  if (type == 'UC_TV') {
    return 'UC TV网盘'
  }
  if (type == 'PAN115') {
    return '115网盘'
  }
  if (type == 'THUNDER') {
    return '迅雷云盘'
  }
  if (type == 'CLOUD189') {
    return '天翼云盘'
  }
  if (type == 'PAN139') {
    return '移动云盘'
  }
  if (type == 'PAN123') {
    return '123网盘'
  }
  return '未知'
}

const fullPath = (share: any) => {
  const path = share.name;
  if (path.startsWith('/')) {
    return path
  }
  if (share.type == 'QUARK') {
    return '/\uD83C\uDF52我的夸克网盘/' + path
  } else if (share.type == 'UC') {
    return '/\uD83C\uDF4D我的UC网盘/' + path
  } else if (share.type == 'QUARK_TV') {
    return '/\uD83C\uDF46夸克网盘TV/' + path
  } else if (share.type == 'UC_TV') {
    return '/\uD83C\uDF46UC网盘TV/' + path
  } else if (share.type == 'PAN115') {
    return '/\uD83E\uDD5D115网盘/' + path
  } else if (share.type == 'THUNDER') {
    return '/\uD83C\uDF50我的迅雷云盘/' + path
  } else if (share.type == 'CLOUD189') {
    return '/\uD83C\uDF45我的天翼云盘/' + path
  } else if (share.type == 'PAN139') {
    return '/\uD83C\uDF4B我的移动云盘/' + path
  } else if (share.type == 'PAN123') {
    return '/\uD83C\uDF4F我的123网盘/' + path
  } else {
    return '/\uD83C\uDF48网盘/' + path
  }
}

const handleEdit = (data: any) => {
  dialogTitle.value = '更新网盘账号 - ' + data.name
  updateAction.value = true
  form.value = Object.assign({}, data)
  formVisible.value = true
  
  // 同步临时状态为当前显示状态
  tempAutoUpdate.value = autoQuarkUpdate.value
  tempStartTime.value = updateStartTime.value
  tempEndTime.value = updateEndTime.value
}

const handleDelete = (data: any) => {
  form.value = data
  dialogVisible.value = true
}

const deleteAccount = () => {
  dialogVisible.value = false
  axios.delete('/api/pan/accounts/' + form.value.id).then(() => {
    load()
  })
}

const handleCancel = () => {
  formVisible.value = false
}

// 修改处理函数，分别发送每个设置项
const handleAutoUpdateChange = (value: boolean): void => {
  tempAutoUpdate.value = value  // 直接使用用户切换的值
}

const handleTimeChange = (): void => {
  // 直接使用用户选择的时间值
  tempStartTime.value = updateStartTime.value
  tempEndTime.value = updateEndTime.value
}

// 修改确认函数，在确认时也需要更新文件状态
const handleConfirm = () => {
  const url = updateAction.value ? '/api/pan/accounts/' + form.value.id : '/api/pan/accounts'
  axios.post(url, form.value).then(async () => {
    formVisible.value = false
    
    // 处理夸克网盘账户的设置
    if (form.value.type === 'QUARK') {
      try {
        // 如果是夸克网盘且不是主账号，关闭自动更新
        if (!form.value.master) {
          // 关闭自动更新设置
          await axios.post('/api/settings', {
            name: 'auto_quark_update',
            value: 'false'
          })
          
          // 重置时间设置
          await axios.post('/api/settings', {
            name: 'quark_update_start_time',
            value: '00:00'
          })
          
          await axios.post('/api/settings', {
            name: 'quark_update_end_time',
            value: '23:59'
          })

          // 发送请求停止自动更新任务
          await axios.post('/api/index/stop-quark-update')
          
          // 更新文件状态为关闭
          await axios.post('/api/index/update-quark-file-status', { enabled: false })

          // 更新显示状态
          autoQuarkUpdate.value = false
          updateStartTime.value = '00:00'
          updateEndTime.value = '23:59'
          tempAutoUpdate.value = false
          tempStartTime.value = '00:00'
          tempEndTime.value = '23:59'
        } else {
          // 如果是主账号，则按原来的逻辑处理
          await axios.post('/api/settings', {
            name: 'auto_quark_update',
            value: tempAutoUpdate.value.toString()
          })
          
          await axios.post('/api/settings', {
            name: 'quark_update_start_time',
            value: tempStartTime.value
          })
          
          await axios.post('/api/settings', {
            name: 'quark_update_end_time',
            value: tempEndTime.value
          })
          
          // 更新文件状态
          try {
            const response = await axios.post('/api/index/update-quark-file-status', { enabled: tempAutoUpdate.value });
          } catch (error: any) {
            ElMessage.error('更新夸克文件状态失败: ' + (error.response?.data || error.message || '未知错误'));
          }
          
          // 如果开启了自动更新，执行更新
          if (tempAutoUpdate.value) {
            try {
              const response = await axios.post('/api/index/update-quark-files');
            } catch (error: any) {
              ElMessage.error('更新夸克文件失败: ' + (error.response?.data || error.message || '未知错误'));
            }
          }
          
          // 更新显示状态
          autoQuarkUpdate.value = tempAutoUpdate.value
          updateStartTime.value = tempStartTime.value
          updateEndTime.value = tempEndTime.value
        }
      } catch (error) {
        ElMessage.error('更新设置失败')
      }
    }

    if (accounts.value.length === 0) {
      ElMessage.success('添加成功')
    } else {
      ElMessage.success('更新成功')
    }
    
    load()
  })
}

// ===1203添加115cookie更新重启弹窗
// ===1221取消重启弹窗
// const handleConfirm = async () => {
//   try {
//     const url = updateAction.value ? '/api/pan/accounts/' + form.value.id : '/api/pan/accounts'
//     await axios.post(url, form.value)
//     formVisible.value = false
    
//     // 只有更新主账号或新增主账号时才显示重启确认弹窗
//     if (form.value.master && (accounts.value.length === 0 || updateAction.value)) {
//       try {
//         await ElMessageBox.confirm(
//           '主账号已' + (updateAction.value ? '更新' : '添加') + '，需要重启AList才能生效。是否立即重启？',
//           '提示',
//           {
//             confirmButtonText: '立即重启',
//             cancelButtonText: '稍后手动重启',
//             type: 'warning',
//           }
//         )
//         // 发送重启命令
//         await axios.post('/api/alist/restart')
//         ElMessage.success('AList重启中')
//         setTimeout(() => router.push('/wait'), 1000)
//       } catch {
//         ElMessage.info('请记得稍后手动重启AList服务以使更改生效')
//       }
//     } else {
//       // 其他情况显示普通成功提示
//       ElMessage.success(updateAction.value ? '更新成功' : '添加成功')
//     }
//     load()
//   } catch (error) {
//     ElMessage.error('操作失败：' + (error as Error).message)
//   }
// }

const showQrCode = () => {
  axios.post('/api/pan/accounts/-/qr?type=' + form.value.type).then(({data}) => {
    qr.value = data
    qrModel.value = true
  })
}
 
const getRefreshToken = () => {
  axios.post('/api/pan/accounts/-/token?type=' + form.value.type + '&queryToken=' + qr.value.query_token).then(({data}) => {
    form.value.token = data
    qrModel.value = false
  })
}

const load = () => {
  // 获取账号列表
  axios.get('/api/pan/accounts').then(({data}) => {
    accounts.value = data
  })
  
  // 获取自动更新设置
  axios.get('/api/settings').then(({data}) => {
    autoQuarkUpdate.value = data.auto_quark_update === 'true'
    updateStartTime.value = data.quark_update_start_time || '00:00'
    updateEndTime.value = data.quark_update_end_time || '23:59'
    
    // 初始化临时状态，与显示状态保持一致
    tempAutoUpdate.value = autoQuarkUpdate.value
    tempStartTime.value = updateStartTime.value
    tempEndTime.value = updateEndTime.value
  }).catch(error => {
    console.error('获取自动更新设置失败:', error)
  })
}

// ==========0922 c45
const generateQRCode = () => {
  axios.get('/api/qrcode', { params: { client: form.value.selectedClient } }).then(({ data }) => {
    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <div>
          <img src="${data.qrcode}" alt="QR Code" />
          <p>请用115手机APP扫码获取cookie</p>
        </div>
      `);
    } else {
      console.error("Failed to open new window.");
    }
    pollQRCodeStatus(data.uid, data.time, data.sign, data.client, newWindow);
  }).catch(error => {
    console.error("Error generating QR code:", error);
  });
};

// ==========0922 c46
// ===1107 优化轮询
const pollQRCodeStatus = (uid: string, time: string, sign: string, client: string, newWindow: Window | null) => {
  const startTime = Date.now();
  const poll = () => {
    axios.get(`/api/status?uid=${uid}&time=${time}&sign=${sign}&client=${client}`)
      .then(({ data }) => {
        if (data.status === 'success') {
          form.value.cookie = data.cookie;
          if (newWindow) {
            newWindow.close();
          }
          ElMessage.success('扫码成功，Cookie已获取');
        } else {
          if (Date.now() - startTime < 60000) { // 1分钟内继续轮询
            setTimeout(poll, 3000); // 每3秒检查一次
          } else {
            if (newWindow) {
              newWindow.close();
            }
            ElMessage.error('扫码失败，请重新获取二维码');
          }
        }
      }).catch(error => {
        console.error('Error polling QR code status:', error);
        if (newWindow) {
          newWindow.close();
        }
        ElMessage.error('获取二维码状态失败，请重试');
      });
  };
  setTimeout(poll, 3000);
};

const generateQRCodeForQuarkOrUC = () => {
  const apiEndpoint = form.value.type === 'QUARK' ? '/api/qrcode_quark' : '/api/qrcode_uc';
  axios.get(apiEndpoint, { responseType: 'arraybuffer' }).then(({ data, headers }) => {
    const blob = new Blob([data], { type: 'image/png' });
    const url = URL.createObjectURL(blob);
    // console.log("Generated QR Code URL:", url); // 添加日志记录

    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(`
        <div>
          <img src="${url}" alt="QR Code" />
          <p>请用手机APP扫码获取cookie</p>
        </div>
      `);
    } else {
      console.error("Failed to open new window.");
    }
    const token = headers['token'];
    pollQRCodeStatusForQuarkOrUC(newWindow, token);
  }).catch(error => {
    console.error("Error generating QR code:", error);
  });
};

const pollQRCodeStatusForQuarkOrUC = (newWindow: Window | null, token:string) => {
  const startTime = Date.now();
  const poll = () => {
    const apiEndpoint = form.value.type === 'QUARK' ? `/api/status_quark?token=${token}` : `/api/status_uc?token=${token}`;
    axios.get(apiEndpoint)
      .then(({ data }) => {
        if (data.status === 'success') {
          form.value.cookie = data.cookie;
          if (newWindow) {
            newWindow.close();
          }
          ElMessage.success('扫码成功，Cookie已获取');
        } else {
          if (Date.now() - startTime < 60000) { // 1分钟内继续轮询
            setTimeout(poll, 3000); // 每3秒检查一次
          } else {
            if (newWindow) {
              newWindow.close();
            }
            ElMessage.error('扫码失败，请重新获取二维码');
          }
        }
      }).catch(error => {
        console.error('Error polling QR code status:', error);
        if (newWindow) {
          newWindow.close();
        }
        ElMessage.error('获取二维码状态失败，请重试');
      });
  };
  setTimeout(poll, 3000);
};

onMounted(() => {
  load()
})
</script>

<style scoped>
.space {
  margin-bottom: 6px;
}

.json pre {
  height: 600px;
  overflow: scroll;
}

.time-range {
  margin-top: 10px;
  margin-left: 10px;
}
.time-label {
  margin-right: 10px;
}
.time-separator {
  margin: 0 10px;
}

.two-line-label {
  line-height: 1.2;
  text-align: right;
  margin-right: 12px;
  min-width: 140px;
}
</style>
