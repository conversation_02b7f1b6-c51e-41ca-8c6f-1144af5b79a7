<template>
  <h2>资源列表</h2>
  <el-row justify="end">
    <el-button type="success" @click="uploadVisible=true">导入</el-button>
    <el-button type="success" @click="exportVisible=true">导出</el-button>
    <!--    <el-button type="success" @click="reload" title="点击获取最新地址">Tacit0924</el-button>-->
    <el-button @click="refreshShares">刷新</el-button>
    <el-button type="primary" @click="handleAdd">添加</el-button>
    <el-button type="danger" @click="handleDeleteBatch" v-if="multipleSelection.length">删除</el-button>
  </el-row>

  <el-table :data="shares" border @selection-change="handleSelectionChange" style="width: 100%">
    <el-table-column type="selection" width="55"/>
    <el-table-column prop="id" label="ID" width="70" sortable/>
    <el-table-column prop="path" label="路径" sortable/>
    <el-table-column label="完整路径" width="380" sortable>
      <template #default="scope">
        {{ fullPath(scope.row) }}
      </template>
    </el-table-column>
    <el-table-column prop="url" label="分享链接">
      <template #default="scope">
        <a v-if="scope.row.type==1" :href="getShareLink(scope.row)" target="_blank">
          https://mypikpak.com/s/{{ scope.row.shareId }}
        </a>
        <a v-else-if="scope.row.type==0" :href="getShareLink(scope.row)" target="_blank">
          https://www.alipan.com/s/{{ scope.row.shareId }}
        </a>
        <a v-else-if="scope.row.type==5" :href="getShareLink(scope.row)" target="_blank">
          https://pan.quark.cn/s/{{ scope.row.shareId }}
        </a>
        <a v-else-if="scope.row.type==7" :href="getShareLink(scope.row)" target="_blank">
          https://fast.uc.cn/s/{{ scope.row.shareId }}
        </a>
        <a v-else-if="scope.row.type==8" :href="getShareLink(scope.row)" target="_blank">
          https://115.com/s/{{ scope.row.shareId }}
        </a>
        <a v-else-if="scope.row.type==9" :href="getShareLink(scope.row)" target="_blank">
          https://cloud.189.cn/t/{{ scope.row.shareId }}
        </a>
        <a v-else-if="scope.row.type==2" :href="getShareLink(scope.row)" target="_blank">
          https://pan.xunlei.com/s/{{ scope.row.shareId }}
        </a>
        <a v-else-if="scope.row.type==3" :href="getShareLink(scope.row)" target="_blank">
          https://www.123pan.com/s/{{ scope.row.shareId }}
        </a>
      </template>
    </el-table-column>
    <el-table-column prop="password" label="密码" width="180"/>
    <el-table-column prop="type" label="类型" width="150" sortable>
      <template #default="scope">
        <span v-if="scope.row.type==1">PikPak分享</span>
        <!-- ==========0922 c29 -->
        <!-- <span v-else-if="scope.row.type==2">夸克网盘</span>
        <span v-else-if="scope.row.type==6">UC网盘</span>
        <span v-else-if="scope.row.type==3">115网盘</span> -->
        <span v-else-if="scope.row.type==4">本地存储</span>
        <span v-else-if="scope.row.type==5">夸克分享</span>
        <span v-else-if="scope.row.type==7">UC分享</span>
        <span v-else-if="scope.row.type==8">115分享</span>
        <span v-else-if="scope.row.type==9">天翼分享</span>
        <span v-else-if="scope.row.type==2">迅雷分享</span>
        <span v-else-if="scope.row.type==3">123分享</span>
        <span v-else>阿里分享</span>
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作" width="200">
      <template #default="scope">
        <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
        <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div>
    <el-pagination layout="total, prev, pager, next, jumper, sizes" :current-page="page" :page-size="size"
                   :total="total"
                   @current-change="loadShares" @size-change="handleSizeChange"/>
  </div>

  <div class="space"></div>
  <h2>失败资源</h2>
  <el-row justify="end">
    <el-button @click="refreshStorages">刷新</el-button>
    <el-button type="danger" @click="dialogVisible1=true" v-if="selectedStorages.length">删除</el-button>
  </el-row>
  <el-table :data="storages" border @selection-change="handleSelectionStorages" style="width: 100%">
    <el-table-column type="selection" width="55"/>
    <el-table-column prop="id" label="ID" width="70"/>
    <el-table-column prop="mount_path" label="路径"/>
    <el-table-column prop="status" label="状态" width="260"/>
    <el-table-column prop="driver" label="类型" width="120">
      <template #default="scope">
        <span v-if="scope.row.driver=='AliyundriveShare2Open'">阿里分享</span>
        <span v-else-if="scope.row.driver=='PikPakShare'">PikPak分享</span>
        <!-- ==========0922 c28 -->
        <!-- <span v-else-if="scope.row.driver=='Quark'">夸克网盘</span>
        <span v-else-if="scope.row.driver=='UC'">UC网盘</span> -->
        <span v-else-if="scope.row.driver=='QuarkShare'">夸克分享</span>
        <span v-else-if="scope.row.driver=='UCShare'">UC分享</span>
        <!-- <span v-else-if="scope.row.driver=='115 Cloud'">115网盘</span> -->
        <span v-else-if="scope.row.driver=='115 Share'">115分享</span>
        <span v-else-if="scope.row.driver=='189Share'">天翼分享</span>
        <span v-else-if="scope.row.driver=='ThunderShare'">迅雷分享</span>
        <span v-else-if="scope.row.driver=='123PanShare'">123分享</span>
        <span v-else-if="scope.row.driver=='Local'">本地存储</span>
        <span v-else-if="scope.row.driver=='Alias'">别名</span>
        <span v-else>{{ scope.row.driver }}</span>
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作" width="130">
      <template #default="scope">
        <el-button link type="primary" size="small" @click="reloadStorage(scope.row.id)">重新加载</el-button>
        <el-button link type="danger" size="small" @click="handleDeleteStorage(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div>
    <el-pagination layout="total, prev, pager, next, jumper, sizes" :current-page="page1" :total="total1"
                   :page-size="size1"
                   @current-change="loadStorages" @size-change="handleSize1Change"/>
  </div>

  <el-dialog v-model="formVisible" :title="dialogTitle">
    <el-form :model="form">
      <el-form-item label="挂载路径" label-width="140" required>
        <el-input v-model="form.path" placeholder="直接填挂载目录名即可，以/开头会挂载在首页根目录" autocomplete="off"/>
      </el-form-item>
      <el-form-item v-if="form.type!=4" label="分享ID" label-width="140" required>
        <el-input v-model="form.shareId" autocomplete="off" placeholder="分享ID或者分享链接，如：https://pan.quark.cn/s/3b7f0fb42c0e或3b7f0fb42c0e均可"/>
      </el-form-item>
      <el-form-item v-if="form.type!=4" :label="form.type==3?'Token':'密码'" label-width="140">
        <el-input v-model="form.password" placeholder="有就填，没有就不填！" autocomplete="off"/>
      </el-form-item>
      <el-form-item :label="form.type==4?'本地路径':'文件夹ID'" label-width="140">
        <el-input v-model="form.folderId" autocomplete="off" :placeholder="form.type==4?'':'留空默认为根目录或者从分享链接读取'"/>
      </el-form-item>
      <el-form-item label="类型" label-width="140">
        <el-radio-group v-model="form.type" class="ml-4">
          <el-radio :label="0" size="large">阿里分享</el-radio>
          <el-radio :label="1" size="large">PikPak分享</el-radio>
          <!-- ==========0922 c30 -->
          <!-- <el-radio :label="2" size="large">夸克网盘</el-radio> -->
          <el-radio :label="5" size="large">夸克分享</el-radio>
          <!-- <el-radio :label="6" size="large">UC网盘</el-radio> -->
          <el-radio :label="7" size="large">UC分享</el-radio>
          <!-- <el-radio :label="3" size="large">115网盘</el-radio> -->
          <el-radio :label="8" size="large">115分享</el-radio>
          <el-radio :label="9" size="large">天翼分享</el-radio>
          <el-radio :label="2" size="large">迅雷分享</el-radio>
          <el-radio :label="3" size="large">123分享</el-radio>
          <el-radio :label="4" size="large">本地存储</el-radio>
        </el-radio-group>
      </el-form-item>
      <span v-if="form.path">完整路径： {{ fullPath(form) }}</span>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">{{ updateAction ? '更新' : '添加' }}</el-button>
      </span>
    </template>
  </el-dialog>


  <el-dialog v-model="dialogVisible" title="删除资源" width="30%">
    <div v-if="batch">
      <p>是否删除选中的{{ multipleSelection.length }}个资源?</p>
    </div>
    <div v-else>
      <p>是否删除资源 - {{ form.shareId }}</p>
      <p>{{ form.path }}</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="danger" @click="deleteSub">删除</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="dialogVisible1" title="删除资源" width="30%">
    <div v-if="selectedStorages.length">
      <p>是否删除选中的{{ selectedStorages.length }}个资源?</p>
    </div>
    <div v-else>
      <p>是否删除资源 - {{ storage.id }}</p>
      <p>{{ storage.mount_path }}</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible1 = false">取消</el-button>
        <el-button type="danger" @click="deleteStorage">删除</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="uploadVisible" title="导入分享" width="50%">
    <el-form>
      <el-form-item label="类型" label-width="140">
        <el-radio-group v-model="sharesDto.type" class="ml-4">
          <el-radio :label="0" size="large">阿里分享</el-radio>
          <el-radio :label="1" size="large">PikPak分享</el-radio>
          <el-radio :label="5" size="large">夸克分享</el-radio>
          <el-radio :label="7" size="large">UC分享</el-radio>
          <el-radio :label="8" size="large">115分享</el-radio>
          <el-radio :label="9" size="large">天翼分享</el-radio>
          <el-radio :label="2" size="large">迅雷分享</el-radio>
          <el-radio :label="3" size="large">123分享</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="分享内容" label-width="120">
        <el-input v-model="sharesDto.content" type="textarea" :rows="15" placeholder="多行分享，挂载路径+分享链接"/>
      </el-form-item>
      <el-progress v-if="uploading" :percentage="100" status="success" :indeterminate="true" :duration="5"/>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="uploadVisible = false">取消</el-button>
        <el-button class="ml-3" type="success" :disabled="uploading" @click="submitUpload">导入</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="exportVisible" title="导出分享" width="50%">
    <el-form-item label="类型" label-width="140">
      <el-radio-group v-model="form.type" class="ml-4">
        <el-radio :label="0" size="large">阿里分享</el-radio>
        <el-radio :label="1" size="large">PikPak分享</el-radio>
        <el-radio :label="5" size="large">夸克分享</el-radio>
        <el-radio :label="7" size="large">UC分享</el-radio>
        <el-radio :label="8" size="large">115分享</el-radio>
        <el-radio :label="9" size="large">天翼分享</el-radio>
        <el-radio :label="2" size="large">迅雷分享</el-radio>
        <el-radio :label="3" size="large">123分享</el-radio>
      </el-radio-group>
    </el-form-item>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="exportVisible = false">取消</el-button>
        <el-button class="ml-3" type="success" @click="exportShares">导出</el-button>
      </span>
    </template>
  </el-dialog>

</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import axios from "axios";
import {ElMessage} from 'element-plus'
import accountService from "@/services/account.service";

const token = accountService.getToken()

interface ShareInfo {
  id: string
  path: string
  shareId: string
  folderId: string
  password: string
  cookie: string
  status: string
  type: number
}

interface Storage {
  id: number
  mount_path: string
  driver: string
  status: string
  addition: string
}

const multipleSelection = ref<ShareInfo[]>([])
const storages = ref<Storage[]>([])
const selectedStorages = ref<Storage[]>([])
const storage = ref<Storage>({
  id: 0,
  mount_path: '',
  driver: '',
  status: '',
  addition: ''
})
const page = ref(1)
const page1 = ref(1)
const size = ref(20)
const size1 = ref(20)
const total = ref(0)
const total1 = ref(0)
const shares = ref([])
const dialogTitle = ref('')
const formVisible = ref(false)
const uploadVisible = ref(false)
const uploading = ref(false)
const exportVisible = ref(false)
const dialogVisible = ref(false)
const dialogVisible1 = ref(false)
const updateAction = ref(false)
const batch = ref(false)
const form = ref({
  id: '',
  path: '',
  shareId: '',
  folderId: '',
  password: '',
  cookie: '',
  type: 0,
  // selectedClient: 'wechatmini' // 设置默认值为微信小程序
})
const sharesDto = ref({
  content: '',
  type: 0
})

// 新增的二维码弹出层相关的data
// const qrCodeDialogVisible = ref(false);
// const qrCodeImage = ref('');

// const clients = ref([
//   { value: 'web', label: '网页' },
//   { value: 'ios', label: '115(i0S端)' },
//   { value: 'tv', label: '115网盘(Android电视端)' },
//   { value: 'qandroid', label: '115安卓管理端' },
//   { value: 'wechatmini', label: '115生活(微信小程序)' },
//   { value: 'alipaymini', label: '115生活(支付宝小程序)' }
// ]);

// // ==========添加生成二维码和轮询状态的函数
// const generateQRCode = () => {
//   axios.get('/api/qrcode', { params: { client: form.value.selectedClient } }).then(({ data }) => {
//     const newWindow = window.open();
//     if (newWindow) {
//       newWindow.document.write(`
//         <div>
//           <img src="${data.qrcode}" alt="QR Code" />
//           <p>请用115手机APP扫码获取cookie</p>
//         </div>
//       `);
//     } else {
//       console.error("Failed to open new window.");
//     }
//     pollQRCodeStatus(data.uid, data.time, data.sign, data.client, newWindow); // 传递 client 参数
//   }).catch(error => {
//     console.error("Error generating QR code:", error);
//   });
// };

// const pollQRCodeStatus = (uid: string, time: string, sign: string, client: string, newWindow: Window | null) => {
//   let intervalActive = true; // 添加一个标志变量
//   const interval = setInterval(() => {
//     axios.get(`/api/status?uid=${uid}&time=${time}&sign=${sign}&client=${form.value.selectedClient}`, { timeout: 2000 }) // 设置1秒超时
//       .then(({ data }) => {
//       if (data.status === 'success') {
//         console.log("Interval cleared for success");
//         clearInterval(interval);
//         intervalActive = false; // 更新标志变量
//         ElMessage.success('扫码成功，Cookie已获取');
//         form.value.cookie = data.cookie;
//         if (newWindow) {
//           newWindow.close(); // 关闭新弹出的窗口
//         }
//       } else if (data.status === 'failure') {
//         clearInterval(interval);
//         intervalActive = false; // 更新标志变量
//         ElMessage.error('扫码失败，请手动添加');
//         if (newWindow) {
//           newWindow.close(); // 关闭新弹出的窗口
//         }
//       } else if (data.status === 'pending') {
//         // 二维码还没有被扫描，继续轮询
//         console.log("intervalActive now is：",intervalActive);
//       } else if (data.status === 'waiting') {
//         // 二维码已被扫描，等待确认
//         ElMessage.info('二维码已被扫描，请在手机上确认');
//       }
//     }).catch(error => {
//       if (error.code === 'ECONNABORTED') {
//         console.log("Request timed out, continuing polling...");
//       } else {
//         console.error("Error polling QR code status:", error);
//         if (error.response) {
//           console.error("Error response data:", error.response.data);
//         }
//         // clearInterval(interval); // 确保在发生错误时停止轮询
//         // if (newWindow) {
//         //   newWindow.close(); // 关闭新弹出的窗口
//         // }
//       }
//     });
//   }, 2000);

//   // 设置超时机制，2分钟后停止轮询
//   setTimeout(() => {
//     if (intervalActive) { // 检查标志变量
//       clearInterval(interval);
//       ElMessage.warning('扫码超时，请重新尝试');
//       if (newWindow) {
//         newWindow.close(); // 关闭新弹出的窗口
//       }
//     }
//   }, 1 * 60 * 1000); // 2分钟
// };


const handleAdd = () => {
  dialogTitle.value = '添加分享'
  updateAction.value = false
  form.value = {
    id: '',
    path: '',
    shareId: '',
    folderId: '',
    password: '',
    cookie: '',
    type: 0,
    // selectedClient: '' // 新增字段
  }
  formVisible.value = true
}

const handleEdit = (data: ShareInfo) => {
  dialogTitle.value = '更新分享 - ' + data.id
  updateAction.value = true
  form.value = {
    id: data.id,
    path: data.path,
    shareId: data.shareId,
    folderId: data.folderId,
    password: data.password,
    cookie: data.cookie,
    type: data.type,
    // selectedClient: '' // 新增字段
  }
  formVisible.value = true
}

const handleDelete = (data: any) => {
  batch.value = false
  form.value = data
  dialogVisible.value = true
}

const handleDeleteStorage = (data: any) => {
  storage.value = data
  dialogVisible1.value = true
}

const handleDeleteBatch = () => {
  batch.value = true
  dialogVisible.value = true
}

const deleteSub = () => {
  dialogVisible.value = false
  if (batch.value) {
    axios.post('/api/delete-shares', multipleSelection.value.map(s => s.id)).then(() => {
      loadShares(page.value)
    })
  } else {
    axios.delete('/api/shares/' + form.value.id).then(() => {
      loadShares(page.value)
    })
  }
}

const deleteStorage = () => {
  dialogVisible1.value = false
  if (selectedStorages.value.length) {
    axios.post('/api/delete-shares', selectedStorages.value.map(s => s.id)).then(() => {
      loadStorages(page1.value)
    })
  } else {
    axios.delete('/api/shares/' + storage.value.id).then(() => {
      loadStorages(page1.value)
    })
  }
}

const handleCancel = () => {
  formVisible.value = false
}

const fullPath = (share: any) => {
  const path = share.path;
  if (path.startsWith('/')) {
    return path
  }
  if (share.type == 1) {
    return '\uD83C\uDF4E我的PikPak分享/' + path
  // ==========0922 c31
  // } else if (share.type == 2) {
  //   return '/\uD83C\uDF52我的夸克网盘/' + path
  // } else if (share.type == 6) {
  //   return '/\uD83C\uDF4D我的UC网盘/' + path
  } else if (share.type == 5) {
    return '/\uD83C\uDF4A我的夸克分享/' + path
  } else if (share.type == 7) {
    return '/\uD83C\uDF53我的UC分享/' + path
  } else if (share.type == 8) {
    return '/\uD83C\uDF4C我的115分享/' + path
  } else if (share.type == 9) {
    return '/\uD83C\uDF45我的天翼分享/' + path
  } else if (share.type == 2) {
    return '/\uD83C\uDF50我的迅雷分享/' + path
  } else if (share.type == 3) {
    return '/\uD83C\uDF4F我的123分享/' + path
  } else if (share.type == 4) {
    return path
  } else {
    return '/\uD83C\uDF51我的阿里分享/' + path
  }
}

const handleConfirm = () => {
  axios.post('/api/shares/' + form.value.id, form.value).then(() => {
    formVisible.value = false
    loadShares(page.value)
  })
}

const getShareLink = (shareInfo: ShareInfo) => {
  let url = ''
  if (shareInfo.type == 1) {
    url = 'https://mypikpak.com/s/' + shareInfo.shareId
  } else if (shareInfo.type == 5) {
    url = 'https://pan.quark.cn/s/' + shareInfo.shareId
  } else if (shareInfo.type == 7) {
    url = 'https://fast.uc.cn/s/' + shareInfo.shareId
  } else if (shareInfo.type == 8) {
    url = 'https://115.com/s/' + shareInfo.shareId
  } else if (shareInfo.type == 9) {
    url = 'https://cloud.189.cn/t/' + shareInfo.shareId
  } else if (shareInfo.type == 2) {
    url = 'https://pan.xunlei.com/s/' + shareInfo.shareId
  } else if (shareInfo.type == 3) {
    url = 'https://www.123pan.com/s/' + shareInfo.shareId
  } else {
    url = 'https://www.alipan.com/s/' + shareInfo.shareId
    if (shareInfo.folderId) {
      url = url + '/folder/' + shareInfo.folderId
    }
  }
  if (shareInfo.password) {
    if (shareInfo.type == 2) {
       url = url + '?pwd=' + shareInfo.password
     } else {
       url = url + '?password=' + shareInfo.password
     }
  }
  return url
}

const loadShares = (value: number) => {
  page.value = value
  axios.get('/api/shares?page=' + (page.value - 1) + '&size=' + size.value).then(({data}) => {
    shares.value = data.content
    total.value = data.totalElements
  })
}

const loadStorages = (value: number) => {
  page1.value = value
  axios.get('/api/storages?page=' + page1.value + '&size=' + size1.value).then(({data}) => {
    storages.value = data.data.content
    total1.value = data.data.total
  })
}

const reloadStorage = (id: number) => {
  axios.post('/api/storages/' + id).then(({data}) => {
    if (data.code == 200) {
      ElMessage.success('加载成功')
      loadStorages(page1.value)
    } else {
      ElMessage.error(data.message)
    }
  })
}

const refreshShares = () => {
  loadShares(page.value)
}

const refreshStorages = () => {
  loadStorages(page1.value)
}

const handleSizeChange = (value: number) => {
  size.value = value
  page.value = 1
  axios.get('/api/shares?page=' + (page.value - 1) + '&size=' + size.value).then(({data}) => {
    shares.value = data.content
    total.value = data.totalElements
  })
}

const handleSize1Change = (value: number) => {
  size1.value = value
  loadStorages(1)
}

const reload = () => {
  axios.post('/api/tacit0924').then(() => {
    ElMessage.success('更新成功')
    loadShares(page.value)
  })
}

const submitUpload = () => {
  uploading.value = true
  axios.post('/api/import-shares', sharesDto.value).then(({data}) => {
    uploadSuccess(data)
  }, (err) => {
    uploadError(err)
  })
}

const exportShares = () => {
  window.location.href = '/api/export-shares?type=' + form.value.type + '&t=' + new Date().getTime() + '&X-ACCESS-TOKEN=' + localStorage.getItem("token");
}

const uploadSuccess = (response: any) => {
  uploading.value = false
  uploadVisible.value = false
  sharesDto.value.content = ''
  loadShares(page.value)
  ElMessage.success('成功导入' + response + '个分享')
}

const uploadError = (error: Error) => {
  uploading.value = false
  ElMessage.error('导入失败：' + error)
}

const handleSelectionChange = (val: ShareInfo[]) => {
  multipleSelection.value = val
}

const handleSelectionStorages = (val: Storage[]) => {
  selectedStorages.value = val
}

onMounted(() => {
  loadShares(page.value)
  loadStorages(page1.value)
})
</script>

<style scoped>
.space {
  margin: 12px 0;
}
</style>
