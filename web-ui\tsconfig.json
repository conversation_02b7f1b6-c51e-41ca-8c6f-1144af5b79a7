{"extends": "@vue/tsconfig/tsconfig.web.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "compilerOptions": {"target": "ES2015", "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["ES2015", "ES2016", "ES2017", "DOM", "DOM.Iterable"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "references": [{"path": "./tsconfig.config.json"}]}